package com.stt.android.remote.usersettings

import javax.inject.Inject

class FcmTokenRegistrationRemoteApi
@Inject constructor(
    private val fcmTokenRegistrationRestApi: FcmTokenRegistrationRestApi
) {
    suspend fun register(token: String, pushApp: String) {
        fcmTokenRegistrationRestApi.register(getFcmTokenInfo(token, pushApp))
    }

    suspend fun unregister(token: String, pushApp: String) {
        fcmTokenRegistrationRestApi.unregister(getFcmTokenInfo(token, pushApp))
    }

    private fun getFcmTokenInfo(token: String, pushApp: String): FcmTokenInfo {
        return FcmTokenInfo(
            token = token,
            app = pushApp
        )
    }
}
