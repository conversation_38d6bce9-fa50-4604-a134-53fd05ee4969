package com.suunto.connectivity.systemevents

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class SuuntoSystemEventsResponse(
    @Json(name = "Samples") val samples: List<SuuntoSystemEventsSample>
)

@JsonClass(generateAdapter = true)
data class SuuntoSystemEventsAnalysisEvent(
    @Json(name = "Header") val header: SuuntoSystemEventsHeader,
    @<PERSON><PERSON>(name = "events") val events: List<SuuntoSystemEventsEvent>
)

@JsonClass(generateAdapter = true)
data class SuuntoSystemEventsSample(
    @Json(name = "Attributes") val attributes: Map<String, SuuntoSystemEventsAnalysisEvent>,
    @Json(name = "Source") val source: String,
    @Json(name = "TimeISO8601") val timeISO8601: String
)

@JsonClass(generateAdapter = true)
data class SuuntoSystemEventsHeader(
    @Json(name = "AppVersion") val appVersion: String,
    @Json(name = "DeviceId") val deviceId: String,
    @Json(name = "DeviceType") val deviceType: String,
    @Json(name = "UserId") val userId: String
)

@JsonClass(generateAdapter = true)
data class SuuntoSystemEventsEventProperties(
    @Json(name = "description") val description: String,
    @Json(name = "module") val module: String,
    @Json(name = "severity") val severity: String
)

@JsonClass(generateAdapter = true)
data class SuuntoSystemEventsEvent(
    @Json(name = "eventId") val eventId: Long,
    @Json(name = "eventProperties") val eventProperties: SuuntoSystemEventsEventProperties,
    @Json(name = "eventType") val eventType: String,
    @Json(name = "timeISO8601") val timeISO8601: String
)
