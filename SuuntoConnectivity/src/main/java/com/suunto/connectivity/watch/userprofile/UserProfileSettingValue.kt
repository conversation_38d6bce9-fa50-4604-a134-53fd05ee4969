package com.suunto.connectivity.watch.userprofile

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.suunto.connectivity.settings.Gender
import kotlinx.parcelize.Parcelize

@JsonClass(generateAdapter = true)
@Parcelize
data class UserProfileGenderItem(
    @Json(name = "gender")
    val gender: <PERSON><PERSON><PERSON>,
    @<PERSON><PERSON>(name = "timestamp")
    val timestamp: Int
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class UserProfileBirthYearItem(
    @Json(name = "birthYear")
    val birthYear: Int,
    @Json(name = "timestamp")
    val timestamp: Int
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class UserProfileWeightItem(
    @Json(name = "weight")
    val weight: Float,
    @Json(name = "timestamp")
    val timestamp: Int
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class UserProfileHeightItem(
    @Json(name = "height")
    val height: Float,
    @Json(name = "timestamp")
    val timestamp: Int
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class UserProfileMaxHeartItem(
    @Json(name = "maxHR")
    val maxHR: Int,
    @Json(name = "timestamp")
    val timestamp: Int
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class UserProfileRestHeartItem(
    @Json(name = "restHR")
    val restHR: Int,
    @Json(name = "timestamp")
    val timestamp: Int
) : Parcelable

const val GENDER_MALE = "male"
const val GENDER_FEMALE = "female"

fun String.wbValue(): Gender? {
    return when (this.lowercase()) {
        GENDER_MALE -> Gender.MALE
        GENDER_FEMALE -> Gender.FEMALE
        else -> null
    }
}
