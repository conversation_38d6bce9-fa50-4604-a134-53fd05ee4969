package com.suunto.connectivity.sportmodes

import com.suunto.connectivity.Spartan
import com.suunto.connectivity.repository.commands.SportModePart
import rx.Completable
import rx.Single

class SportModesSpartanWrapper
constructor(
    val spartan: Spartan
) {
    fun fetchSportModes(): Single<String> {
        return spartan.suuntoRepositoryClient
            .sportModesConsumer
            .getSportModeObject(spartan, 0, SportModePart.GROUP)
    }

    fun fetchSportModeDisplays(id: Int): Single<String> {
        return spartan.suuntoRepositoryClient
            .sportModesConsumer
            .getSportModeObject(spartan, id, SportModePart.DISPLAY)
    }

    fun fetchSportModeSettings(id: Int): Single<String> {
        return spartan.suuntoRepositoryClient
            .sportModesConsumer
            .getSportModeObject(spartan, id, SportModePart.SETTING)
    }

    fun setSportModeDisplays(id: Int, json: String): Completable {
        return spartan.suuntoRepositoryClient
            .sportModesConsumer
            .setSportModeObject(spartan, id, json, SportModePart.DISPLAY)
    }

    fun setSportModeSettings(id: Int, json: String): Completable {
        return spartan.suuntoRepositoryClient
            .sportModesConsumer
            .setSportModeObject(spartan, id, json, SportModePart.SETTING)
    }

    fun setSportModeGroups(id: Int, json: String): Completable {
        return spartan.suuntoRepositoryClient
            .sportModesConsumer
            .setSportModeObject(spartan, id, json, SportModePart.GROUP)
    }

    fun deleteSportModeGroup(id: Int): Completable {
        return spartan.suuntoRepositoryClient
            .sportModesConsumer
            .deleteSportMode(spartan, id)
    }

    fun fetchSportMode(modeId: Int): Single<String> {
        return spartan.suuntoRepositoryClient
            .sportModesConsumer
            .getSportModeObject(spartan, modeId, SportModePart.GROUP)
    }
}
