package com.suunto.connectivity.repository.stateMachines.connectionStateMachine;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Pair;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.analytics.AnalyticsEvent;
import com.suunto.connectivity.hooks.OnDeviceConnectedHook;
import com.suunto.connectivity.repository.LogbookEntrySyncResult;
import com.suunto.connectivity.repository.SingleLogbookEntrySyncResultEvent;
import com.suunto.connectivity.repository.commands.SyncDeviceResponse;
import com.suunto.connectivity.repository.commands.SyncTrainingZoneResponse;
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo;
import com.suunto.connectivity.sync.AlreadySynchronizingException;
import com.suunto.connectivity.sync.SyncResultService;
import com.suunto.connectivity.sync.SyncState;
import com.suunto.connectivity.sync.WatchNotConnectedException;
import com.suunto.connectivity.sync.WatchSynchronizer;
import com.suunto.connectivity.util.SupportedDevices;
import com.suunto.connectivity.watch.SpartanSyncResult;
import com.suunto.connectivity.watch.SynchronizationAnalytics;
import com.suunto.connectivity.watch.WatchBt;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import rx.Completable;
import rx.Observable;
import rx.Single;
import rx.Subscription;
import rx.subjects.BehaviorSubject;
import rx.subscriptions.CompositeSubscription;
import timber.log.Timber;

/**
 * Sync logic is responsible to:
 * - Start autosync when device reports unsynced workouts.
 * - Monitor and report device busy state.
 * - Perform manual sync.
 * - Send sync result to application process.
 */
public class SyncLogic implements WatchSynchronizer.WatchSynchronizerListener {
    private final WatchBt watchBt;
    private final Context context;
    private final SupportedDevices supportedDevices;
    private final OnDeviceConnectedHook onDeviceConnectedHook;
    private Subscription unsyncedMovesSubscription;
    private final CompositeSubscription autoSyncSubscriptions = new CompositeSubscription();
    private AtomicBoolean autoSyncPending = new AtomicBoolean(false);
    private final AtomicBoolean trainingZoneAutoSyncPending = new AtomicBoolean(false);
    public final static int MAX_SUBCSRIPTION_RETRIES = 3;
    public final static int RETRY_DELAY_IN_SECONDS = 2;
    private volatile boolean foreground = false;

    /**
     * Delayed autosync variables.
     */
    private final static int MINIMUM_TIME_BETWEEN_AUTOSYNCS_MINUTES = 5;
    private final static int MINIMUM_AUTOSYNC_DELAY_SECONDS = 10;
    private final Handler delayedAutoSyncHandler = new Handler(Looper.getMainLooper());
    private final boolean delayAutosync;
    private volatile long lastAutosyncTime = 0;
    private volatile boolean reportIntialConnectSync;

    @Nullable
    private volatile Runnable autoSyncRunnable;
    private boolean wasForegroundSync;
    private BehaviorSubject<Boolean> busyObservableSubscribed = BehaviorSubject.create(false);

    @Nullable
    private Subscription trainingZoneSyncSubscription;

    /**
     * Exception thrown when attempt to syncNow and device is not ready for sync.
     */
    public static class DeviceNotConnectedOrBusy extends Exception {
    }

    /**
     * Constructor.
     *
     * @param watchBt           Device to be synced with.
     * @param context           Context.
     * @param supportedDevices  Supported devices provider.
     * @param delayAutosync     If true, autosync start will be delayed until
     *                          MINIMUM_TIME_BETWEEN_AUTOSYNCS_MINUTES
     *                          has passed from the previous autosync.
     */
    public SyncLogic(
        @NonNull WatchBt watchBt,
        Context context,
        SupportedDevices supportedDevices,
        boolean delayAutosync,
        OnDeviceConnectedHook onDeviceConnectedHook
    ) {
        this.watchBt = watchBt;
        this.context = context;
        this.supportedDevices = supportedDevices;
        this.delayAutosync = delayAutosync;
        this.onDeviceConnectedHook = onDeviceConnectedHook;
        watchBt.getWatchSynchronizer().setWatchSynchronizerListener(this);
    }

    /**
     * Device connected. Starts monitoring device state autosyncs when needed.
     */
    public void onConnected(boolean initialConnect) {
        Timber.v("SyncLogic onConnected");
        reportIntialConnectSync = initialConnect;
        // Just in case subscription is still there
        unsubscribeUnsyncedMoves();

        MdsDeviceInfo deviceInfo = watchBt.getCurrentState().getDeviceInfo();
        if (deviceInfo != null) {
            onDeviceConnectedHook.onDeviceConnected(
                deviceInfo.getSerial(),
                deviceInfo.getVariant(),
                deviceInfo.getSwVersion(),
                deviceInfo.getHw(),
                deviceInfo.getCapabilities()
            );
            if (!supportedDevices.isSupportedFirmwareVersion(deviceInfo)) {
                Timber.d("Firmware version too old or not available, disabling autosync");
                return;
            }
        }

        // During initial connect we want to let the main process control when the sync begins.
        if (!initialConnect) {
            autoSyncPending.set(true); // Auto-sync after connecting.
        }

        Completable onInitialConnectCompletable;
        if (initialConnect) {
            onInitialConnectCompletable = watchBt.getWatchSynchronizer().onInitialConnectCompletable();
        } else {
            onInitialConnectCompletable = Completable.complete();
        }

        Observable<Pair<Integer, Boolean>> unsyncedMovesObservable = Observable.combineLatest(
            watchBt.getUnsyncedMovesObservable()
                .retryWhen(
                    new RetryWithDelay(MAX_SUBCSRIPTION_RETRIES, RETRY_DELAY_IN_SECONDS))
                .doOnNext(count -> {
                    Timber.v("Unsynced moves: %d", count);
                    // Setting autoSyncPending only here, because it seems that the
                    // count is not reset to zero after sync. Should it?
                    if (count > 0) {
                        autoSyncPending.set(true);
                    }
                }),
            watchBt.getWatchBusyObservable()
                .retryWhen(
                    new RetryWithDelay(MAX_SUBCSRIPTION_RETRIES, RETRY_DELAY_IN_SECONDS))
                .map(busyValue -> busyValue != 0)
                .doOnNext(watchBusy -> {
                    Timber.v("Watch busy state: %s", watchBusy);
                    watchBt.setDeviceBusy(watchBusy);
                    busyObservableSubscribed.onNext(true);
                })
                .doOnError(t -> {
                    Timber.w(t, "Failed to get busy state from watch.");
                    busyObservableSubscribed.onNext(true);
                })
                .onErrorReturn(t -> false),
            Pair::new);

        // Start listening for unsynced moves and watch busy state.
        // Do not start to sync before unsynced moves are properly subscribed.
        // It seems that probably ESW gets confused otherwise.
        unsyncedMovesSubscription =
            onInitialConnectCompletable.andThen(unsyncedMovesObservable)
                .subscribe(pair -> {
                    boolean watchBusy = pair.second;
                    if (!watchBusy && autoSyncPending.getAndSet(false)) {
                        autoSyncDelayed();
                    }
                }, t -> {
                    Timber.e(t, "Failed to get unsynced moves from watch!");
                    // Busy state can not be known.
                    watchBt.setDeviceBusy(false);
                    // Sync anyway.
                    autoSyncDelayed();
                });

        // Legacy watches do not send unsynced move event if there is no new move done after
        // the connection happens. That's why we need to trigger autoSync explicitly here.
        if (watchBt.getSuuntoBtDevice().getDeviceType().isAmbit()) {
            autoSyncDelayed();
        }
    }

    /**
     * Device disconnected. Stop monitoring device.
     */
    public void onDisconnected() {
        Timber.v("SyncLogic onDisconnected");
        // Stop listening for unsynced moves
        unsubscribeUnsyncedMoves();
        delayedAutoSyncHandler.removeCallbacksAndMessages(null);
    }

    public void requestAutoSync() {
        // at next WatchState update the sync will be triggered
        autoSyncPending.set(true);
    }

    /**
     * Autosync after required delays.
     */
    private synchronized void autoSyncDelayed() {
        if (!delayAutosync) {
            // Delayed autosync not active. Sync now.
            autoSync();
            return;
        }
        if (autoSyncRunnable != null) {
            // Autosync already active.
            return;
        }
        final long minimumDelay = TimeUnit.SECONDS.toMillis(MINIMUM_AUTOSYNC_DELAY_SECONDS);
        final long delay;
        if (lastAutosyncTime == 0) {
            delay = minimumDelay;
        } else {
            final long minimumTimeBetweenAutosyncs =
                TimeUnit.MINUTES.toMillis(MINIMUM_TIME_BETWEEN_AUTOSYNCS_MINUTES);
            long timeToNextAutosync =
                minimumTimeBetweenAutosyncs - (System.currentTimeMillis() - lastAutosyncTime);
            delay =
                Math.min(Math.max(timeToNextAutosync, minimumDelay), minimumTimeBetweenAutosyncs);
        }

        autoSyncRunnable = this::autoSync;
        delayedAutoSyncHandler.postDelayed(autoSyncRunnable, delay);
    }

    private synchronized void autosyncReady(@Nullable Throwable throwable) {
        if (reportIntialConnectSync &&
            !(throwable instanceof AlreadySynchronizingException) &&
            !(throwable instanceof WatchNotConnectedException)) {
            reportIntialConnectSync = false;
        }
        lastAutosyncTime = System.currentTimeMillis();
        autoSyncRunnable = null;
        autoSyncSubscriptions.clear();
    }

    private void autoSync() {
        wasForegroundSync = foreground;

        if (supportedDevices.requiresForcedUpdate(watchBt)) {
            Timber.i("Skipping auto sync due to ongoing forced update.");
            return;
        }

        WatchSynchronizer synchronizer = watchBt.getWatchSynchronizer();
        if (synchronizer.isSynchronizing()) {
            // Early exit if sync is ongoing already. Trying to sync here would eventually fail
            // with AlreadySynchronizingException in that case, but let's try to avoid going through
            // so many steps into an almost certain failure.
            Timber.d("Skipping auto sync due to ongoing sync. Foreground: %s", wasForegroundSync);
            return;
        } else {
            Timber.v("Initiating auto sync. Foreground: %s", wasForegroundSync);
        }

        SynchronizationAnalytics analytics = new SynchronizationAnalytics(watchBt, context);
        autoSyncSubscriptions.add(synchronizer.synchronize(false, foreground)
            .subscribe(syncResult -> {
                boolean newWorkouts = false;
                for (LogbookEntrySyncResult logbookResult : syncResult.getLogbookResult()
                    .getLogbookEntriesResult()) {
                    if (logbookResult.getSummaryResult().isSuccess()
                        && logbookResult.getSamplesResult().isSuccess()) {
                        newWorkouts = true;
                        break;
                    }
                }
                if (wasForegroundSync ||
                    newWorkouts ||
                    syncResult.getSystemEventsResult().getSyncResult().isSuccess()) {
                    // Send sync results to analytics and application process.
                    sendSpartanSyncResult(syncResult, syncMethod(wasForegroundSync), analytics);
                } else {
                    // Send sync results to analytics.
                    analytics.sendSyncResult(syncMethod(wasForegroundSync), syncResult);
                }
                autosyncReady(null);
            }, t -> {
                if (t instanceof AlreadySynchronizingException) {
                    Timber.v("Not auto syncing as sync is already ongoing");
                } else {
                    analytics.sendSyncResultError(syncMethod(wasForegroundSync), t);
                    Timber.w(t, "Failed to auto synchronize Device!");
                    autosyncReady(t);
                }
            }));
    }

    private String syncMethod(boolean foreground) {
        return foreground ? AnalyticsEvent.SUUNTO_SYNC_FOREGROUND_METHOD
            : AnalyticsEvent.SUUNTO_SYNC_BACKGROUND_METHOD;
    }

    private synchronized void unsubscribeUnsyncedMoves() {
        busyObservableSubscribed.onNext(false);
        if (unsyncedMovesSubscription != null) {
            // Busy state can not be known.
            watchBt.setDeviceBusy(false);
            unsyncedMovesSubscription.unsubscribe();
            unsyncedMovesSubscription = null;
        }
    }

    void onDestroy() {
        unsubscribeUnsyncedMoves();
        autoSyncSubscriptions.clear();
        delayedAutoSyncHandler.removeCallbacksAndMessages(null);
        watchBt.getWatchSynchronizer().removeWatchSynchronizerListener();
        if (trainingZoneSyncSubscription != null) {
            trainingZoneSyncSubscription.unsubscribe();
        }
    }

    private void sendSpartanSyncResult(
        SpartanSyncResult spartanSyncResult,
        String syncMethod,
        @Nullable SynchronizationAnalytics analytics) {
        SyncResultService.sendSpartanSyncResult(
            spartanSyncResult,
            syncMethod,
            analytics,
            context);
    }

    Single<SyncDeviceResponse> syncNow(boolean is247Only) {
        Timber.v("Initiating sync now");
        SynchronizationAnalytics analytics = new SynchronizationAnalytics(watchBt, context);
        return busyObservableSubscribed
            .filter(ready -> ready)
            .first()
            .flatMapCompletable(aBoolean -> {
                if (watchBt.getCurrentState().isDeviceBusy() || !watchBt.getCurrentState()
                    .isConnected()) {
                    return Completable.error(new DeviceNotConnectedOrBusy());
                } else {
                    return Completable.complete();
                }
            })
            .toCompletable()
            .andThen(
                watchBt.getWatchSynchronizer().synchronize(is247Only, foreground)
                    .flatMap(spartanSyncResult -> {
                        sendSpartanSyncResult(spartanSyncResult,
                            AnalyticsEvent.SUUNTO_SYNC_MANUAL_METHOD,
                            analytics);
                        return Single.just(spartanSyncResult);
                    })
                    .map(SyncDeviceResponse::new)
                    .doOnError(t -> {
                        if (t instanceof AlreadySynchronizingException) {
                            Timber.v("Not syncing as sync is already ongoing");
                        } else {
                            analytics.sendSyncResultError(AnalyticsEvent.SUUNTO_SYNC_MANUAL_METHOD,
                                t);
                            Timber.w(t, "Failed to synchronize Spartan!");
                        }
                    }));
    }

    SyncTrainingZoneResponse requestTrainingZoneSync() {
        Timber.v("Initiating sync training zone");
        if (!watchBt.getCurrentState().isConnected()) {
            return new SyncTrainingZoneResponse(false, "Watch is not connected");
        }
        // if we request syncing training zone and we already syncing things before it, we just don't do anything
        if (watchBt.getWatchSynchronizer().isSynchronizing() && watchBt.getCurrentState().getSyncState().getState() < SyncState.SYNCING_TRAINING_ZONE) {
            return new SyncTrainingZoneResponse(false, "Watch is already syncing and has not synced training zone yet");
        }
        startTrainingZoneSync();

        return new SyncTrainingZoneResponse(true);
    }

    private void startTrainingZoneSync() {
        // if the training zone is syncing, we need to re-sync again
        if (trainingZoneSyncSubscription != null && !trainingZoneSyncSubscription.isUnsubscribed()) {
            Timber.v("Training zone is already syncing, mark the sync as pending to re-sync again");
            trainingZoneAutoSyncPending.set(true);
            return;
        }

        trainingZoneSyncSubscription = watchBt.getWatchSynchronizer().synchronizeTrainingZone()
            .doOnUnsubscribe(() -> {
                if (trainingZoneAutoSyncPending.compareAndSet(true, false)) {
                    requestTrainingZoneSync();
                }
            })
            .subscribe(
                ignore -> {},
                error -> {
                    if (error instanceof AlreadySynchronizingException) {
                        Timber.v("Not syncing training zone as sync is already ongoing");
                    } else {
                        Timber.w(error, "Failed to synchronize training zone to Spartan!");
                    }
                }
            );
    }

    void handleReportAppProcessForeground(boolean foreground) {
        boolean wasForeground = this.foreground;
        this.foreground = foreground;
        if (foreground && !wasForeground) {
            if (watchBt.getCurrentState().isConnected() && !watchBt.getCurrentState()
                .isDeviceBusy()) {
                autoSync();
            }
        }
    }

    @Override
    public void onSingleLogbookEntrySynced(SingleLogbookEntrySyncResultEvent event) {
        if (wasForegroundSync) {
            sendSpartanSyncResult(event.getSpartanSyncResult(),
                "", null);
        }
    }
}
