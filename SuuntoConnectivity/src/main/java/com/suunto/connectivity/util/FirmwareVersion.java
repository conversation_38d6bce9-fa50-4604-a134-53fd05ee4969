package com.suunto.connectivity.util;

import androidx.annotation.NonNull;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Firmware version class.
 */
public class FirmwareVersion implements Comparable<FirmwareVersion> {

    public static final FirmwareVersion SMART_SENSOR_MEM_FUNCTIONALITY =
            new FirmwareVersion("1.1.0");

    public static final FirmwareVersion WORKOUTS =
            new FirmwareVersion("1.5.0");

    public static final FirmwareVersion ASIAN_LANGUAGE =
            new FirmwareVersion("1.5.0");

    public static final FirmwareVersion EXTENDED_CHARACTER_SET =
            new FirmwareVersion("1.6.8");

    public static final FirmwareVersion CYRILLIC_LANGUAGE =
            new FirmwareVersion("2.0.4");

    public static final FirmwareVersion FIRSTBEAT_SLEEP_THRESHOLD =
            new FirmwareVersion("2.11.10");

    private final int major;
    private final int minor;
    private final int revision;
    private final int beta;

    public FirmwareVersion(int major, int minor, int revision) {
        this(major, minor, revision, Integer.MAX_VALUE);
    }

    public FirmwareVersion(int major, int minor, int revision, int beta) {
        this.major = major;
        this.minor = minor;
        this.revision = revision;
        this.beta = beta;
    }

    /**
     * Constructs a firmware version from a string.
     *
     * The version string must have three numbers (major, minor, revision)
     * separated with a dot. The version string may contain an optional beta
     * suffix with or without a number.
     *
     * Examples of a valid version string:
     *  1.1.0
     *  1.2.3beta
     *  2.12.2beta2
     *
     * @param version String representation of the version
     */
    public FirmwareVersion(@NonNull String version) {
        Matcher m = Pattern.compile("(\\d+)\\.(\\d+)\\.(\\d+)(beta(\\d*))?")
                .matcher(version);
        if (m.matches()) {
            major = Integer.parseInt(m.group(1));
            minor = Integer.parseInt(m.group(2));
            revision = Integer.parseInt(m.group(3));
            if (m.group(4) != null) { // has beta suffix
                if (!m.group(5).isEmpty()) { // has beta number
                    beta = Integer.parseInt(m.group(5));
                } else {
                    beta = 1;
                }
            } else {
                beta = Integer.MAX_VALUE;
            }
        } else {
            throw new IllegalArgumentException("Malformed firmware version: " + version);
        }
    }

    /**
     * Constructs a firmware version from a string.
     *
     * @param versionString String representation of the version
     * @return Firmware version object
     */
    public static FirmwareVersion fromString(String versionString) {
        FirmwareVersion version = null;

        if (versionString != null) {
            try {
                version = new FirmwareVersion(versionString);
            } catch (IllegalArgumentException ex) {
                // No action
            }
        }

        return version;
    }

    /**
     * Movescount Backend always sends the version string as four numbers (major, minor, revision,
     * build number). We need to strip Build number to instantiate {@link FirmwareVersion}.
     *
     * @param version String representation of the version
     * @return Firmware version object
     */
    public static FirmwareVersion stripBuildNumberFromFw(String version) {
        Matcher m = Pattern.compile("(\\d+)\\.(\\d+)\\.(\\d+).(\\d+)")
            .matcher(version);
        if (m.matches()) {
            return new FirmwareVersion(Integer.parseInt(m.group(1)), Integer.parseInt(m.group(2)),
                Integer.parseInt(m.group(3)));
        } else {
            throw new IllegalArgumentException("Malformed firmware version: " + version);
        }
    }

    boolean isNewerThanOrEqualToReference(FirmwareVersion version) {
        return compareTo(version) >= 0;
    }

    private boolean isNewerThanReference(FirmwareVersion version) {
        return compareTo(version) > 0;
    }

    public static boolean isNewerThanOrEqualToReference(String version, FirmwareVersion referenceVersion) {
        FirmwareVersion fwVersion = FirmwareVersion.fromString(version);
        return fwVersion != null && fwVersion.isNewerThanOrEqualToReference(referenceVersion);
    }

    public static boolean isNewerThanReference(String version, FirmwareVersion referenceVersion) {
        FirmwareVersion fwVersion = FirmwareVersion.fromString(version);
        return fwVersion != null && fwVersion.isNewerThanReference(referenceVersion);
    }

    private boolean isOlderThanReference(FirmwareVersion version) {
        return compareTo(version) < 0;
    }

    public static boolean isOlderThanReference(String version, FirmwareVersion referenceVersion) {
        FirmwareVersion fwVersion = FirmwareVersion.fromString(version);
        return fwVersion != null && fwVersion.isOlderThanReference(referenceVersion);
    }

    @Override
    public int compareTo(FirmwareVersion o) {
        if (major != o.major) {
            return Integer.valueOf(major).compareTo(o.major);
        } else if (minor != o.minor) {
            return Integer.valueOf(minor).compareTo(o.minor);
        } else if (revision != o.revision) {
            return Integer.valueOf(revision).compareTo(o.revision);
        } else if (beta != o.beta) {
            return Integer.valueOf(beta).compareTo(o.beta);
        } else {
            return 0;
        }
    }

    public int getMajor() {
        return major;
    }

    public int getMinor() {
        return minor;
    }

    public int getRevision() {
        return revision;
    }

    public int getBeta() {
        return beta;
    }

    public boolean isBeta() {
        return beta != Integer.MAX_VALUE;
    }

    @Override
    public String toString() {
        return major + "." + minor + "." + revision +
                (isBeta() ? "beta" + (beta > 1 ? beta : "") : "");
    }
}
