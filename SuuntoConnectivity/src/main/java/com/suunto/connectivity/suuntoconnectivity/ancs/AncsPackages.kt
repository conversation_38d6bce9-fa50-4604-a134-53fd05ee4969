package com.suunto.connectivity.suuntoconnectivity.ancs

import android.app.Notification
import androidx.core.app.NotificationCompat
import com.suunto.connectivity.suuntoconnectivity.ancs.AncsConstants.CategoryID

/**
 * Maps package names to notification categories for ANCS.
 */
internal object AncsPackages {
    const val CALL_PACKAGE: String = "com.android.phone"

    private const val ID_MISSED_CALL = 1

    // Package names for known phone packages.
    private val PHONE_PACKAGES = setOf(
        "com.android.phone",
        "com.google.android.dialer",
        "com.sonymobile.android.dialer",
    )

    // Package names for known system spammers
    private val SYSTEM_PACKAGES = setOf(
        "com.android.providers.downloads",
        "com.sec.android.providers.downloads",
        "com.android.vending",
        "com.android.systemui",
        "com.sonyericsson.updatecenter",
        "com.wssyncmldm",
        "com.samsung.android.themestore",
        "android",
    )

    // Package names for known apps posting incoming call notifications
    private val INCOMING_CALL_PACKAGES = setOf(
        "com.android.dialer",
        "com.android.incallui",
        "com.asus.asusincallui",
        "com.samsung.android.incallui",
        "com.samsung.android.dialer",
    )

    private val MISSED_CALL_PACKAGES = setOf(
        "com.android.server.telecom",
    )

    // Package names for SMS
    private val MESSAGE_PACKAGES = setOf(
        "com.sonyericsson.conversations",
        "com.android.mms",
        "com.htc.sense.mms",
        "com.pantech.app.mms",
        "com.asus.message",
        "com.android.contacts",
        "com.samsung.android.messaging",
        "com.google.android.talk",  // Nexus 5 SMS
        "com.google.android.gm",  // Gmail
        "ch.protonmail.android", // Proton Mail
        "com.suunto.suuntoandroidtest", // For notification testing
    )

    private val CALL_PACKAGES = PHONE_PACKAGES + INCOMING_CALL_PACKAGES + MISSED_CALL_PACKAGES

    private val PACKAGE_CATEGORIES = mapOf(
        SYSTEM_PACKAGES to CategoryID.SYSTEM,
        INCOMING_CALL_PACKAGES to CategoryID.INCOMING_CALL,
        MISSED_CALL_PACKAGES to CategoryID.MISSED_CALL,
        MESSAGE_PACKAGES to CategoryID.EMAIL,
    )

    private val NOTIFICATION_CATEGORY = mapOf(
        NotificationCompat.CATEGORY_EMAIL to CategoryID.EMAIL,
        NotificationCompat.CATEGORY_EVENT to CategoryID.SCHEDULE,
        NotificationCompat.CATEGORY_LOCATION_SHARING to CategoryID.LOCATION,
        NotificationCompat.CATEGORY_MESSAGE to CategoryID.EMAIL,
        NotificationCompat.CATEGORY_REMINDER to CategoryID.SCHEDULE,
        NotificationCompat.CATEGORY_SOCIAL to CategoryID.SOCIAL,
        NotificationCompat.CATEGORY_WORKOUT to CategoryID.HEALTH_AND_FITNESS,
    )

    /**
     * Gets notification category based on id, package name and category given by Android
     *
     * @param notificationId Id from notification
     * @param packageName Package name from notification
     * @param category Category from the original notification from Android
     * @return Category value defined in [CategoryID]
     */
    @AncsConstants.Category
    fun getCategory(notificationId: Int, packageName: String, category: String?): Int {
        if (PHONE_PACKAGES.contains(packageName)) {
            return if (Notification.CATEGORY_CALL == category ||
                (category == null && notificationId != ID_MISSED_CALL)
            ) {
                CategoryID.INCOMING_CALL
            } else {
                CategoryID.MISSED_CALL
            }
        }

        PACKAGE_CATEGORIES.firstNotNullOfOrNull { (packages, categoryId) ->
            if (packages.contains(packageName)) {
                categoryId
            } else {
                null
            }
        }?.let { return it }

        category?.let(NOTIFICATION_CATEGORY::get)
            ?.let { return it }

        return CategoryID.OTHER
    }

    fun isCallPackage(packageName: String): Boolean = CALL_PACKAGES.contains(packageName)

    fun isSmsPackage(packageName: String): Boolean = MESSAGE_PACKAGES.contains(packageName)

    /**
     * Check whether it is an alarm clock application by the last paragraph of the package name.
     * At present, the package name of the alarm clock application basically contains "clock", but maybe there are exceptions.
     *
     * @param packageName Package name from notification
     * @return If true, it is an clock notification
     */
    fun isClockPackage(packageName: String): Boolean {
        // Some system packages might not have '.' in their names, e.g. "android".
        val index = packageName.lastIndexOf(".")
        return index >= 0 && packageName.substring(index).contains("clock")
    }
}
