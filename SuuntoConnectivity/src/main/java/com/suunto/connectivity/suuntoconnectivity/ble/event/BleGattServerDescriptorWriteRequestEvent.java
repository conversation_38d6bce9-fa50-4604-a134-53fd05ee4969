package com.suunto.connectivity.suuntoconnectivity.ble.event;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGattDescriptor;

public class BleGattServerDescriptorWriteRequestEvent extends BleGattServerEvent {

    private final int requestId;
    private final BluetoothGattDescriptor descriptor;
    private final boolean preparedWrite;
    private final boolean responseNeeded;
    private final int offset;
    private final byte[] value;

    public BleGattServerDescriptorWriteRequestEvent(BluetoothDevice bluetoothDevice, int requestId,
                                                    BluetoothGattDescriptor descriptor,
                                                    boolean preparedWrite, boolean responseNeeded,
                                                    int offset, byte[] value) {
        super(bluetoothDevice);
        this.requestId = requestId;
        this.descriptor = descriptor;
        this.preparedWrite = preparedWrite;
        this.responseNeeded = responseNeeded;
        this.offset = offset;
        this.value = value;
    }

    public int getRequestId() {
        return requestId;
    }

    public BluetoothGattDescriptor getDescriptor() {
        return descriptor;
    }

    public boolean isPreparedWrite() {
        return preparedWrite;
    }

    public boolean isResponseNeeded() {
        return responseNeeded;
    }

    public int getOffset() {
        return offset;
    }

    public byte[] getValue() {
        return value;
    }
}
