package com.suunto.connectivity.suuntoconnectivity.ble.operation;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattServer;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattServerEvent;
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattServerNotificationSentEvent;
import com.suunto.connectivity.suuntoconnectivity.ble.exception.GattServerNotifyCharacteristicChangedException;

/**
 * Operation for notifying a device that GATT server characteristic has changed.
 */
public class BleGattServerOperationNotifyCharacteristicChanged
        extends BleGattServerOperation<Integer> {

    private static final int MAX_AUTO_COMPLETE_TIMEOUT_MS = 2048;
    private static final int MIN_AUTO_COMPLETE_TIMEOUT_MS = 8;

    private final BluetoothGattCharacteristic characteristic;
    private final boolean confirm;
    private final byte[] value;

    // All devices before API level 21 and also some devices with API level 21 and above,
    // for example Huawei Y5 II with Android 5.1 (API level 22),
    // don't send notification sent callbacks at all. The value of this static
    // boolean variable will be true, if at least one callback is ever received.
    volatile static boolean notificationSentCallbackSupported;

    // Current auto complete timeout in milliseconds.
    private volatile static int autoCompleteTimeout = MAX_AUTO_COMPLETE_TIMEOUT_MS;

    private final Handler handler;

    /**
     * Constructor.
     *
     * @param gattServer     GATT server
     * @param device         Device to notify
     * @param characteristic Characteristic that has changed
     * @param confirm        true to request confirmation from the client (indication),
     *                       false to send a notification
     * @param value          Value for the characteristic. The characteristic is updated
     *                       with this value when the operation runs. The value must not
     *                       be modified before the operation completes.
     */
    public BleGattServerOperationNotifyCharacteristicChanged(
            @NonNull BluetoothGattServer gattServer, @NonNull BluetoothDevice device,
            @NonNull BluetoothGattCharacteristic characteristic, boolean confirm,
            byte[] value) {
        this(gattServer, device, characteristic, confirm, value, new Handler(Looper.getMainLooper()));
    }

    BleGattServerOperationNotifyCharacteristicChanged(
        @NonNull BluetoothGattServer gattServer, @NonNull BluetoothDevice device,
        @NonNull BluetoothGattCharacteristic characteristic, boolean confirm,
        byte[] value, Handler handler) {
        super(gattServer, device);

        this.characteristic = characteristic;
        this.confirm = confirm;
        this.value = value;
        this.handler = handler;
    }

    @SuppressLint("MissingPermission")
    @Override
    protected void protectedRun() throws Throwable {
        super.protectedRun();

        if (value != null) {
            characteristic.setValue(value);
        }

        if (!bluetoothGattServer.notifyCharacteristicChanged(
                bluetoothDevice, characteristic, confirm)) {
            onError(new GattServerNotifyCharacteristicChangedException(BluetoothGatt.GATT_FAILURE));
        }
        autoCompleteIfNeeded();
    }

    @Nullable
    @Override
    protected Exception customTimeoutException() {
        return new GattServerNotifyCharacteristicChangedException(OPERATION_TIMEOUT);
    }

    /**
     * Auto complete operation after timeout if android device is not
     * sending callbacks on GattServer.notifyCharacteristicChanged.
     */
    private void autoCompleteIfNeeded() {
        if (!notificationSentCallbackSupported) {
            final int waitTime;
            waitTime = autoCompleteTimeout;
            if (autoCompleteTimeout != MIN_AUTO_COMPLETE_TIMEOUT_MS) {
                autoCompleteTimeout = Math.max(MIN_AUTO_COMPLETE_TIMEOUT_MS, autoCompleteTimeout / 2);
            }
            handler.postDelayed(() -> onCompleted(BluetoothGatt.GATT_SUCCESS), waitTime);
        }
    }

    @Override
    protected void handleBleGattServerEvent(BleGattServerEvent event) {
        super.handleBleGattServerEvent(event);

        if (event instanceof BleGattServerNotificationSentEvent) {
            notificationSentCallbackSupported = true;
            if (event.getStatus() == BluetoothGatt.GATT_SUCCESS) {
                onCompleted(event.getStatus());
            } else {
                onError(new GattServerNotifyCharacteristicChangedException(event.getStatus()));
            }
        }
    }
}
