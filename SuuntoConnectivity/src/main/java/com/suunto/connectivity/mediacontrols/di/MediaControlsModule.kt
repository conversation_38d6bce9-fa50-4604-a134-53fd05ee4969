package com.suunto.connectivity.mediacontrols.di

import android.content.Context
import android.media.session.MediaSessionManager
import com.stt.android.di.ConnectivityModule
import com.suunto.connectivity.mediacontrols.datasource.VolumeAndroidDataSource
import com.suunto.connectivity.mediacontrols.datasource.VolumeDataSource
import dagger.Binds
import dagger.Module
import dagger.Provides

@Module
@ConnectivityModule
abstract class MediaControlsModule {

    @Binds
    abstract fun bindVolumeDataSource(dataSource: VolumeAndroidDataSource): VolumeDataSource

    companion object {
        @Provides
        fun provideMediaSessionManager(context: Context): MediaSessionManager {
            return context.getSystemService(Context.MEDIA_SESSION_SERVICE) as MediaSessionManager
        }
    }
}
