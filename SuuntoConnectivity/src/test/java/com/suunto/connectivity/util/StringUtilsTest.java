package com.suunto.connectivity.util;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

/**
 * Tests for StringUtils class
 */
public class StringUtilsTest {

    @Test
    public void mayConcatenateZeroStrings() {
        assertEquals("", StringUtils.concatNonEmpty(","));
    }

    @Test
    public void mayConcatenateOneString() {
        assertEquals("test", StringUtils.concatNonEmpty(",", "test"));
    }

    @Test
    public void mayConcatenateMultipleStrings() {
        assertEquals("abc,def", StringUtils.concatNonEmpty(",", "", "abc", null, "def", null, ""));
    }

    @Test
    public void mayConcatWithLongDelim() {
        assertEquals("testcase", StringUtils.concatNonEmpty("stca", null, "te", "se"));
    }

}
