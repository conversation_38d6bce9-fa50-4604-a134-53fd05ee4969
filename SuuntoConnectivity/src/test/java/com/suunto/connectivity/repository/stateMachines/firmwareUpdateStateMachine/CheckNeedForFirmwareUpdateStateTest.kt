package com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine

import com.suunto.connectivity.firmware.WatchFirmwareInfo
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class CheckNeedForFirmwareUpdateStateTest {
    @Test
    fun `isDowngrade returns true for downgrade`() {
        val mdsInfo = createMdsDeviceInfo(
            sw = "2.24.2"
        )

        val olderMajorFw = createWatchFwInfo(
            version = "SapporoC-fw_1.24.2.3769-P-b2b7f65b"
        )
        val olderMinorFw = createWatchFwInfo(
            version = "SapporoC-fw_2.23.2.3769-P-b2b7f65b"
        )
        val olderPatchFw = createWatchFwInfo(
            version = "SapporoC-fw_2.24.1.3769-P-b2b7f65b"
        )

        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, olderMajorFw)).isTrue
        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, olderMinorFw)).isTrue
        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, olderPatchFw)).isTrue
    }

    @Test
    fun `isDowngrade returns false for same version`() {
        val mdsInfo = createMdsDeviceInfo(
            sw = "2.24.2"
        )

        val sameFw = createWatchFwInfo(
            version = "Nagano-fw_2.24.2.5897-K-26c4a751"
        )

        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, sameFw)).isFalse
    }

    @Test
    fun `isDowngrade returns false for update`() {
        val mdsInfo = createMdsDeviceInfo(
            sw = "2.24.2"
        )

        val newerMajorFw = createWatchFwInfo(
            version = "Sapporo-fw_3.24.2.3769-P-b2b7f65b"
        )
        val newerMinorFw = createWatchFwInfo(
            version = "Sapporo-fw_2.25.2.3769-P-b2b7f65b"
        )
        val newerPatchFw = createWatchFwInfo(
            version = "Sapporo-fw_2.24.3.3769-P-b2b7f65b"
        )

        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, newerMajorFw)).isFalse
        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, newerMinorFw)).isFalse
        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, newerPatchFw)).isFalse
    }

    @Test
    fun `isDowngrade is not affected by build number after patch`() {
        val mdsInfo = createMdsDeviceInfo(
            sw = "2.24.2"
        )

        val buildZeroNewerPatch = createWatchFwInfo(
            version = "Sapporo-fw_2.24.3.0-P-b2b7f65b"
        )
        val buildOneNewerPatch = createWatchFwInfo(
            version = "Sapporo-fw_2.24.3.1-P-b2b7f65b"
        )
        val buildThousandNewerPatch = createWatchFwInfo(
            version = "Sapporo-fw_2.24.3.1000-P-b2b7f65b"
        )

        val buildZeroSameFw = createWatchFwInfo(
            version = "Sapporo-fw_2.24.2.0-P-b2b7f65b"
        )
        val buildOneSameFw = createWatchFwInfo(
            version = "Sapporo-fw_2.24.2.1-P-b2b7f65b"
        )
        val buildThousandSameFw = createWatchFwInfo(
            version = "Sapporo-fw_2.24.2.1000-P-b2b7f65b"
        )

        val buildZeroOlderPatch = createWatchFwInfo(
            version = "Sapporo-fw_2.24.1.0-P-b2b7f65b"
        )
        val buildOneOlderPatch = createWatchFwInfo(
            version = "Sapporo-fw_2.24.1.1-P-b2b7f65b"
        )
        val buildThousandOlderPatch = createWatchFwInfo(
            version = "Sapporo-fw_2.24.1.1000-P-b2b7f65b"
        )

        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, buildZeroNewerPatch)).isFalse
        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, buildOneNewerPatch)).isFalse
        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, buildThousandNewerPatch)).isFalse

        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, buildZeroSameFw)).isFalse
        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, buildOneSameFw)).isFalse
        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, buildThousandSameFw)).isFalse

        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, buildZeroOlderPatch)).isTrue
        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, buildOneOlderPatch)).isTrue
        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, buildThousandOlderPatch)).isTrue
    }

    @Test
    fun `isDowngrade returns false for invalid versions`() {
        val mdsInfo = createMdsDeviceInfo(
            sw = "2.24.2"
        )

        val fwWithBadVersion = createWatchFwInfo(
            version = "Sapporo-fw_2.corrupted-P-b2b7f65b"
        )

        assertThat(CheckNeedForFirmwareUpdateState.isDowngrade(mdsInfo, fwWithBadVersion)).isFalse
    }

    companion object {
        private fun createMdsDeviceInfo(sw: String) = MdsDeviceInfo(
            swVersion = "",
            productName = "",
            variant = "",
            hwCompatibilityId = "",
            serial = "",
            sw = sw,
            hw = "",
            productVersion = null,
            manufacturerName = "",
            additionalVersionInfoExtension = null,
            description = null,
            capabilities = null
        )

        private fun createWatchFwInfo(version: String) = WatchFirmwareInfo(
            version = "",
            versionLog = "",
            firmwareUploadDate = "",
            latestFirmwareURI = "",
            latestFirmwareVersion = version,
            deviceName = "",
            releaseType = ""
        )
    }
}
