package com.suunto.music.model

import androidx.annotation.StringRes
import com.suunto.music.R

enum class OfflineMusicHelpItems(@StringRes val titleResId: Int, @StringRes val contentResId: Int) {
    GUIDE(R.string.help_guide_title, R.string.help_guide_content),
    STORE_MUSIC(R.string.help_store_music_title, R.string.help_store_music_content),
    COMPATIBILITY(R.string.help_compatibility_title, R.string.help_compatibility_content),
    MANAGEMENT(R.string.help_playlist_management_title, R.string.help_playlist_management_content),
    ATTENTION(R.string.help_attention_title, R.string.help_attention_content)
}
