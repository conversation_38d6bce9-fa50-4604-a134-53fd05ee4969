package com.suunto.scsampleapp;

import android.app.Activity;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.stt.android.logbook.SuuntoLogbookSummary;
import com.suunto.connectivity.logbook.Logbook;
import java.util.List;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.subscriptions.CompositeSubscription;

/**
 * Fragment to show contents of a logbook
 */
public class LogbookFragment extends Fragment {
    private static final String TAG = LogbookFragment.class.getSimpleName();
    private LogbookProvider logbookProvider;
    private LogbookEntryAdapter logbookEntryAdapter;
    private CompositeSubscription subscriptions;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Activity activity = getActivity();
        if (activity instanceof LogbookProvider) {
            logbookProvider = (LogbookProvider) activity;
        } else {
            throw new IllegalStateException(
                "Containing Activity does not implement LogbookProvider");
        }

        subscriptions = new CompositeSubscription();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
        @Nullable Bundle savedInstanceState) {
        final Logbook logbook = logbookProvider.getLogbook();
        if (logbook == null) {
            // No Logbook, go back
            getActivity().onBackPressed();
            return null;
        }

        View view = inflater.inflate(R.layout.fragment_logbook, container, false);

        // List of entries
        RecyclerView entryList = (RecyclerView) view.findViewById(R.id.entry_list);
        entryList.setLayoutManager(new LinearLayoutManager(getContext()));
        logbookEntryAdapter = new LogbookEntryAdapter();
        entryList.setAdapter(logbookEntryAdapter);
        entryList.setItemAnimator(null);

        // TextView to show results
        final TextView contentTextView = (TextView) view.findViewById(R.id.content);

        // Listen for summary selection
        subscriptions.add(logbookEntryAdapter.getSummaryClicks()
            .flatMap(entry -> entry.getSummary().toObservable())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                summary -> contentTextView.setText(summaryToStr(summary.getSummary())),
                throwable -> contentTextView.setText(throwable.getMessage()))
        );

        // Listen for data selection
        subscriptions.add(logbookEntryAdapter.getDataClicks()
            .flatMap(entry -> entry.getSamples().toObservable())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(samples -> {
                if (samples == null || samples.getSamples() == null) {
                    contentTextView.setText("Received null samples");
                } else {
                    contentTextView.setText(
                        "Got " + samples.getSamples().size() + " samples");
                }
            }, throwable -> contentTextView.setText(throwable.getMessage())));

        // Fetch entries and update list with result
        subscriptions.add(logbook.getLogbookEntries()
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(new Action1<List<Logbook.Entry>>() {
                @Override
                public void call(List<Logbook.Entry> entries) {
                    logbookEntryAdapter.setEntries(entries);
                }
            }, throwable -> {
                Log.w(TAG, "Unable to read logbook entries", throwable);
                if(getContext() != null) {
                    Toast.makeText(getContext().getApplicationContext(), throwable.getMessage(), Toast.LENGTH_LONG).show();
                }
            }));

        return view;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        subscriptions.unsubscribe();
    }

    private String summaryToStr(SuuntoLogbookSummary summary) {
        return "Summary of type " + summary.getActivityType();
    }

    interface LogbookProvider {
        Logbook getLogbook();
    }
}
