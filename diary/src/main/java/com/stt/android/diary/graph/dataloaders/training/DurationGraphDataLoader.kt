package com.stt.android.diary.graph.dataloaders.training

import com.stt.android.controllers.CurrentUserController
import com.stt.android.diary.graph.dataloaders.base.TrainingGraphDataLoader
import com.stt.android.domain.diary.models.ChartPoint
import com.stt.android.domain.diary.models.GraphData
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.workouts.GetWorkoutHeadersForRangeUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class DurationGraphDataLoader @Inject constructor(
    getWorkoutHeadersForRangeUseCase: GetWorkoutHeadersForRangeUseCase,
    currentUserController: CurrentUserController
) : TrainingGraphDataLoader(getWorkoutHeadersForRangeUseCase, currentUserController) {

    private data class DurationGraphData(override val data: Map<GraphDataType, List<ChartPoint>>) :
        GraphData {
        companion object {
            fun create(headers: List<WorkoutHeader>): GraphData {
                val chartPoints = headers.map { header ->
                    ChartPoint(
                        header.startTime,
                        (header.totalTime / TimeUnit.HOURS.toSeconds(1)).toFloat()
                    )
                }

                return DurationGraphData(
                    mapOf(
                        GraphDataType.DURATION to chartPoints,
                        GraphDataType.TRAINING to chartPoints
                    )
                )
            }
        }
    }

    override suspend fun createGraphData(headers: List<WorkoutHeader>): GraphData =
        DurationGraphData.create(headers)
}
