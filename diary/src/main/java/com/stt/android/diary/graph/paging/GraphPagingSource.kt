package com.stt.android.diary.graph.paging

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.DaoFactory
import com.stt.android.diary.common.RoomTableObserver
import com.stt.android.diary.common.RoomTableObserverDelegate
import com.stt.android.diary.graph.data.ChartPage
import com.stt.android.diary.graph.dataloaders.GraphDataLoaders
import com.stt.android.diary.graph.dataloaders.base.GraphDataLoader
import com.stt.android.diary.graph.dataloaders.base.observedRoomTables
import com.stt.android.diary.graph.dataloaders.getLoaders
import com.stt.android.diary.graph.extensions.toGraphTimeFrame
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.diary.models.GraphTimeFrame
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.utils.CalendarProvider
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.Clock

class GraphPagingSource(
    scope: CoroutineScope,
    val graphId: GraphId,
    private val timeRange: GraphTimeRange,
    private val initialPage: Int?,
    val primaryGraphType: GraphDataType,
    val secondaryGraphType: GraphDataType?,
    dataLoaders: GraphDataLoaders,
    private val clock: Clock,
    private val calendarProvider: CalendarProvider,
    private val daoFactory: DaoFactory
) : PagingSource<Int, ChartPage>(), RoomTableObserver by RoomTableObserverDelegate() {
    private val graphDataLoaders =
        dataLoaders.getLoaders(listOf(primaryGraphType, secondaryGraphType))

    private var initialPageLoaded = false

    init {
        initRoomTableObserver(graphDataLoaders.observedRoomTables, this, daoFactory, scope)
    }

    fun onCleared() {
        unregisterObservers(daoFactory)
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ChartPage> = withContext(IO) {
        runSuspendCatching {
            val pageNumber = params.key ?: FIRST_PAGE
            val timeFrame = timeRange.toGraphTimeFrame(
                clock,
                calendarProvider.getDayOfWeekField(),
                pageNumber
            )

            GraphDataToChartPageUseCase.toLoadResult(
                dataMap = loadGraphData(timeFrame),
                timeFrame = timeFrame,
                pageNumber = pageNumber,
                primaryGraphType = primaryGraphType,
                secondaryGraphType = secondaryGraphType
            )
        }.getOrElse { e ->
            LoadResult.Error(e)
        }
    }

    private suspend fun loadGraphData(
        timeFrame: GraphTimeFrame
    ): Map<GraphDataType, GraphDataLoader.LoadResult> = graphDataLoaders.map { loader ->
        loader.load(
            timeFrame,
            calendarProvider
        ).toMutableMap()
    }.reduce { acc, map -> acc.apply { putAll(map) } }

    /**
     * Since the page size is 1 for graphs, we cannot use the same approach as for the list.
     * The initialPage is used to stay on the current page when the graph data type is changed and
     * the data source is invalidated.
     */
    override fun getRefreshKey(state: PagingState<Int, ChartPage>): Int? {
        if (!initialPageLoaded) {
            Timber.d("Refreshing page: $initialPage")
            initialPageLoaded = true
        }
        return initialPage
    }

    companion object {
        private const val FIRST_PAGE = 1
    }
}
