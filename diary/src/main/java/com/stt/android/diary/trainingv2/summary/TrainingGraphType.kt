package com.stt.android.diary.trainingv2.summary

import androidx.annotation.StringRes
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.diary.R
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

enum class TrainingGraphType {
    DURATION,
    DISTANCE,
    AVG_SPEED,
    AVG_PACE,
    AVG_HEART_RATE,
    ASCENT,
    TSS,
    CALORIES,
    VO2MAX,
    AVG_POWER,
    NORMALIZED_POWER,
    AVG_SWIM_PACE,
}

@get:StringRes
val TrainingGraphType.titleResId: Int
    get() = when (this) {
        TrainingGraphType.DURATION -> R.string.training_graph_type_duration
        TrainingGraphType.DISTANCE -> R.string.training_graph_type_distance
        TrainingGraphType.AVG_SPEED -> R.string.training_graph_type_avg_peed
        TrainingGraphType.AVG_PACE -> R.string.training_graph_type_avg_pace
        TrainingGraphType.AVG_HEART_RATE -> R.string.training_graph_type_avg_heart_rate
        TrainingGraphType.ASCENT -> R.string.training_graph_type_ascent
        TrainingGraphType.TSS -> R.string.training_graph_type_tss
        TrainingGraphType.CALORIES -> R.string.training_graph_type_calories
        TrainingGraphType.VO2MAX -> R.string.training_graph_type_vo2max
        TrainingGraphType.AVG_POWER -> R.string.training_graph_type_avg_power
        TrainingGraphType.NORMALIZED_POWER -> R.string.training_graph_type_normalized_power
        TrainingGraphType.AVG_SWIM_PACE -> R.string.training_graph_type_avg_swim_pace
    }

@StringRes
fun TrainingGraphType.unitResId(unit: MeasurementUnit): Int? =
    when (this) {
        TrainingGraphType.DURATION -> CR.string.hour
        TrainingGraphType.DISTANCE -> unit.distanceUnit
        TrainingGraphType.AVG_SPEED -> unit.speedUnit
        TrainingGraphType.AVG_PACE -> unit.paceUnit
        TrainingGraphType.AVG_HEART_RATE -> CR.string.bpm
        TrainingGraphType.ASCENT -> unit.altitudeUnit
        TrainingGraphType.TSS -> BaseR.string.workout_values_headline_tss
        TrainingGraphType.CALORIES -> CR.string.kcal
        TrainingGraphType.VO2MAX -> CR.string.vo2maxUnit
        TrainingGraphType.AVG_POWER -> CR.string.watt
        TrainingGraphType.NORMALIZED_POWER -> CR.string.watt
        TrainingGraphType.AVG_SWIM_PACE -> unit.swimPaceUnit
    }
