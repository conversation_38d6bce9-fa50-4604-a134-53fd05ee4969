package com.stt.android.diary.workout.training

import com.stt.android.common.ui.ViewPagerFragmentCreator
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named

@Module
@InstallIn(SingletonComponent::class)
abstract class TrainingModule {
    @Binds
    @Named("TRAINING_FRAGMENT_CREATOR")
    abstract fun bindTrainingFragmentCreator(creator: TrainingFragmentCreator): ViewPagerFragmentCreator
}
