package com.stt.android.diary.recovery.data

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.LineChartConfig
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.controllers.UserSettingsController
import com.stt.android.data.TimeUtils
import com.stt.android.diary.recovery.v2.CommonChartContributor
import com.stt.android.diary.recovery.v2.ContributorType
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.recovery.RecoveryDataRepository
import com.stt.android.home.diary.R
import com.stt.android.utils.toEpochMilli
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEmpty
import java.time.LocalDate
import java.time.LocalTime
import java.time.YearMonth
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.R as BaseR

class ResourcesChartDataLoader @Inject constructor(
    private val context: Context,
    private val recoveryDataRepository: RecoveryDataRepository,
    private val userSettingsController: UserSettingsController
) {
    companion object {
        private const val NO_DATA_VALUE = 0f
        private const val MIN_Y = 0.0
        private const val DEFAULT_MAX_Y = 120.0
        
        private val SUPPORTED_GRANULARITIES = setOf(
            ChartGranularity.WEEKLY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.MONTHLY,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIX_MONTHS,
            ChartGranularity.YEARLY
        )
    }
    
    private val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
    
    fun loadChartContributor(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate
    ): Flow<CommonChartContributor> {
        if (chartGranularity !in SUPPORTED_GRANULARITIES) {
            throw IllegalArgumentException("This loader does not support ChartGranularity.$chartGranularity")
        }
        
        return recoveryDataRepository.fetchRecoveryDataForDateRange(
            from.atStartOfDay().toEpochMilli(),
            to.atTime(LocalTime.MAX).toEpochMilli()
        ).onEmpty { emit(emptyList()) }
        .map { recoveryDataList ->
            val chartData = seriesStrategies[chartGranularity]?.createChartData(chartGranularity, from, to, recoveryDataList)
                ?: DailyAggregatedResourcesSeriesStrategy().createChartData(chartGranularity, from, to, recoveryDataList)
            
            CommonChartContributor(
                value = chartData.series.firstOrNull()?.value ?: AnnotatedString("--"),
                valueType = context.getString(R.string.recovery_average),
                chartData = chartData,
                contributorType = ContributorType.RESOURCES
            )
        }
    }
    

    
    private fun createSeries(
        chartType: ChartType,
        color: Int,
        axisRange: ChartData.AxisRange,
        entries: ImmutableList<ChartData.Entry>,
        value: AnnotatedString = AnnotatedString(""),
        candlestickEntries: ImmutableList<ChartData.CandlestickEntry> = persistentListOf()
    ): ChartData.Series {
        return ChartData.Series(
            chartType = chartType,
            color = color,
            axisRange = axisRange,
            entries = entries,
            value = value,
            candlestickEntries = candlestickEntries,
            lineConfig = LineChartConfig(),
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarStyle = null,
        )
    }
    
    private fun LocalDate.daysUntil(endExclusive: LocalDate): Sequence<LocalDate> = 
        generateSequence(this) { date -> 
            date.plusDays(1).takeIf { it.isBefore(endExclusive) }
        }
    
    private interface ResourcesDataSeriesStrategy {
        fun createChartData(
            chartGranularity: ChartGranularity,
            from: LocalDate,
            to: LocalDate,
            recoveryData: List<RecoveryData>
        ): ChartData
    }
    
    private val seriesStrategies: Map<ChartGranularity, ResourcesDataSeriesStrategy> = mapOf(
        ChartGranularity.WEEKLY to DailyAggregatedResourcesSeriesStrategy(),
        ChartGranularity.SEVEN_DAYS to DailyAggregatedResourcesSeriesStrategy(),
        ChartGranularity.MONTHLY to DailyAggregatedResourcesSeriesStrategy(),
        ChartGranularity.THIRTY_DAYS to DailyAggregatedResourcesSeriesStrategy(),
        ChartGranularity.SIX_MONTHS to WeeklyResourcesSeriesStrategy(),
        ChartGranularity.YEARLY to MonthlyResourcesSeriesStrategy()
    )
    
    private inner class DailyAggregatedResourcesSeriesStrategy : ResourcesDataSeriesStrategy {
        override fun createChartData(
            chartGranularity: ChartGranularity,
            from: LocalDate,
            to: LocalDate,
            recoveryData: List<RecoveryData>
        ): ChartData {
            val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
            
            val axisRange = ChartData.AxisRange(
                minX = from.toEpochDay().toDouble(),
                maxX = to.toEpochDay().toDouble(),
                minY = MIN_Y,
                maxY = DEFAULT_MAX_Y
            )
            
            if (recoveryData.isNotEmpty()) {
                val sortedRecoveryData = recoveryData.sortedBy { it.timestamp }
                
                val dailyRecoveryData = sortedRecoveryData.groupBy { 
                    TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate() 
                }
                
                for (date in from.daysUntil(to.plusDays(1))) {
                    val dayData = dailyRecoveryData[date]
                    
                    if (!dayData.isNullOrEmpty()) {
                        val balances = dayData.map { it.balance * 100 } 
                        
                        val min = balances.minOrNull() ?: 0f
                        val max = balances.maxOrNull() ?: 0f
                        
                        candlestickEntries.add(
                            ChartData.CandlestickEntry(
                                x = date.toEpochDay(),
                                open = min,  
                                close = max, 
                                low = min,   
                                high = max   
                            )
                        )
                    }
                }
            }
            
            val averageBalance = if (recoveryData.isNotEmpty()) {
                recoveryData.map { it.balance }.average().toFloat()
            } else {
                NO_DATA_VALUE
            }
            
            return createCommonChartData(
                chartGranularity,
                candlestickEntries,
                axisRange,
                averageBalance
            )
        }
    }
    
    private inner class WeeklyResourcesSeriesStrategy : ResourcesDataSeriesStrategy {
        override fun createChartData(
            chartGranularity: ChartGranularity,
            from: LocalDate,
            to: LocalDate,
            recoveryData: List<RecoveryData>
        ): ChartData {
            val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
            
            val axisRange = ChartData.AxisRange(
                minX = from.toEpochDay().toDouble(),
                maxX = to.toEpochDay().toDouble(),
                minY = MIN_Y,
                maxY = DEFAULT_MAX_Y
            )
            
            if (recoveryData.isNotEmpty()) {
                val sortedRecoveryData = recoveryData.sortedBy { it.timestamp }
                
                val weeklyRecoveryData = sortedRecoveryData.groupBy { 
                    val date = TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate()
                    date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                }
                
                val startWeek = from.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                val endWeek = to.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                generateSequence(startWeek) { week -> 
                    week.plusWeeks(1).takeIf { it <= endWeek }
                }.forEach { weekStart ->
                    val weekData = weeklyRecoveryData[weekStart]
                    
                    if (!weekData.isNullOrEmpty()) {
                        val balances = weekData.map { it.balance * 100 }
                        
                        val min = balances.minOrNull() ?: 0f
                        val max = balances.maxOrNull() ?: 0f
                        
                        candlestickEntries.add(
                            ChartData.CandlestickEntry(
                                x = weekStart.toEpochDay(),
                                open = min,
                                close = max,
                                low = min,
                                high = max
                            )
                        )
                    }
                }
            }
            
            val averageBalance = if (recoveryData.isNotEmpty()) {
                val weeklyAverages = recoveryData
                    .groupBy { 
                        val date = TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate()
                        date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                    }
                    .values
                    .map { weekData -> weekData.map { it.balance }.average().toFloat() }
                
                if (weeklyAverages.isNotEmpty()) {
                    weeklyAverages.average().toFloat()
                } else {
                    NO_DATA_VALUE
                }
            } else {
                NO_DATA_VALUE
            }
            
            return createCommonChartData(
                chartGranularity,
                candlestickEntries,
                axisRange,
                averageBalance
            )
        }
    }
    
    private inner class MonthlyResourcesSeriesStrategy : ResourcesDataSeriesStrategy {
        override fun createChartData(
            chartGranularity: ChartGranularity,
            from: LocalDate,
            to: LocalDate,
            recoveryData: List<RecoveryData>
        ): ChartData {
            val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
            
            val axisRange = ChartData.AxisRange(
                minX = from.epochMonth.toDouble(),
                maxX = to.epochMonth.toDouble(),
                minY = MIN_Y,
                maxY = DEFAULT_MAX_Y
            )
            
            if (recoveryData.isNotEmpty()) {
                val sortedRecoveryData = recoveryData.sortedBy { it.timestamp }
                
                val monthlyRecoveryData = sortedRecoveryData.groupBy { 
                    val date = TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate()
                    YearMonth.from(date)
                }
                
                val startMonth = YearMonth.from(from)
                val endMonth = YearMonth.from(to)
                generateSequence(startMonth) { month -> 
                    month.plusMonths(1).takeIf { it <= endMonth }
                }.forEach { yearMonth ->
                    val monthData = monthlyRecoveryData[yearMonth]
                    
                    if (!monthData.isNullOrEmpty()) {
                        val balances = monthData.map { it.balance * 100 }
                        
                        val min = balances.minOrNull() ?: 0f
                        val max = balances.maxOrNull() ?: 0f
                        
                        val monthStart = yearMonth.atDay(1)
                        
                        candlestickEntries.add(
                            ChartData.CandlestickEntry(
                                x = monthStart.epochMonth.toLong(),
                                open = min,
                                close = max,
                                low = min,
                                high = max
                            )
                        )
                    }
                }
            }
            
            val averageBalance = if (recoveryData.isNotEmpty()) {
                val monthlyAverages = recoveryData
                    .groupBy { 
                        val date = TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate()
                        YearMonth.from(date)
                    }
                    .values
                    .map { monthData -> monthData.map { it.balance }.average().toFloat() }
                
                if (monthlyAverages.isNotEmpty()) {
                    monthlyAverages.average().toFloat()
                } else {
                    NO_DATA_VALUE
                }
            } else {
                NO_DATA_VALUE
            }
            
            return createCommonChartData(
                chartGranularity,
                candlestickEntries,
                axisRange,
                averageBalance
            )
        }
    }
    


    private fun createCommonChartData(
        chartGranularity: ChartGranularity,
        candlestickEntries: List<ChartData.CandlestickEntry>,
        axisRange: ChartData.AxisRange,
        averageBalance: Float
    ): ChartData {
        val value = buildAnnotatedString {
            withStyle(SpanStyle(fontSize = 24.sp)) {
                append("${(averageBalance * 100).roundToInt()}")
            }

            withStyle(SpanStyle(fontSize = 12.sp)) {
                append("%")
            }
        }

        val mainSeries = createSeries(
            chartType = ChartType.CANDLESTICK,
            color = context.getColor(BaseR.color.dashboard_widget_hrv),
            axisRange = axisRange,
            entries = persistentListOf(),
            value = value,
            candlestickEntries = candlestickEntries.toImmutableList()
        )
        
        return ChartData(
            chartGranularity = chartGranularity,
            series = persistentListOf(mainSeries),
            highlightEnabled = false,
            goal = null,
            currentValues = persistentListOf(),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            chartContent = ChartContent.RESOURCES,
            highlightDecorationLines = persistentMapOf(),
            colorIndicator = null,
        )
    }
} 
