package com.stt.android.diary.insights

import java.time.LocalTime

interface TrainingHubFormatter {
    fun formatDistance(value: Double): Pair<String, Int?>
    fun formatAccumulatedDistance(value: Double): Pair<String, Int?>
    fun formatAscent(value: Double): Pair<String, Int?>
    fun formatDuration(value: Double): Pair<String, Int?>
    fun formatDurationExactToSeconds(value: Double): Pair<String, Int?>
    fun formatTSS(value: Double): Pair<String, Int>
    fun formatAverageCount(value: Float): String
    fun formatSleepTime(localTime: LocalTime): String
}
