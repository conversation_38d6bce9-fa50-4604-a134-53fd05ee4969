package com.stt.android.workout.planner.editstep

import androidx.compose.runtime.Immutable
import androidx.compose.runtime.saveable.Saver
import androidx.compose.ui.text.input.TextFieldValue
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.domain.user.MeasurementUnit

@Immutable
data class EditStepTargetState(
    val editMode: EditStepTargetMode,
    val phase: WorkoutStep.Exercise.Phase,
    val unit: MeasurementUnit,
    val heartRateTarget: EditStepTargetMinMaxState,
    val powerTarget: EditStepTargetMinMaxState,
    val speedTarget: EditStepTargetMinMaxState,
    val paceTarget: EditStepTargetMinMaxState,
) {
    companion object {
        val Saver = Saver<EditStepTargetState, Any>(
            save = {
                arrayListOf(
                    it.editMode,
                    it.phase,
                    it.unit,
                    with(EditStepTargetMinMaxState.Saver) { save(it.heartRateTarget) },
                    with(EditStepTargetMinMaxState.Saver) { save(it.powerTarget) },
                    with(EditStepTargetMinMaxState.Saver) { save(it.speedTarget) },
                    with(EditStepTargetMinMaxState.Saver) { save(it.paceTarget) },
                )
            },
            restore = {
                @Suppress("UNCHECKED_CAST")
                val list = it as List<Any>
                EditStepTargetState(
                    editMode = list[0] as EditStepTargetMode,
                    phase = list[1] as WorkoutStep.Exercise.Phase,
                    unit = list[2] as MeasurementUnit,
                    heartRateTarget = with(EditStepTargetMinMaxState.Saver) { restore(list[3])!! },
                    powerTarget = with(EditStepTargetMinMaxState.Saver) { restore(list[4])!! },
                    speedTarget = with(EditStepTargetMinMaxState.Saver) { restore(list[5])!! },
                    paceTarget = with(EditStepTargetMinMaxState.Saver) { restore(list[6])!! },
                )
            }
        )
    }
}

@Immutable
data class EditStepTargetMinMaxState(
    val unitText: String,
    val minValue: TextFieldValue,
    val maxValue: TextFieldValue,
) {
    companion object {
        val Saver = Saver<EditStepTargetMinMaxState, Any>(
            save = {
                arrayListOf(
                    it.unitText,
                    with(TextFieldValue.Saver) { save(it.minValue) },
                    with(TextFieldValue.Saver) { save(it.maxValue) },
                )
            },
            restore = {
                @Suppress("UNCHECKED_CAST")
                val list = it as List<Any>
                EditStepTargetMinMaxState(
                    unitText = list[0] as String,
                    minValue = with(TextFieldValue.Saver) { restore(list[1]) }!!,
                    maxValue = with(TextFieldValue.Saver) { restore(list[2]) }!!,
                )
            }
        )
    }
}

// EditStepTargetOptions does not emit a fully populated state object when state changes in order
// to make sure state changes are not unintentionally overridden due to multiple callbacks
// happening between recompositions.
data class EditStepTargetStatePartialUpdate(
    val newEditMode: EditStepTargetMode? = null,
    val newHeartRateMinMax: EditStepTargetMinMaxStatePartialUpdate? = null,
    val newPowerMinMax: EditStepTargetMinMaxStatePartialUpdate? = null,
    val newSpeedMinMax: EditStepTargetMinMaxStatePartialUpdate? = null,
    val newPaceMinMax: EditStepTargetMinMaxStatePartialUpdate? = null,
)

private fun EditStepTargetMinMaxState.withUpdateApplied(
    update: EditStepTargetMinMaxStatePartialUpdate?
) = if (update != null) {
    copy(
        minValue = update.minValue ?: minValue,
        maxValue = update.maxValue ?: maxValue
    )
} else {
    this
}

data class EditStepTargetMinMaxStatePartialUpdate(
    val minValue: TextFieldValue? = null,
    val maxValue: TextFieldValue? = null,
)

fun EditStepTargetState.withUpdateApplied(
    update: EditStepTargetStatePartialUpdate
): EditStepTargetState {
    return copy(
        editMode = update.newEditMode ?: editMode,
        heartRateTarget = heartRateTarget.withUpdateApplied(update.newHeartRateMinMax),
        powerTarget = powerTarget.withUpdateApplied(update.newPowerMinMax),
        speedTarget = speedTarget.withUpdateApplied(update.newSpeedMinMax),
        paceTarget = paceTarget.withUpdateApplied(update.newPaceMinMax),
    )
}
