package com.stt.android.workout.planner.common

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle

fun buildAnnotatedStringWithStyledNumbers(
    text: String,
    numberStyle: SpanStyle
): AnnotatedString {
    val firstNumberIndex = text.indexOfFirst { it.isDigit() }
    val lastNumberIndex = text.indexOfLast { it.isDigit() }
    return if (firstNumberIndex != -1 && lastNumberIndex != -1) {
        AnnotatedString(
            text = text,
            spanStyles = listOf(
                AnnotatedString.Range(
                    item = numberStyle,
                    start = firstNumberIndex,
                    end = lastNumberIndex + 1 // end index is exclusive
                )
            )
        )
    } else {
        AnnotatedString(text)
    }
}
