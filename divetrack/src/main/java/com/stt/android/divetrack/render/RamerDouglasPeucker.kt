package com.stt.android.divetrack.render

import org.rajawali3d.math.vector.Vector3

internal class RamerDouglasPeucker {
    internal companion object {
        fun simplify(points: List<Vector3>): List<Vector3> {
            return if (points.size < 3) points else simplifyRecursive(points, 0.2f)
        }

        private fun simplifyRecursive(points: List<Vector3>, epsilon: Float): List<Vector3> {
            var index = -1
            var maxDistance = 0.0f

            for (i in 1 until points.size - 1) {
                val distance = perpendicularDistance(points[i], points.first(), points.last())
                if (distance > maxDistance) {
                    index = i
                    maxDistance = distance
                }
            }
            return if (maxDistance > epsilon && index != -1) {
                val firstHalf = simplifyRecursive(points.subList(0, index + 1), epsilon)
                val secondHalf = simplifyRecursive(points.subList(index, points.size), epsilon)
                firstHalf.dropLast(1) + secondHalf
            } else {
                listOf(points.first(), points.last())
            }
        }

        private fun perpendicularDistance(
            point: Vector3,
            lineStart: Vector3,
            lineEnd: Vector3
        ): Float {
            if (lineStart == lineEnd) {
                return point.clone().subtract(lineEnd).length().toFloat()
            }

            val lineVec = lineEnd.clone().subtract(lineStart)
            val pointVec = point.clone().subtract(lineStart)
            val crossProd = lineVec.clone().cross(pointVec)
            val areaOfParallelogram = crossProd.length()
            val lengthOfBase = lineVec.length()

            return (areaOfParallelogram / lengthOfBase).toFloat()
        }
    }
}
