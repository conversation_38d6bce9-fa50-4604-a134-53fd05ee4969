package com.stt.android.divetrack.render

import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import com.stt.android.divetrack.R

internal object Environment {
    const val BORDER_THICKNESS = 3F

    @ColorInt
    const val COLOR_TRACK = 0xFFFF3333.toInt()

    @ColorInt
    const val COLOR_SHADOW = 0x40E9ECEE

    @ColorInt
    const val COLOR_BACKGROUND = 0xFF416188.toInt()

    @ColorInt
    const val COLOR_BOTTOM_PLANE = 0x00FFFFFF

    @ColorInt
    const val COLOR_BOTTOM_PLANE_BORDER = 0xE9ECEE

    @ColorInt
    const val COLOR_WALL_MESH = 0x4CFFFFFF
}

internal enum class IconConfig(
    val width: Float,
    val height: Float,
    val textureName: String,
    @DrawableRes val resId: Int,
) {
    START(0.2f, 0.2f, "start", R.drawable.dive_track_start),
    E<PERSON>(0.2f, 0.2f, "end", R.drawable.dive_track_stop)
}

internal enum class TrackConfig(
    val thickness: Float,
    @ColorInt val color: Int
) {
    TRACK(2f, Environment.COLOR_TRACK),
    SHADOW(1f, Environment.COLOR_SHADOW)
}
