package com.stt.android.utils

import com.stt.android.coroutines.runSuspendCatching
import kotlin.math.max

inline fun <T> Iterable<T>.sumByFloat(selector: (T) -> Float): Float {
    var sum = 0f
    for (element in this) {
        sum += selector(element)
    }
    return sum
}

/**
 * Returns the sum of all values produced by [selector] function applied to each element in the collection.
 */
inline fun <T> Sequence<T>.sumByFloat(selector: (T) -> Float): Float {
    var sum = 0f
    for (element in this) {
        sum += selector(element)
    }
    return sum
}

suspend inline fun <T : Any, R> Iterable<T>.runSuspendCatchingEach(
    crossinline action: suspend (T) -> R
): List<Result<R>> = map {
    runSuspendCatching {
        action(it)
    }
}

inline fun <T, I : Iterable<Result<T>>> I.onEachSuccess(action: (T) -> Unit): I {
    filter { it.isSuccess }
        .forEach { action(it.getOrThrow()) }
    return this
}

inline fun <T, I : Iterable<Result<T>>> I.onEachFailure(action: (e: Throwable) -> Unit): I {
    filter { it.isFailure }
        .forEach { it.exceptionOrNull()?.let { e -> action(e) } }
    return this
}

/**
 * Divide a list into groups cutting when the predicate returns true.
 * All the cut points (indexes at which the cut happens) are by default discarded.
 * @param discardCutPoints if true, cutPoints are discarded.
 *      If false, cutPoints are added to the result (list is divided just before the cut point).
 * @param discardEmpty if true, empty lists resulting from the cut (ie. if two cutPoints are consecutive) are discarded.
 */
inline fun <T> List<T>.splitBy(
    discardEmpty: Boolean = true,
    discardCutPoints: Boolean = true,
    crossinline predicate: (T) -> Boolean
): List<List<T>> {
    val result = mutableListOf<List<T>>()
    val cutPointIndexes = this.withIndex()
        .filter { predicate(it.value) }
        .map { it.index } + size
    var prevCutIndex = -1
    for (cutPointIndex in cutPointIndexes) {
        val fromCut = when {
            prevCutIndex < 0 -> 0
            discardCutPoints -> prevCutIndex + 1
            else -> prevCutIndex
        }
        val cutSize = cutPointIndex - fromCut
        if (fromCut + cutSize <= this.size) result.add(List(cutSize) { i -> this[fromCut + i] })
        prevCutIndex = cutPointIndex
    }
    return if (discardEmpty) result.filter { it.isNotEmpty() } else result
}

/**
 * Divide a list into groups cutting between the items when the predicate returns true,
 * comparing each item with the first in the previous category.
 */
inline fun <T> List<T>.windowByCompareFirst(crossinline predicate: (T, T) -> Boolean): List<List<T>> {
    if (isEmpty()) return emptyList()
    if (size == 1) return listOf(listOf(this[0]))
    var listIter = mutableListOf<T>()
    val result = mutableListOf<List<T>>()
    result.add(listIter)
    forEach {
        if (listIter.isNotEmpty() && predicate(listIter.first(), it)) {
            listIter = mutableListOf()
            result.add(listIter)
        }
        listIter.add(it)
    }
    return result
}

/**
 * Divide a list into groups cutting between the items when the predicate returns, comparing each item with its predecessor.
 */
inline fun <T> List<T>.splitByCompareLast(crossinline predicate: (T, T) -> Boolean): List<List<T>> {
    if (isEmpty()) return emptyList()
    if (size == 1) return listOf(listOf(this[0]))
    val result = mutableListOf<List<T>>()
    val cutAtIndexes = (0 until (size - 1))
        .filter { predicate(this[it], this[it + 1]) }
        .map { it + 1 } + size

    var prevCutIndex = -1
    for (cutPointIndex in cutAtIndexes) {
        val fromCut = max(0, prevCutIndex)
        val cutSize = cutPointIndex - fromCut
        if (fromCut + cutSize <= this.size) result.add(List(cutSize) { i -> this[fromCut + i] })
        prevCutIndex = cutPointIndex
    }
    return result
}

/**
 * Kotlin [List<T>.windowed] behaves very weird.
 * This is a more straightforward implementation
 */
fun <T> List<T>.windowByStep(step: Int): List<List<T>> {
    if (step <= 0 || step >= size) {
        return listOf(this.toList())
    }
    var listIter = mutableListOf<T>()
    val result = mutableListOf<List<T>>()
    result.add(listIter)
    forEachIndexed { index, t ->
        if (listIter.isNotEmpty() && index % step == 0) {
            listIter = mutableListOf()
            result.add(listIter)
        }
        listIter.add(t)
    }
    return result
}

inline fun <reified T> List<*>.firstOfType(): T? = this.firstOrNull { it != null && it is T } as? T

/**
 * Removes the first occurrence of a predicate from the list
 */
inline fun <T> Sequence<T>.removeFirstOccurrence(crossinline predicate: (T) -> Boolean): Sequence<T> {
    var found = false
    return filter {
        if (found) {
            true
        } else {
            if (predicate(it)) {
                found = true
                false
            } else {
                true
            }
        }
    }
}

inline fun <T> Iterable<T>.averageOfInt(selector: (T) -> Int): Double {
    val count = count()
    if (count == 0) {
        return Double.NaN
    }

    return sumOf(selector).toDouble() / count
}

inline fun <T> Iterable<T>.averageOfFloat(selector: (T) -> Float): Double {
    val count = count()
    if (count == 0) {
        return Double.NaN
    }

    return sumOf { selector(it).toDouble()} / count
}

inline fun <T> Iterable<T>.averageOfDouble(selector: (T) -> Double): Double {
    val count = count()
    if (count == 0) {
        return Double.NaN
    }

    return sumOf { selector(it) } / count
}

fun Iterable<Double>.averageOrNull(): Double? {
    // average() returns NaN when called on empty lists.
    return average().takeUnless { it.isNaN() }
}
fun Iterable<Float>.averageOrNull(): Float? {
    // average() returns NaN when called on empty lists.
    return average().toFloat().takeUnless { it.isNaN() }
}

fun <T> Collection<T>.takeIfNotEmpty(): Collection<T>? {
    return takeIf { it.isNotEmpty() }
}

inline fun <T, K> Iterable<T>.distributionBy(keySelector: (T) -> K): Map<K, Int> {
    return groupBy(keySelector).mapValues { (_, value) -> value.count() }
}

/**
 * Returns a list containing only elements from the given collection
 * having distinct keys returned by the given [selector] function.
 *
 * Among elements of the given collection with equal keys, only the last element with greatest
 * or equal comparison by [comparator] is kept.
 * The elements in the resulting list are in the same order as they were in the source collection.
 */
inline fun <T, K> Iterable<T>.distinctBy(
    comparator: Comparator<in T>,
    selector: (T) -> K
): List<T> {
    val map = LinkedHashMap<K, T>()
    for (e in this) {
        val key = selector(e)
        val previous = map[key]
        if (previous == null) {
            map[key] = e
        } else if (comparator.compare(e, previous) >= 0) {
            map.remove(key)
            map[key] = e
        }
    }
    return map.values.toList()
}

/**
 * Returns the last non-null value produced by [transform] function being applied to elements of this collection in iteration order,
 * or `null` if no non-null value was produced.
 */
inline fun <T, R : Any> List<T>.lastNotNullOfOrNull(transform: (T) -> R?): R? {
    val iterator = this.listIterator(size)
    while (iterator.hasPrevious()) {
        val result = transform(iterator.previous())
        if (result != null) return result
    }
    return null
}
