package com.stt.android.questionnaire.usecases

import com.stt.android.domain.CoroutineUseCase
import com.stt.android.domain.user.settings.QuestionnaireRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

class StoreSportsUseCase @Inject constructor(
    private val questionnaireRepository: QuestionnaireRepository
) : CoroutineUseCase<Unit, List<Int>> {

    @Suppress("PARAMETER_NAME_CHANGED_ON_OVERRIDE")
    override suspend fun run(favoriteSports: List<Int>) = withContext(Dispatchers.IO) {
        questionnaireRepository.saveFavoriteSports(favoriteSports)
    }
}
