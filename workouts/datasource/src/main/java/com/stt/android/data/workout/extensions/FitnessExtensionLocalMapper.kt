package com.stt.android.data.workout.extensions

import com.stt.android.data.EntityMapper
import com.stt.android.data.source.local.fitnessextension.LocalFitnessExtension
import com.stt.android.domain.user.workoutextension.FitnessExtension
import javax.inject.Inject

class FitnessExtensionLocalMapper
@Inject constructor() : EntityMapper<LocalFitnessExtension, FitnessExtension> {

    override fun toDomainEntity(): Function1<LocalFitnessExtension, FitnessExtension> = {
        FitnessExtension(
            workoutId = it.workoutId,
            maxHeartRate = it.maxHeartRate,
            vo2Max = it.vo2Max,
            fitnessAge = it.fitnessAge,
        )
    }

    override fun toDataEntity(): Function1<FitnessExtension, LocalFitnessExtension> = {
        it.toLocalFitnessExtension()
    }

    private fun FitnessExtension.toLocalFitnessExtension(): LocalFitnessExtension {
        return LocalFitnessExtension(
            workoutId = workoutId,
            maxHeartRate = maxHeartRate,
            vo2Max = vo2Max,
            fitnessAge = fitnessAge,
        )
    }
}
