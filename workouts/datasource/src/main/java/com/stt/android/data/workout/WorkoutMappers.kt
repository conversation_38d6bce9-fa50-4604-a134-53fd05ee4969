package com.stt.android.data.workout

import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.data.routes.toLocalPoint
import com.stt.android.data.routes.toPoint
import com.stt.android.data.source.local.tags.LocalSuuntoTag
import com.stt.android.data.source.local.tags.LocalUserTag
import com.stt.android.data.source.local.workout.LocalBasicWorkoutHeader
import com.stt.android.data.source.local.workout.LocalSummaryWorkoutHeader
import com.stt.android.data.source.local.workout.LocalUserWorkoutSummaryByActivityType
import com.stt.android.data.source.local.workout.LocalWeChatWorkoutData
import com.stt.android.data.source.local.workout.LocalWorkoutFeeling
import com.stt.android.data.source.local.workout.LocalWorkoutHeader
import com.stt.android.data.source.local.workout.tss.LocalTSS
import com.stt.android.data.source.local.workout.tss.LocalTSSCalculationMethod
import com.stt.android.data.source.local.workout.zonesense.LocalZoneSense
import com.stt.android.data.source.local.workout.zonesense.LocalZoneSenseSyncWorkoutHeader
import com.stt.android.data.tags.toDomain
import com.stt.android.data.workout.comment.toDomainWorkoutComment
import com.stt.android.domain.Point
import com.stt.android.domain.ranking.Ranking
import com.stt.android.domain.review.ReviewState
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.BasicWorkoutHeader
import com.stt.android.domain.workouts.DomainWorkout
import com.stt.android.domain.workouts.SummaryWorkoutHeader
import com.stt.android.domain.workouts.WeChatWorkoutData
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.ZoneSenseSyncWorkoutHeader
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummary
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.pictures.Picture
import com.stt.android.domain.workouts.reactions.DomainReactionSummary
import com.stt.android.domain.workouts.stats.ActivityTypeStats
import com.stt.android.domain.workouts.stats.WorkoutStats
import com.stt.android.domain.workouts.summary.UserWorkoutSummaryByActivity
import com.stt.android.domain.workouts.summary.WorkoutFeeling
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS
import com.stt.android.domain.workouts.videos.Video
import com.stt.android.domain.workouts.zonesense.ZoneSense
import com.stt.android.infomodel.SummaryItem
import com.stt.android.logbook.CompetitionResult
import com.stt.android.remote.routes.RemotePoint
import com.stt.android.remote.tags.RemoteUserTag
import com.stt.android.remote.workout.RemoteActivityTypeStats
import com.stt.android.remote.workout.RemoteCombinedWorkout
import com.stt.android.remote.workout.RemoteCompetitionResult
import com.stt.android.remote.workout.RemoteCompetitionWorkoutDetails
import com.stt.android.remote.workout.RemoteSuuntoTag
import com.stt.android.remote.workout.RemoteSyncedWorkout
import com.stt.android.remote.workout.RemoteSyncedWorkoutImage
import com.stt.android.remote.workout.RemoteSyncedWorkoutRankings
import com.stt.android.remote.workout.RemoteSyncedWorkoutReaction
import com.stt.android.remote.workout.RemoteTSS
import com.stt.android.remote.workout.RemoteTSSCalculationMethod
import com.stt.android.remote.workout.RemoteUpdatedWorkout
import com.stt.android.remote.workout.RemoteWorkoutStats
import com.stt.android.remote.workout.RemoteZoneSense
import com.stt.android.remote.workout.picture.RemotePicture
import com.stt.android.remote.workout.video.RemoteVideo
import kotlin.math.roundToInt

fun RemoteCombinedWorkout.toDomainWorkout(
    mapper: WorkoutRemoteExtensionMapper,
    workoutId: Int = WorkoutHeader.generateIdFromRemote(workoutKey)
): DomainWorkout {
    val workoutAverageHRPercentage =
        if (this.hrData != null && this.hrData!!.absoluteMaximum > 0) {
            this.hrData!!.workoutAverage.toDouble() / this.hrData!!.absoluteMaximum.toDouble() * 100.0
        } else {
            0.toDouble()
        }
    val workoutMaxHRPercentage =
        if (this.hrData != null && this.hrData!!.absoluteMaximum > 0) {
            this.hrData!!.workoutMaximum.toDouble() / this.hrData!!.absoluteMaximum.toDouble() * 100.0
        } else {
            0.toDouble()
        }

    return DomainWorkout(
        header = WorkoutHeader(
            id = workoutId,
            key = this.workoutKey,
            totalDistance = this.totalDistance ?: 0.0,
            maxSpeed = this.maxSpeed ?: 0.0,
            activityTypeId = this.activityId,
            avgSpeed = this.avgSpeed ?: 0.0,
            description = this.description,
            startPosition = this.startPosition?.toPoint(),
            stopPosition = this.stopPosition?.toPoint(),
            centerPosition = this.centerPosition?.toPoint(),
            startTime = this.startTime,
            stopTime = this.stopTime,
            totalTime = this.totalTime,
            energyConsumption = this.energyConsumption ?: 0.0,
            username = this.username,
            heartRateAverage = (this.hrData?.workoutAverage ?: 0).toDouble(),
            heartRateAvgPercentage = workoutAverageHRPercentage,
            heartRateMax = (this.hrData?.workoutMaximum ?: 0).toDouble(),
            heartRateMaxPercentage = workoutMaxHRPercentage,
            heartRateUserSetMax = (this.hrData?.absoluteMaximum ?: 0).toDouble(),
            averageCadence = this.cadence?.avg ?: 0,
            maxCadence = this.cadence?.max ?: 0,
            pictureCount = this.photos?.size ?: 0,
            viewCount = 0,
            commentCount = this.comments?.size ?: 0,
            sharingFlags = this.sharingFlags,
            stepCount = this.stepCount ?: 0,
            // Combined workout API returns an empty string if polyline is missing
            polyline = this.polyline.takeUnless { it.isNullOrEmpty() },
            manuallyAdded = false,
            seen = true,
            reactionCount = this.reactionCount ?: 0,
            totalAscent = this.totalAscent ?: 0.0,
            totalDescent = this.totalDescent ?: 0.0,
            recoveryTime = this.recoveryTime ?: 0L,
            minAltitude = this.minAltitude,
            maxAltitude = this.maxAltitude,
            tss = this.tss?.toDomain(),
            tssList = this.tssList.orEmpty().map(RemoteTSS::toDomain),
            suuntoTags = this.suuntoTags.orEmpty().mapNotNull(RemoteSuuntoTag::toDomain),
            userTags = this.userTagData.orEmpty().map(RemoteUserTag::toDomain)
        ),
        comments = this.comments?.map { it.toDomainWorkoutComment() },
        extensions = this.extensions?.mapNotNull(mapper.toDomainEntity(workoutId))
            ?.associateBy { it::class },
        pictures = this.photos?.map { it.toPicture(workoutId) },
        rankings = this.rankings?.toRanking(this.workoutKey),
        reactions = null,
        videos = this.videos?.map { it.toVideo(workoutId) }
    )
}

fun RemoteSyncedWorkout.toDomainWorkout(
    mapper: WorkoutRemoteExtensionMapper,
    workoutId: Int = WorkoutHeader.generateIdFromRemote(workoutKey)
): DomainWorkout {
    val workoutAverageHRPercentage =
        if (this.hrdata != null && this.hrdata!!.absoluteMaximum > 0) {
            this.hrdata!!.workoutAverage.toDouble() / this.hrdata!!.absoluteMaximum.toDouble() * 100.0
        } else {
            0.toDouble()
        }
    val workoutMaxHRPercentage =
        if (this.hrdata != null && this.hrdata!!.absoluteMaximum > 0) {
            this.hrdata!!.workoutMaximum.toDouble() / this.hrdata!!.absoluteMaximum.toDouble() * 100.0
        } else {
            0.toDouble()
        }

    return DomainWorkout(
        header = WorkoutHeader(
            id = workoutId,
            key = this.workoutKey,
            totalDistance = this.totalDistance ?: 0.0,
            maxSpeed = this.maxSpeed ?: 0.0,
            activityTypeId = this.activityId,
            avgSpeed = this.avgSpeed ?: 0.0,
            description = this.description,
            startPosition = this.startPosition?.toPoint(),
            stopPosition = this.stopPosition?.toPoint(),
            centerPosition = this.centerPosition?.toPoint(),
            startTime = this.startTime,
            stopTime = this.stopTime,
            totalTime = this.totalTime,
            energyConsumption = this.energyConsumption ?: 0.0,
            username = this.username,
            heartRateAverage = (this.hrdata?.workoutAverage ?: 0).toDouble(),
            heartRateAvgPercentage = workoutAverageHRPercentage,
            heartRateMax = (this.hrdata?.workoutMaximum ?: 0).toDouble(),
            heartRateMaxPercentage = workoutMaxHRPercentage,
            heartRateUserSetMax = (this.hrdata?.absoluteMaximum ?: 0).toDouble(),
            averageCadence = this.cadence?.avg ?: 0,
            maxCadence = this.cadence?.max ?: 0,
            pictureCount = this.photos?.size ?: this.pictureCount,
            viewCount = this.viewCount,
            commentCount = this.comments?.size ?: 0,
            sharingFlags = this.sharingFlags,
            stepCount = this.stepCount ?: 0,
            polyline = this.polyline,
            manuallyAdded = this.manuallyAdded ?: false,
            seen = true,
            reactionCount = this.reactions?.map { it.count }?.reduce { total, next -> total + next }
                ?: 0,
            totalAscent = this.totalAscent ?: 0.0,
            totalDescent = this.totalDescent ?: 0.0,
            recoveryTime = this.recoveryTime ?: 0L,
            minAltitude = this.minAltitude,
            maxAltitude = this.maxAltitude,
            tss = this.tss?.toDomain(),
            tssList = this.tssList.orEmpty().map(RemoteTSS::toDomain),
            suuntoTags = this.suuntoTags.orEmpty().mapNotNull(RemoteSuuntoTag::toDomain),
            userTags = this.userTags.orEmpty().map(RemoteUserTag::toDomain),
            zoneSense = this.zoneSense?.toDomain()
        ),
        comments = this.comments?.map { it.toDomainWorkoutComment() },
        extensions = this.extensions?.mapNotNull(mapper.toDomainEntity(workoutId))
            ?.associateBy { it::class },
        pictures = this.photos?.map { it.toPicture(workoutId) },
        rankings = this.rankings?.toRanking(this.workoutKey),
        reactions = this.reactions?.map { it.toReactionSummary(this.workoutKey) },
        videos = this.videos?.map { it.toVideo(workoutId) }
    )
}

fun RemotePoint.toPoint(): Point {
    return Point(this.longitude, this.latitude, this.altitude)
}

fun WorkoutHeader.toRemoteUpdatedWorkout(): RemoteUpdatedWorkout {
    return RemoteUpdatedWorkout(
        workoutKey = this.key ?: throw IllegalStateException("Workout key cannot be null"),
        totalDistance = this.totalDistance,
        activityId = this.activityTypeId,
        description = this.description,
        startTime = this.startTime,
        totalTime = this.totalTime,
        energyConsumption = this.energyConsumption.roundToInt(),
        hrMaxValue = this.heartRateMax.roundToInt(),
        hrAvgValue = this.heartRateAverage.roundToInt(),
        sharingFlags = this.sharingFlags,
        stepCount = this.stepCount,
    )
}

fun RemoteSyncedWorkoutImage.toPicture(workoutId: Int?): Picture {
    return Picture(
        key = this.key,
        location = this.location?.toPoint(),
        timestamp = this.timestamp,
        totalTime = this.totalTime,
        fileName = null,
        workoutId = workoutId,
        workoutKey = this.workoutKey,
        md5Hash = null,
        locallyChanged = false,
        description = this.description,
        username = this.username,
        width = this.width,
        height = this.height,
        indexInWorkoutHeader = 0,
        reviewState = getReviewState(contentReviewStatus)
    )
}

private fun getReviewState(state: Int): ReviewState {
    return when (state) {
        0 -> ReviewState.REVIEWING
        1 -> ReviewState.PASS
        2 -> ReviewState.FAIL
        else -> ReviewState.PASS
    }
}

const val TOTAL_TIME_ON_ROUTE_RANKING = "totalTimeOnRouteRanking"

fun RemoteSyncedWorkoutRankings.toRanking(workoutKey: String): Ranking {
    return Ranking(
        key = workoutKey,
        type = TOTAL_TIME_ON_ROUTE_RANKING,
        ranking = this.totalTimeOnRouteRanking.originalRanking,
        numberOfWorkouts = this.totalTimeOnRouteRanking.originalNumberOfWorkouts
    )
}

fun RemoteSyncedWorkoutReaction.toReactionSummary(workoutKey: String): DomainReactionSummary {
    return DomainReactionSummary(
        id = 31L * workoutKey.hashCode() + this.utfCode.hashCode(), // copied from legacy code
        workoutKey = workoutKey,
        reaction = this.utfCode,
        count = this.count,
        userReacted = this.userReacted
    )
}

fun RemoteVideo.toVideo(workoutId: Int?): Video {
    return Video(
        key = this.key,
        workoutId = workoutId,
        workoutKey = this.workoutKey,
        username = this.username,
        totalTime = this.totalTime,
        timestamp = this.timestamp,
        description = this.description,
        location = this.location?.toPoint(),
        url = this.url,
        thumbnailUrl = this.thumbnailUrl,
        width = this.width,
        height = this.height,
        filename = null,
        thumbnailFilename = null,
        locallyChanged = false,
        reviewState = getReviewState(this.reviewState)
    )
}

fun RemotePicture.toPicture(workoutId: Int?, indexInWorkoutHeader: Int): Picture {
    return Picture(
        key = this.key,
        location = this.location?.toPoint(),
        timestamp = this.timestamp,
        totalTime = this.totalTime,
        fileName = null,
        workoutId = workoutId,
        workoutKey = this.workoutKey,
        md5Hash = null,
        locallyChanged = false,
        description = this.description,
        username = this.username,
        width = this.width,
        height = this.height,
        indexInWorkoutHeader = indexInWorkoutHeader,
        reviewState = getReviewState(this.reviewState)
    )
}

fun RemoteTSS.toDomain(): TSS = TSS(
    trainingStressScore,
    calculationMethod.toDomain(),
    intensityFactor,
    normalizedPower,
    averageGradeAdjustedPace
)

fun RemoteZoneSense.toDomain(): ZoneSense {
    return ZoneSense(
        aerobicBaseline = aerobicBaseline
    )
}

fun RemoteTSSCalculationMethod.toDomain(): TSSCalculationMethod = when (this) {
    RemoteTSSCalculationMethod.POWER -> TSSCalculationMethod.POWER
    RemoteTSSCalculationMethod.PACE -> TSSCalculationMethod.PACE
    RemoteTSSCalculationMethod.HR -> TSSCalculationMethod.HR
    RemoteTSSCalculationMethod.SWIM_PACE -> TSSCalculationMethod.SWIM_PACE
    RemoteTSSCalculationMethod.MET -> TSSCalculationMethod.MET
    RemoteTSSCalculationMethod.MANUAL -> TSSCalculationMethod.MANUAL
    RemoteTSSCalculationMethod.DYNAMIC_DFA -> TSSCalculationMethod.DYNAMIC_DFA
}

fun RemoteWorkoutStats.toDomain(): WorkoutStats = WorkoutStats(
    totalDistanceSum = totalDistanceSum,
    totalTimeSum = totalTimeSum,
    totalEnergyConsumptionKCalSum = totalEnergyConsumptionKCalSum,
    totalNumberOfWorkoutsSum = totalNumberOfWorkoutsSum,
    totalDays = totalDays ?: 0,
    activityTypeStats = activityTypeStats.map { it.toDomain() }
)

fun RemoteActivityTypeStats.toDomain(): ActivityTypeStats = ActivityTypeStats(
    activityId = activityId,
    totalDistance = totalDistance,
    totalTime = totalTime,
    energyConsumptionKCal = energyConsumptionKCal,
    numberOfWorkouts = numberOfWorkouts
)

fun RemoteCompetitionWorkoutDetails.toDomain(): CompetitionWorkoutSummary = CompetitionWorkoutSummary(
    competition = competition?.toDomain(),
    status = status,
    displayedUserName = showUserName,
)

fun RemoteCompetitionResult.toDomain(): CompetitionResult = CompetitionResult(
    workoutId,
    username,
    distance,
    result,
    finishDuration,
    targetDuration,
    workoutOwner,
)

@JvmOverloads
fun LocalWorkoutHeader.toDomain(
    userTags: List<LocalUserTag>? = null
) = WorkoutHeader(
    id = id,
    key = key,
    totalDistance = totalDistance,
    maxSpeed = maxSpeed,
    activityTypeId = activityId,
    avgSpeed = avgSpeed,
    description = description,
    startPosition = startPosition?.toPoint(),
    stopPosition = stopPosition?.toPoint(),
    centerPosition = centerPosition?.toPoint(),
    startTime = startTime,
    stopTime = stopTime,
    totalTime = totalTime,
    energyConsumption = energyConsumption,
    username = username,
    heartRateAverage = heartRateAvg,
    heartRateAvgPercentage = heartRateAvgPercentage,
    heartRateMax = heartRateMax,
    heartRateMaxPercentage = heartRateMaxPercentage,
    heartRateUserSetMax = heartRateUserSetMax,
    averageCadence = averageCadence,
    maxCadence = maxCadence,
    pictureCount = pictureCount,
    viewCount = viewCount,
    commentCount = commentCount,
    sharingFlags = sharingFlags,
    stepCount = stepCount,
    polyline = polyline,
    manuallyAdded = manuallyCreated,
    reactionCount = reactionCount,
    totalAscent = totalAscent,
    totalDescent = totalDescent,
    recoveryTime = recoveryTime,
    locallyChanged = locallyChanged,
    deleted = deleted,
    seen = seen,
    maxAltitude = maxAltitude,
    minAltitude = minAltitude,
    extensionsFetched = extensionsFetched,
    tss = tss?.toDomain(),
    tssList = tssList.orEmpty().map(LocalTSS::toDomain),
    suuntoTags = suuntoTags?.map { it.toSuuntoTag() }.orEmpty(),
    userTags = userTags.orEmpty().map(LocalUserTag::toDomain),
    zoneSense = zoneSense?.toDomain(),
)

fun LocalTSS.toDomain() = TSS(
    trainingStressScore = trainingStressScore,
    calculationMethod = calculationMethod.toDomain(),
    intensityFactor = intensityFactor,
    normalizedPower = normalizedPower,
    averageGradeAdjustedPace = averageGradeAdjustedPace
)

private fun LocalTSSCalculationMethod.toDomain() = when (this) {
    LocalTSSCalculationMethod.POWER -> TSSCalculationMethod.POWER
    LocalTSSCalculationMethod.PACE -> TSSCalculationMethod.PACE
    LocalTSSCalculationMethod.HR -> TSSCalculationMethod.HR
    LocalTSSCalculationMethod.SWIM_PACE -> TSSCalculationMethod.SWIM_PACE
    LocalTSSCalculationMethod.MET -> TSSCalculationMethod.MET
    LocalTSSCalculationMethod.MANUAL -> TSSCalculationMethod.MANUAL
    LocalTSSCalculationMethod.DYNAMIC_DFA -> TSSCalculationMethod.DYNAMIC_DFA
}

fun WorkoutHeader.toLocal() = LocalWorkoutHeader(
    id = id,
    key = key,
    totalDistance = totalDistance,
    maxSpeed = maxSpeed,
    activityId = activityTypeId,
    avgSpeed = avgSpeed,
    description = description,
    startPosition = startPosition?.toLocalPoint(),
    stopPosition = stopPosition?.toLocalPoint(),
    centerPosition = centerPosition?.toLocalPoint(),
    startTime = startTime,
    stopTime = stopTime,
    totalTime = totalTime,
    energyConsumption = energyConsumption,
    username = username,
    heartRateAvg = heartRateAverage,
    heartRateAvgPercentage = heartRateAvgPercentage,
    heartRateMax = heartRateMax,
    heartRateMaxPercentage = heartRateMaxPercentage,
    heartRateUserSetMax = heartRateUserSetMax,
    averageCadence = averageCadence,
    maxCadence = maxCadence,
    pictureCount = pictureCount,
    viewCount = viewCount,
    commentCount = commentCount,
    sharingFlags = sharingFlags,
    stepCount = stepCount,
    polyline = polyline,
    manuallyCreated = manuallyAdded,
    reactionCount = reactionCount,
    totalAscent = totalAscent,
    totalDescent = totalDescent,
    recoveryTime = recoveryTime,
    locallyChanged = locallyChanged,
    deleted = deleted,
    seen = seen,
    maxAltitude = maxAltitude,
    minAltitude = minAltitude,
    extensionsFetched = extensionsFetched,
    tss = tss?.toLocal(),
    tssList = tssList.map { it.toLocal() },
    suuntoTags = suuntoTags.map { it.toLocalSuuntoTag() }.takeUnless(List<*>::isEmpty),
    zoneSense = zoneSense?.toLocal(),
)

fun TSS.toLocal() = LocalTSS(
    trainingStressScore = trainingStressScore,
    calculationMethod = calculationMethod.toLocal(),
    intensityFactor = intensityFactor,
    normalizedPower = normalizedPower,
    averageGradeAdjustedPace = averageGradeAdjustedPace
)

private fun TSSCalculationMethod.toLocal() = when (this) {
    TSSCalculationMethod.POWER -> LocalTSSCalculationMethod.POWER
    TSSCalculationMethod.PACE -> LocalTSSCalculationMethod.PACE
    TSSCalculationMethod.HR -> LocalTSSCalculationMethod.HR
    TSSCalculationMethod.SWIM_PACE -> LocalTSSCalculationMethod.SWIM_PACE
    TSSCalculationMethod.MET -> LocalTSSCalculationMethod.MET
    TSSCalculationMethod.MANUAL -> LocalTSSCalculationMethod.MANUAL
    TSSCalculationMethod.DYNAMIC_DFA -> LocalTSSCalculationMethod.DYNAMIC_DFA
}

fun RemoteSuuntoTag.toDomain(): SuuntoTag? = when (this) {
    RemoteSuuntoTag.COMMUTE -> SuuntoTag.COMMUTE
    RemoteSuuntoTag.MARATHON -> SuuntoTag.MARATHON
    RemoteSuuntoTag.HALF_MARATHON -> SuuntoTag.HALF_MARATHON
    RemoteSuuntoTag.IMPACT_SPEED_AND_AGILITY -> SuuntoTag.IMPACT_SPEED_AND_AGILITY
    RemoteSuuntoTag.IMPACT_SPEED_AND_STRENGTH -> SuuntoTag.IMPACT_SPEED_AND_STRENGTH
    RemoteSuuntoTag.IMPACT_FLEXIBILITY -> SuuntoTag.IMPACT_FLEXIBILITY
    RemoteSuuntoTag.IMPACT_STRENGTH -> SuuntoTag.IMPACT_STRENGTH
    RemoteSuuntoTag.IMPACT_ABOVE_THRESHOLD_VO2MAX -> SuuntoTag.IMPACT_ABOVE_THRESHOLD_VO2MAX
    RemoteSuuntoTag.IMPACT_HARD_ANAEROBIC_EFFORT -> SuuntoTag.IMPACT_HARD_ANAEROBIC_EFFORT
    RemoteSuuntoTag.IMPACT_ANAEROBIC_THRESHOLD -> SuuntoTag.IMPACT_ANAEROBIC_THRESHOLD
    RemoteSuuntoTag.IMPACT_AEROBIC_TO_ANAEROBIC -> SuuntoTag.IMPACT_AEROBIC_TO_ANAEROBIC
    RemoteSuuntoTag.IMPACT_HARD_LONG_AEROBIC_BASE -> SuuntoTag.IMPACT_HARD_LONG_AEROBIC_BASE
    RemoteSuuntoTag.IMPACT_LONG_AEROBIC_BASE -> SuuntoTag.IMPACT_LONG_AEROBIC_BASE
    RemoteSuuntoTag.IMPACT_AEROBIC -> SuuntoTag.IMPACT_AEROBIC
    RemoteSuuntoTag.IMPACT_EASY_RECOVERY -> SuuntoTag.IMPACT_EASY_RECOVERY
    RemoteSuuntoTag.UNKNOWN -> null
}

fun LocalUserWorkoutSummaryByActivityType.toDomain() = UserWorkoutSummaryByActivity(
    totalWorkouts = totalWorkouts,
    totalDistance = totalDistance,
    totalDuration = totalDuration,
    totalAscent = totalAscent,
    totalDescent = totalDescent,
    totalEnergy = totalEnergy,
)

fun LocalWorkoutFeeling.toDomain() = WorkoutFeeling(workoutId, feeling, timestamp)

fun LocalBasicWorkoutHeader.toDomain() = BasicWorkoutHeader(
    id = id,
    key = key,
    totalDistance = totalDistance,
    activityTypeId = activityTypeId,
    startTime = startTime,
    totalTime = totalTime,
    username = username,
    totalAscent = totalAscent,
    totalDescent = totalDescent,
    extensionsFetched = extensionsFetched,
    tss = tss?.toDomain(),
    energyConsumption = energyConsumption,
)

fun toNewLocalWorkoutData(
    header: WorkoutHeader,
    extension: SummaryExtension
): LocalWeChatWorkoutData {
    return LocalWeChatWorkoutData(
        id = header.id,
        key = header.key,
        totalDistance = header.totalDistance,
        maxSpeed = header.maxSpeed,
        activityId = header.activityTypeId,
        avgSpeed = header.avgSpeed,
        description = header.description,
        startPosition = header.startPosition?.toLocalPoint(),
        stopPosition = header.stopPosition?.toLocalPoint(),
        centerPosition = header.centerPosition?.toLocalPoint(),
        startTime = header.startTime,
        stopTime = header.stopTime,
        totalTime = header.totalTime,
        energyConsumption = header.energyConsumption,
        username = header.username,
        heartRateAvg = header.heartRateAverage,
        heartRateAvgPercentage = header.heartRateAvgPercentage,
        heartRateMax = header.heartRateMax,
        heartRateMaxPercentage = header.heartRateMaxPercentage,
        heartRateUserSetMax = header.heartRateUserSetMax,
        averageCadence = header.averageCadence,
        maxCadence = header.maxCadence,
        pictureCount = header.pictureCount,
        viewCount = header.viewCount,
        commentCount = header.commentCount,
        sharingFlags = header.sharingFlags,
        stepCount = header.stepCount,
        polyline = header.polyline,
        manuallyCreated = header.manuallyAdded,
        reactionCount = header.reactionCount,
        totalAscent = header.totalAscent,
        totalDescent = header.totalDescent,
        recoveryTime = header.recoveryTime,
        locallyChanged = header.locallyChanged,
        deleted = header.deleted,
        seen = header.seen,
        maxAltitude = header.maxAltitude,
        minAltitude = header.minAltitude,
        extensionsFetched = header.extensionsFetched,
        tss = header.tss?.toLocal(),
        tssList = header.tssList.map { it.toLocal() },
        isCommute = header.suuntoTags.contains(SuuntoTag.COMMUTE),
        deviceName = extension.deviceName,
        deviceSerial = extension.deviceSerialNumber,
        deviceDisplayName = extension.displayName,
        deviceHardwareVersion = extension.deviceHardwareVersion,
        deviceSoftwareVersion = extension.deviceSoftwareVersion,
        productType = extension.productType,
        deviceManufacturer = extension.deviceManufacturer,
        syncStatus = 0,
        skipsCount = extension.repetitionCount,
    )
}

fun toNewWorkoutData(localWeChatWorkoutData: LocalWeChatWorkoutData): WeChatWorkoutData {
    return WeChatWorkoutData(
        id = localWeChatWorkoutData.id,
        key = localWeChatWorkoutData.key,
        totalDistance = localWeChatWorkoutData.totalDistance,
        maxSpeed = localWeChatWorkoutData.maxSpeed,
        activityTypeId = localWeChatWorkoutData.activityId,
        avgSpeed = localWeChatWorkoutData.avgSpeed,
        description = localWeChatWorkoutData.description,
        startPosition = localWeChatWorkoutData.startPosition?.toPoint(),
        stopPosition = localWeChatWorkoutData.stopPosition?.toPoint(),
        centerPosition = localWeChatWorkoutData.centerPosition?.toPoint(),
        startTime = localWeChatWorkoutData.startTime,
        stopTime = localWeChatWorkoutData.stopTime,
        totalTime = localWeChatWorkoutData.totalTime,
        energyConsumption = localWeChatWorkoutData.energyConsumption,
        username = localWeChatWorkoutData.username,
        heartRateAverage = localWeChatWorkoutData.heartRateAvg,
        heartRateAvgPercentage = localWeChatWorkoutData.heartRateAvgPercentage,
        heartRateMax = localWeChatWorkoutData.heartRateMax,
        heartRateMaxPercentage = localWeChatWorkoutData.heartRateMaxPercentage,
        heartRateUserSetMax = localWeChatWorkoutData.heartRateUserSetMax,
        averageCadence = localWeChatWorkoutData.averageCadence,
        maxCadence = localWeChatWorkoutData.maxCadence,
        pictureCount = localWeChatWorkoutData.pictureCount,
        viewCount = localWeChatWorkoutData.viewCount,
        commentCount = localWeChatWorkoutData.commentCount,
        sharingFlags = localWeChatWorkoutData.sharingFlags,
        stepCount = localWeChatWorkoutData.stepCount,
        polyline = localWeChatWorkoutData.polyline,
        manuallyAdded = localWeChatWorkoutData.manuallyCreated,
        reactionCount = localWeChatWorkoutData.reactionCount,
        totalAscent = localWeChatWorkoutData.totalAscent,
        totalDescent = localWeChatWorkoutData.totalDescent,
        recoveryTime = localWeChatWorkoutData.recoveryTime,
        locallyChanged = localWeChatWorkoutData.locallyChanged,
        deleted = localWeChatWorkoutData.deleted,
        seen = localWeChatWorkoutData.seen,
        maxAltitude = localWeChatWorkoutData.maxAltitude,
        minAltitude = localWeChatWorkoutData.minAltitude,
        extensionsFetched = localWeChatWorkoutData.extensionsFetched,
        tss = localWeChatWorkoutData.tss?.toDomain(),
        tssList = localWeChatWorkoutData.tssList.orEmpty().map(LocalTSS::toDomain),
        suuntoTags = buildList { if (localWeChatWorkoutData.isCommute) add(SuuntoTag.COMMUTE) },
        deviceName = localWeChatWorkoutData.deviceName,
        deviceSerial = localWeChatWorkoutData.deviceSerial,
        deviceDisplayName = localWeChatWorkoutData.deviceDisplayName,
        deviceHardwareVersion = localWeChatWorkoutData.deviceHardwareVersion,
        deviceSoftwareVersion = localWeChatWorkoutData.deviceSoftwareVersion,
        productType = localWeChatWorkoutData.productType,
        deviceManufacturer = localWeChatWorkoutData.deviceManufacturer,
        syncStatus = localWeChatWorkoutData.syncStatus,
        userTags = arrayListOf(),
        skipsCount = localWeChatWorkoutData.skipsCount,
    )
}

fun SuuntoTag.toLocalSuuntoTag(): LocalSuuntoTag = when (this) {
    SuuntoTag.COMMUTE -> LocalSuuntoTag.COMMUTE
    SuuntoTag.MARATHON -> LocalSuuntoTag.MARATHON
    SuuntoTag.HALF_MARATHON -> LocalSuuntoTag.HALF_MARATHON
    SuuntoTag.IMPACT_SPEED_AND_AGILITY -> LocalSuuntoTag.IMPACT_SPEED_AND_AGILITY
    SuuntoTag.IMPACT_SPEED_AND_STRENGTH -> LocalSuuntoTag.IMPACT_SPEED_AND_STRENGTH
    SuuntoTag.IMPACT_FLEXIBILITY -> LocalSuuntoTag.IMPACT_FLEXIBILITY
    SuuntoTag.IMPACT_STRENGTH -> LocalSuuntoTag.IMPACT_STRENGTH
    SuuntoTag.IMPACT_ABOVE_THRESHOLD_VO2MAX -> LocalSuuntoTag.IMPACT_ABOVE_THRESHOLD_VO2MAX
    SuuntoTag.IMPACT_HARD_ANAEROBIC_EFFORT -> LocalSuuntoTag.IMPACT_HARD_ANAEROBIC_EFFORT
    SuuntoTag.IMPACT_ANAEROBIC_THRESHOLD -> LocalSuuntoTag.IMPACT_ANAEROBIC_THRESHOLD
    SuuntoTag.IMPACT_AEROBIC_TO_ANAEROBIC -> LocalSuuntoTag.IMPACT_AEROBIC_TO_ANAEROBIC
    SuuntoTag.IMPACT_HARD_LONG_AEROBIC_BASE -> LocalSuuntoTag.IMPACT_HARD_LONG_AEROBIC_BASE
    SuuntoTag.IMPACT_LONG_AEROBIC_BASE -> LocalSuuntoTag.IMPACT_LONG_AEROBIC_BASE
    SuuntoTag.IMPACT_AEROBIC -> LocalSuuntoTag.IMPACT_AEROBIC
    SuuntoTag.IMPACT_EASY_RECOVERY -> LocalSuuntoTag.IMPACT_EASY_RECOVERY
}

fun LocalSuuntoTag.toSuuntoTag(): SuuntoTag = when (this) {
    LocalSuuntoTag.COMMUTE -> SuuntoTag.COMMUTE
    LocalSuuntoTag.MARATHON -> SuuntoTag.MARATHON
    LocalSuuntoTag.HALF_MARATHON -> SuuntoTag.HALF_MARATHON
    LocalSuuntoTag.IMPACT_SPEED_AND_AGILITY -> SuuntoTag.IMPACT_SPEED_AND_AGILITY
    LocalSuuntoTag.IMPACT_SPEED_AND_STRENGTH -> SuuntoTag.IMPACT_SPEED_AND_STRENGTH
    LocalSuuntoTag.IMPACT_FLEXIBILITY -> SuuntoTag.IMPACT_FLEXIBILITY
    LocalSuuntoTag.IMPACT_STRENGTH -> SuuntoTag.IMPACT_STRENGTH
    LocalSuuntoTag.IMPACT_ABOVE_THRESHOLD_VO2MAX -> SuuntoTag.IMPACT_ABOVE_THRESHOLD_VO2MAX
    LocalSuuntoTag.IMPACT_HARD_ANAEROBIC_EFFORT -> SuuntoTag.IMPACT_HARD_ANAEROBIC_EFFORT
    LocalSuuntoTag.IMPACT_ANAEROBIC_THRESHOLD -> SuuntoTag.IMPACT_ANAEROBIC_THRESHOLD
    LocalSuuntoTag.IMPACT_AEROBIC_TO_ANAEROBIC -> SuuntoTag.IMPACT_AEROBIC_TO_ANAEROBIC
    LocalSuuntoTag.IMPACT_HARD_LONG_AEROBIC_BASE -> SuuntoTag.IMPACT_HARD_LONG_AEROBIC_BASE
    LocalSuuntoTag.IMPACT_LONG_AEROBIC_BASE -> SuuntoTag.IMPACT_LONG_AEROBIC_BASE
    LocalSuuntoTag.IMPACT_AEROBIC -> SuuntoTag.IMPACT_AEROBIC
    LocalSuuntoTag.IMPACT_EASY_RECOVERY -> SuuntoTag.IMPACT_EASY_RECOVERY
}

fun LocalSummaryWorkoutHeader.toDomain(
    summaryItems: List<SummaryItem>,
    supportsDistance: Boolean, // to avoid duplicating the logic of checking for distance that exists in ActivitySummary
    supportsAscent: Boolean
) = SummaryWorkoutHeader(
    id = id,
    key = key,
    username = username,
    totalDistance = totalDistance,
    activityTypeId = activityId,
    startTime = startTime,
    totalTime = run {
        val activityType = ActivityType.valueOf(activityId)
        if (activityType.isDiving) {
            divePauseDuration?.let {
                totalTime - it.toLong()
            } ?: totalTime
        } else {
            totalTime
        }
    },
    totalAscent = totalAscent,
    avgSpeed = run {
        val activityType = ActivityType.valueOf(activityId)
        if (activityType.isSwimming && activityType.isIndoor) {
            val summarySpeed = summaryAvgSpeed ?: 0f
            if (summarySpeed > 0f) summarySpeed.toDouble() else avgSpeed
        } else {
            avgSpeed
        }
    },
    heartRateAverage = heartRateAvg,
    energyConsumption = energyConsumption,
    tss = tss?.toDomain(),
    vo2Max = vo2Max,
    avgPower = summaryAvgPower,
    normalizedPower = run {
        val avgPower = summaryAvgPower
        if (avgPower != null && avgPower > 0.0) {
            tss?.normalizedPower ?: tssList?.firstNotNullOfOrNull { it.normalizedPower }
        } else {
            null
        }
    },
    supportsDistance = supportsDistance,
    supportsAvgSpeed = summaryItems.contains(SummaryItem.AVGSPEED),
    supportsAvgPace = summaryItems.contains(SummaryItem.AVGPACE),
    supportsAvgHeartRate = summaryItems.contains(SummaryItem.AVGHEARTRATE),
    supportsAscent = supportsAscent,
    supportsEnergyConsumption = summaryItems.contains(SummaryItem.ENERGY),
    supportsTss = summaryItems.contains(SummaryItem.TRAININGSTRESSSCORE),
    supportsVo2Max = summaryItems.contains(SummaryItem.ESTVO2PEAK),
    supportsAvgPower = summaryItems.contains(SummaryItem.AVGPOWER),
    supportsNormalizedPower = summaryItems.contains(SummaryItem.NORMALIZEDPOWER),
    supportsAvgSwimPace = summaryItems.contains(SummaryItem.AVGSWIMPACE),
)

fun LocalZoneSense.toDomain(): ZoneSense {
    return ZoneSense(aerobicBaseline = aerobicBaseline)
}

fun ZoneSense.toLocal(): LocalZoneSense {
    return LocalZoneSense(
        aerobicBaseline = aerobicBaseline
    )
}

fun LocalZoneSenseSyncWorkoutHeader.toDomain(): ZoneSenseSyncWorkoutHeader {
    return ZoneSenseSyncWorkoutHeader(
        activityTypeId = activityId,
        zoneSenseAerobicBaseline = this.zoneSense.aerobicBaseline
    )
}
