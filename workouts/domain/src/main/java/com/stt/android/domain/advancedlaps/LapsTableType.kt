package com.stt.android.domain.advancedlaps

enum class LapsTableType {
    MANUAL,
    INTERVAL,
    DISTANCE_AUTO_LAP,
    DURATION_AUTO_LAP,
    ONE_KM_AUTO_LAP,
    FIVE_KM_AUTO_LAP,
    TEN_KM_AUTO_LAP,
    ONE_MILE_AUTO_LAP,
    FIVE_MILE_AUTO_LAP,
    TEN_MILE_AUTO_LAP,
    DOWNHILL,
    DIVE;

    fun isAutoGeneratedDistanceLapTable(): <PERSON><PERSON><PERSON> {
        return when (this) {
            DISTANCE_AUTO_LAP,
            ONE_KM_AUTO_LAP,
            FIVE_KM_AUTO_LAP,
            TEN_KM_AUTO_LAP,
            ONE_MILE_AUTO_LAP,
            FIVE_MILE_AUTO_LAP,
            TEN_MILE_AUTO_LAP -> true

            else -> false
        }
    }

    fun isAutoGeneratedDurationLapTable(): <PERSON><PERSON><PERSON> {
        return when (this) {
            DURATION_AUTO_LAP -> true
            else -> false
        }
    }
}
