package com.stt.android.domain.workouts.competition

import android.os.Parcelable
import com.stt.android.logbook.CompetitionResult
import kotlinx.parcelize.Parcelize

@Parcelize
data class CompetitionWorkoutSummary(
    val competition: CompetitionResult?,
    val status: String,
    val displayedUserName: String?,
) : Parcelable

data class CompetitionWorkoutSummaryData(
    val summary: CompetitionWorkoutSummary,
    val onCompetitionWorkoutClick: () -> Unit
)

enum class CompetitionStatus {
    NOT_IN_COMPETITION,
    DELETED,
    OK,
    FORBIDDEN,
}
