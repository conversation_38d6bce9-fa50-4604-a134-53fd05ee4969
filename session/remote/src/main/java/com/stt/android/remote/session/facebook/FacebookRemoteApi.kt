package com.stt.android.remote.session.facebook

import androidx.core.os.bundleOf
import com.facebook.AccessToken
import com.facebook.GraphRequest
import com.facebook.GraphResponse
import com.stt.android.billing.Base64
import com.stt.android.exceptions.remote.STTError
import com.stt.android.exceptions.remote.facebook.FacebookSignInException
import com.stt.android.remote.session.RemoteNewUserCredentials
import com.stt.android.remote.session.RemoteSex
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.suspendCancellableCoroutine
import org.json.JSONObject
import timber.log.Timber
import java.security.SecureRandom
import java.time.LocalDate
import javax.inject.Inject
import kotlin.coroutines.resumeWithException

class FacebookRemoteApi
@Inject constructor() {
    suspend fun fetchFacebookInfo(): RemoteNewUserCredentials =
        suspendCancellableCoroutine { continuation: CancellableContinuation<RemoteNewUserCredentials> ->
            val request: GraphRequest = GraphRequest.newMeRequest(
                AccessToken.getCurrentAccessToken()
            ) { jsonObject: JSONObject?, response: GraphResponse? ->
                when {
                    response == null || response.error != null -> {
                        val error = response?.error?.exception
                            ?: RuntimeException("Null response from facebook API")
                        Timber.w(error, "Error during facebook fetch info")
                        continuation.resumeWithException(error)
                    }
                    jsonObject == null -> {
                        continuation.resumeWithException(
                            FacebookSignInException("Empty facebook info")
                        )
                    }
                    else -> {
                        continuation.resumeWith(
                            runCatching {
                                val newUserCredentials: RemoteNewUserCredentials =
                                    createNewUserCredentials(jsonObject)
                                if (newUserCredentials.email.isNullOrBlank()) {
                                    throw STTError.MissingEmail()
                                } else {
                                    newUserCredentials
                                }
                            }
                        )
                    }
                }
            }
            request.parameters = bundleOf(
                "fields" to "id,first_name,last_name,email,gender,birthday"
            )
            val executeAsync = request.executeAsync()
            continuation.invokeOnCancellation {
                // can remove when facebook SDK stops using async tasks
                @Suppress("DEPRECATION")
                executeAsync.cancel(false)
            }
        }

    private fun createNewUserCredentials(jsonObject: JSONObject): RemoteNewUserCredentials {
        val email = jsonObject.optString("email")
        val firstName = jsonObject.optString("first_name")
        val lastName = jsonObject.optString("last_name")
        // you can set a custom string as your gender in Facebook...
        val gender = jsonObject.optString("gender")
        val sex: RemoteSex? = when (gender) {
            "female" -> RemoteSex.FEMALE
            "male" -> RemoteSex.MALE
            else -> null
        }
        val birthdayFields = jsonObject.optString("birthday")
            .takeIf { it.isNotBlank() }
            ?.split("/")
            ?.toTypedArray()
        val birthday: LocalDate =
            if (birthdayFields == null || birthdayFields.size != 3) {
                LocalDate.now().minusYears(DEFAULT_FACEBOOK_AGE)
            } else {
                LocalDate.of(
                    Integer.valueOf(birthdayFields[2]),
                    Integer.valueOf(birthdayFields[0]),
                    Integer.valueOf(birthdayFields[1])
                )
            }
        val accessToken = AccessToken.getCurrentAccessToken()?.token
        return RemoteNewUserCredentials(
            fullName = "$firstName $lastName",
            password = generateRandomPassword(),
            email = email,
            sex = sex,
            birthday = birthday,
            facebookAccessToken = accessToken,
            phoneNumber = null
        )
    }

    private fun generateRandomPassword(): String? {
        val secureRandom = SecureRandom()
        val tmp = ByteArray(16)
        secureRandom.nextBytes(tmp)
        return Base64.encodeWebSafe(tmp, true)
    }

    companion object {
        const val DEFAULT_FACEBOOK_AGE = 30L
    }
}
