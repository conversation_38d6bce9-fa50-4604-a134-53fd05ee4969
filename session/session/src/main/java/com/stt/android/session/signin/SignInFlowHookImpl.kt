package com.stt.android.session.signin

import android.content.Context
import android.content.Intent
import com.stt.android.session.EXTRA_ON_SUCCESS_LOGIN_INTENT
import com.stt.android.session.SignInFlowHook
import javax.inject.Inject

class SignIn<PERSON>lowHookImpl
@Inject constructor() : SignIn<PERSON><PERSON>Hook {
    override fun newStartIntent(context: Context, onSuccessLoginIntent: Intent?): Intent {
        return Intent(context, SignInActivity::class.java).apply {
            putExtra(EXTRA_ON_SUCCESS_LOGIN_INTENT, onSuccessLoginIntent)
        }
    }
}
