package com.stt.android.session

import android.os.SystemClock
import android.util.Patterns
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.liveData
import com.google.i18n.phonenumbers.NumberParseException
import com.google.i18n.phonenumbers.Phonenumber
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.domain.session.LoginMethod
import com.stt.android.domain.session.facebook.NewUserCredentials
import com.stt.android.domain.session.facebook.Sex
import com.stt.android.lifecycle.mapAndObserve
import com.stt.android.session.configuration.SignInConfiguration
import com.stt.android.session.phonenumberverification.IsValidPhoneNumberUseCase
import dagger.hilt.android.scopes.ViewModelScoped
import kotlinx.coroutines.delay
import timber.log.Timber
import java.time.Duration
import java.time.LocalDate
import javax.inject.Inject
import javax.inject.Named
import com.stt.android.R as BaseR

/**
 * LiveData object for all user input fields in the sign-up or login flow.
 *
 * A common instance of this object is used by [SignInOnboardingViewModel] and the related delegates
 */
@ViewModelScoped
class SignInUserDataImpl
@Inject constructor(
    config: SignInConfiguration,
    @Named("currentCountryCode") val currentCountryCode: Phonenumber.PhoneNumber,
    private val isValidPhoneNumberUseCase: IsValidPhoneNumberUseCase,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) : SignInUserData {

    private var verificationCodeSentTimestamp: Long? = null

    private val defaultCountryCode: String
        get() = "+${currentCountryCode.countryCode}"

    private val defaultNationalNumber: String
        get() = currentCountryCode.nationalNumber.let {
            if (it == 0L) "" else it.toString()
        }

    override val fullName: MutableLiveData<String> = MutableLiveData("")
    override val rawEmailOrUsername: MutableLiveData<String> = MutableLiveData("")
    override val emailOrUsername: LiveData<String> =
        rawEmailOrUsername.mapAndObserve {
            it.trim()
        }
    override val password: MutableLiveData<String> = MutableLiveData("")
    override val verificationToken: MutableLiveData<String> = MutableLiveData("")
    override val loginMethod: MutableLiveData<LoginMethod> = MutableLiveData(config.defaultLogin)
    override val phoneRegion = MutableLiveData(defaultCountryCode)
    override val nationalNumber = MutableLiveData(defaultNationalNumber)
    override val sex: MutableLiveData<Sex?> = MutableLiveData()
    override val birthday: MutableLiveData<LocalDate?> = MutableLiveData()
    override val facebookAccessToken: MutableLiveData<String?> = MutableLiveData()
    override val phoneNumberVerificationCode = MutableLiveData("")
    override val phoneNumberVerificationToken = MutableLiveData<String>()
    override val emailExistsInMovescount = MutableLiveData(false)
    override val activeBrandForEmail = MutableLiveData<String>()
    override val emailValid = emailOrUsername.mapAndObserve {
        Patterns.EMAIL_ADDRESS.matcher(it).matches()
    }
    override val termscode: MutableLiveData<String> = MutableLiveData<String>()

    override val signUpPasswordState: LiveData<SignInUserData.SignUpPasswordState> =
        password.mapAndObserve {
            when {
                it.isNullOrEmpty() ->
                    SignInUserData.SignUpPasswordState.EMPTY
                GOOD_PASSWORD_MATCHER.matches(it) ->
                    SignInUserData.SignUpPasswordState.GOOD_PASSWORD
                MINIMUM_ACCEPTABLE_PASSWORD_MATCHER.matches(it) ->
                    SignInUserData.SignUpPasswordState.WEAK_PASSWORD
                else ->
                    SignInUserData.SignUpPasswordState.BAD_PASSWORD
            }
        }

    override val phoneRegionSelectionEnabled: Boolean = config.defaultPhoneNumberCountryCode == null

    override fun resetForNewLoginMethod(newLoginMethod: LoginMethod) {
        loginMethod.value = newLoginMethod
        phoneRegion.value = defaultCountryCode
        nationalNumber.value = defaultNationalNumber
        sequenceOf(
            rawEmailOrUsername,
            fullName,
            password,
            phoneNumberVerificationCode
        ).forEach { it.value = "" }
        sequenceOf(
            sex,
            birthday,
            facebookAccessToken,
            phoneNumberVerificationToken,
            activeBrandForEmail
        ).forEach { it.value = null }
        emailExistsInMovescount.value = false
    }

    override fun setupWithNewUserCredentials(credentials: NewUserCredentials) {
        fullName.value = credentials.fullName ?: ""
        password.value = credentials.password ?: ""
        rawEmailOrUsername.value = credentials.email ?: ""
        sex.value = credentials.sex
        birthday.value = credentials.birthday
        facebookAccessToken.value = credentials.facebookAccessToken
    }

    override fun validateAndGetPhoneNumberInputError(): InputError {
        val phoneValueText = phoneNumberValue?.trim()
        if (phoneValueText.isNullOrEmpty()) {
            return InputError(BaseR.string.required)
        }

        return isValidPhoneNumberUseCase.run(phoneValueText)
            .map { valid ->
                if (valid) {
                    InputError.None
                } else {
                    Timber.d("Invalid phone number '$phoneValueText'")
                    trackInvalidPhoneNumberEvent(firebaseAnalyticsTracker, amplitudeAnalyticsTracker)
                    InputError(BaseR.string.phone_error)
                }
            }
            .getOrElse { e ->
                Timber.d(e, "Failed to validate phone number '$phoneValueText'")
                if (e is NumberParseException) {
                    trackPhoneNumberParsingErrorEvent(e, firebaseAnalyticsTracker, amplitudeAnalyticsTracker)
                }
                InputError(BaseR.string.phone_error)
            }
    }

    override fun updateVerificationCodeSentTimestamp() {
        verificationCodeSentTimestamp = SystemClock.elapsedRealtime()
    }

    override val resendVerificationCodeTimer = liveData {
        while (true) {
            verificationCodeSentTimestamp?.let {
                val now = SystemClock.elapsedRealtime()
                val remaining = it + SEND_VERIFICATION_CODE_COOL_DOWN_TIME - now
                if (remaining > 0) {
                    emit(Duration.ofMillis(remaining).seconds)
                } else {
                    emit(0L)
                }
            } ?: emit(0L)

            delay(1_000)
        }
    }

    companion object {
        private const val CONTAINS_DIGIT = "(?=.*[0-9])"
        private const val CONTAINS_LETTER = "(?=.*\\p{L})"
        private const val CONTAINS_SPECIAL_CHARACTER = "(?=.*[\\W_])"
        private const val PASSWORD_PATTERN =
            "^$CONTAINS_DIGIT$CONTAINS_LETTER$CONTAINS_SPECIAL_CHARACTER.{8,255}$"
        private val GOOD_PASSWORD_MATCHER = Regex(PASSWORD_PATTERN)
        private val MINIMUM_ACCEPTABLE_PASSWORD_MATCHER = Regex("^.{8,255}$")

        private const val SEND_VERIFICATION_CODE_COOL_DOWN_TIME = 30_000 // milliseconds
    }
}
