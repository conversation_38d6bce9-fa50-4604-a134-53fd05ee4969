package com.stt.android.session.configuration

import android.content.Context
import android.content.Intent
import com.stt.android.domain.session.SessionInitType
import com.stt.android.login.signuplogindone.SignUpLoginDoneActivity
import com.stt.android.session.EXTRA_ON_SUCCESS_LOGIN_INTENT
import javax.inject.Inject
import javax.inject.Named

class SuuntoPostSignInConfiguration
@Inject constructor(
    @Named(EXTRA_ON_SUCCESS_LOGIN_INTENT)
    private val onSuccessLoginIntent: Intent?
) : PostSignInConfiguration(onSuccessLoginIntent) {

    override fun signInDoneActivityIntent(
        context: Context,
        sessionInitType: SessionInitType
    ): Intent = SignUpLoginDoneActivity.newStartIntent(
        context = context,
        isNewUser = sessionInitType == SessionInitType.SIGNUP
    )
}
