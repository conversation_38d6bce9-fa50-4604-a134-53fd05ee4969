package com.stt.android.session.datasource

import com.stt.android.newemail.UserInfoFromChina
import com.stt.android.remote.BaseRemoteApi
import com.stt.android.remote.response.AskoResponse
import com.stt.android.resetpassword.HEADER_PHONE_NUMBER_VERIFICATION
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Query

interface UserCenterChinaRestApi {

    @POST("authentication/suunto")
    suspend fun signInOrSignUp(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
        @Header(HEADER_PHONE_NUMBER_VERIFICATION) phoneNumberVerificationToken: String,
        @Query("phonenumber") phoneNumber: String
    ): AskoResponse<UserInfoFromChina>

    @POST("login2")
    suspend fun loginChina(@Query("l") id: String, @Query("p") password: String): UserInfoFromChina

    @GET("resetPassword")
    suspend fun sendPasswordResetEmail(
        @Query("brand") brand: String,
        @Query("username") username: String
    ): Any
}
