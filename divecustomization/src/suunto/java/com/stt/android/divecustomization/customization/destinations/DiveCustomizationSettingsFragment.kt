package com.stt.android.divecustomization.customization.destinations

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.Scaffold
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.findNavController
import com.stt.android.common.viewstate.ViewState.Loading
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.divecustomization.R
import com.stt.android.divecustomization.customization.logic.DiveCustomizationViewModel
import com.stt.android.divecustomization.customization.ui.common.DiveTopBar
import com.stt.android.divecustomization.customization.ui.divingviews.alarms.DiveAlarmsMainContentView
import com.stt.android.divecustomization.customization.ui.divingviews.algorithm.DiveAlgorithmsMainContentView
import com.stt.android.divecustomization.customization.ui.divingviews.algorithm.DivingAlgorithmInfoLayout
import com.stt.android.divecustomization.customization.ui.divingviews.notifications.DiveNotificationsMainContentView
import com.stt.android.divecustomization.customization.ui.divingviews.stops.DiveStopsMainContentView
import com.stt.android.divecustomization.customization.ui.theme.DiveMaterialTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class DiveCustomizationSettingsFragment : Fragment() {

    private val viewModel: DiveCustomizationViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setContent {
            val state = rememberModalBottomSheetState(ModalBottomSheetValue.Hidden)
            val scope = rememberCoroutineScope()
            val diveSettingsAlgorithmContent by viewModel.getDiveSettingsAlgorithmFlow()
                .collectAsState(Loading())
            val diveAlgorithmsInfoContent by viewModel.getDiveAlgorithmInfoFlow()
                .collectAsState(Loading())
            val diveSettingsStopsContent by viewModel.getDiveSettingsStopsFlow()
                .collectAsState(Loading())
            val diveAlarmsContent by viewModel.getDiveAlarmsFlow()
                .collectAsState(Loading())
            val diveNotificationsContent by viewModel.getDiveNotificationFlow()
                .collectAsState(Loading())

            DiveMaterialTheme {
                ModalBottomSheetLayout(
                    sheetState = state,
                    sheetShape = RoundedCornerShape(8.dp),
                    sheetElevation = 8.dp,
                    sheetContent = {
                        DivingAlgorithmInfoLayout(
                            content = diveAlgorithmsInfoContent,
                            onLinkClicked = {
                                @Suppress("UnsafeImplicitIntentLaunch")
                                startActivity(
                                    Intent(Intent.ACTION_VIEW).apply {
                                        data = it.toUri()
                                    }
                                )
                            }
                        )
                    }
                ) {
                    Scaffold(
                        topBar = {
                            DiveTopBar(
                                titleText = stringResource(R.string.dive_modes_settings_title),
                                onBackPressed = {
                                    findNavController().navigateUp()
                                }
                            )
                        }
                    ) { internalPadding ->
                        ContentCenteringColumn(
                            modifier = Modifier
                                .padding(internalPadding)
                                .verticalScroll(rememberScrollState())
                        ) {
                            DiveAlgorithmsMainContentView(
                                diveSettingsAlgorithmContent = diveSettingsAlgorithmContent,
                                diveAlgorithmsController = viewModel,
                                onAlgorithmInfoClicked = {
                                    scope.launch { state.show() }
                                }
                            )
                            DiveStopsMainContentView(
                                diveSettingsStopsContent = diveSettingsStopsContent,
                                diveStopsController = viewModel
                            )
                            DiveAlarmsMainContentView(
                                content = diveAlarmsContent,
                                controller = viewModel
                            )
                            DiveNotificationsMainContentView(
                                content = diveNotificationsContent,
                                controller = viewModel
                            )
                        }
                    }
                }
            }
        }
    }
}
