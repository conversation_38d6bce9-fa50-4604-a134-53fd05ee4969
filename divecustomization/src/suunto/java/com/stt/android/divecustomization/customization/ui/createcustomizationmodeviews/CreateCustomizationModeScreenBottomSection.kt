package com.stt.android.divecustomization.customization.ui.createcustomizationmodeviews

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.divecustomization.R
import java.util.Locale
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@Composable
fun CreateDiveModeScreenBottomSection(
    isActiveMode: Boolean,
    onSetActiveModeButtonClicked: () -> Unit,
    onSyncToDeviceClick: () -> Unit,
    modifier: Modifier = Modifier,
    syncButtonEnabled: Boolean = true
) {
    Column(
        modifier = modifier
            .padding(dimensionResource(BaseR.dimen.size_spacing_xlarge))
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        SetActiveModeButton(
            modifier = Modifier
                .padding(horizontal = MaterialTheme.spacing.small)
                .fillMaxWidth(),
            enabled = !isActiveMode,
            onClick = onSetActiveModeButtonClicked
        )

        PrimaryButton(
            text = stringResource(R.string.dive_modes_sync_to_device),
            onClick = onSyncToDeviceClick,
            modifier = Modifier
                .padding(horizontal = MaterialTheme.spacing.large)
                .fillMaxWidth(),
            enabled = syncButtonEnabled
        )

        Text(
            modifier = Modifier.padding(top = dimensionResource(CR.dimen.smaller_padding)),
            text = stringResource(R.string.dive_modes_sync_to_device_info_text),
            color = colorResource(BaseR.color.dark_gray),
            textAlign = TextAlign.Center,
            fontSize = 14.sp
        )
    }
}

@Composable
fun SetActiveModeButton(
    modifier: Modifier = Modifier,
    enabled: Boolean = false,
    onClick: () -> Unit
) {
    val buttonText = if (enabled) {
        stringResource(R.string.dive_modes_set_as_active)
    } else {
        stringResource(R.string.dive_modes_active)
    }

    OutlinedButton(
        enabled = enabled,
        modifier = modifier
            .padding(dimensionResource(BaseR.dimen.size_spacing_medium))
            .height(dimensionResource(id = CR.dimen.height_elevated_button)),

        onClick = onClick,
        border = BorderStroke(
            width = 2.dp,
            color = colorResource(id = BaseR.color.dive_set_active_mode_button_border_color)
        ),
        colors = ButtonDefaults.outlinedButtonColors(
            backgroundColor = Color.Transparent,
            contentColor = colorResource(CR.color.accent),
            disabledContentColor = colorResource(BaseR.color.dive_disable_text_color)
        )
    ) {
        Text(
            text = buttonText.uppercase(Locale.getDefault())
        )
    }
}

@Preview
@Composable
private fun CreateDiveModeScreenBottomSectionPreview() {
    AppTheme {
        Surface {
            CreateDiveModeScreenBottomSection(
                syncButtonEnabled = true,
                isActiveMode = false,
                onSetActiveModeButtonClicked = {},
                onSyncToDeviceClick = {}
            )
        }
    }
}

@Preview
@Composable
private fun CreateDiveModeScreenBottomSectionDisabledButtonsPreview() {
    AppTheme {
        Surface {
            CreateDiveModeScreenBottomSection(
                syncButtonEnabled = false,
                isActiveMode = true,
                onSetActiveModeButtonClicked = {},
                onSyncToDeviceClick = {}
            )
        }
    }
}
