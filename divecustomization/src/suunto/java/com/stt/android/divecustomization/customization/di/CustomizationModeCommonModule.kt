package com.stt.android.divecustomization.customization.di

import android.content.Context
import com.soy.algorithms.divemodecustomization.DiveDeviceConfigValidator
import com.soy.algorithms.divemodecustomization.xml.DiveDeviceXMLMapper
import com.stt.android.divecustomization.customization.logic.DiveFileProvider
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import java.io.File

@Module
@InstallIn(SingletonComponent::class)
object CustomizationModeCommonModule {
    @Provides
    fun provideDiveDeviceXMLMapper(): DiveDeviceXMLMapper {
        return DiveDeviceXMLMapper()
    }

    @Provides
    fun provideDiveDeviceConfigValidator(): DiveDeviceConfigValidator {
        return DiveDeviceConfigValidator()
    }

    @Provides
    fun provideDiveFileProvider(@ApplicationContext context: Context): DiveFileProvider {
        return object : DiveFileProvider {
            override fun getFile(): File {
                val dir: File? = context.getExternalFilesDir("eon-settings")
                return File(dir, "eon_settings.xml")
            }
        }
    }
}
