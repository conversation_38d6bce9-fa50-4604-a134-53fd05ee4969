package com.stt.android.divecustomization.customization.destinations.displays

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Scaffold
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.findNavController
import com.stt.android.common.viewstate.ViewState
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.divecustomization.R
import com.stt.android.divecustomization.customization.logic.DiveCustomizationViewModel
import com.stt.android.divecustomization.customization.ui.common.DiveTopBar
import com.stt.android.divecustomization.customization.ui.displays.main.DiveDisplaysMainContentView
import com.stt.android.divecustomization.customization.ui.theme.DiveMaterialTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class DiveCustomizationDisplaysFragment : Fragment() {

    private val viewModel: DiveCustomizationViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setContent {
            val diveDisplaysContentFlow by viewModel.getDiveDisplaysContentFlow()
                .collectAsState(ViewState.Loading())
            DiveMaterialTheme {
                Scaffold(
                    topBar = {
                        DiveTopBar(
                            titleText = stringResource(R.string.dive_modes_displays_title),
                            onBackPressed = {
                                findNavController().navigateUp()
                            }
                        )
                    }
                ) { internalPadding ->
                    val scrollState = rememberScrollState()
                    val coroutineScope = rememberCoroutineScope()
                    ContentCenteringColumn(
                        modifier = Modifier
                            .padding(internalPadding)
                            .verticalScroll(state = scrollState)
                    ) {
                        DiveDisplaysMainContentView(
                            content = diveDisplaysContentFlow,
                            onAddFixedFieldClicked = { viewIndex, fieldType ->
                                val action =
                                    DiveCustomizationDisplaysFragmentDirections.diveCustomizationSelectFixedFieldsAction(
                                        diveDisplayViewIndex = viewIndex,
                                        diveDisplayFieldType = fieldType
                                    )
                                findNavController().navigate(action)
                            },
                            onAddSelectionFieldClicked = { viewIndex, fieldType ->
                                val action =
                                    DiveCustomizationDisplaysFragmentDirections.diveCustomizationSelectSwitchableFieldsAction(
                                        diveDisplayViewIndex = viewIndex,
                                        diveDisplayFieldType = fieldType
                                    )
                                findNavController().navigate(action)
                            },
                            onAddDisplayClicked = {
                                val action =
                                    DiveCustomizationDisplaysFragmentDirections.diveCustomizationSelectDisplayAction()
                                findNavController().navigate(action)
                            },
                            onChangeDisplayClicked = { viewIndex ->
                                val action =
                                    DiveCustomizationDisplaysFragmentDirections.diveCustomizationSelectDisplayAction(
                                        diveDisplayIndex = viewIndex
                                    )
                                findNavController().navigate(action)
                            },
                            onDelete = { index ->
                                coroutineScope.launch {
                                    scrollState.scrollTo(0)
                                }
                                viewModel.deleteDisplay(index)
                            }
                        )
                    }
                }
            }
        }
    }
}
