package com.stt.android.divecustomization.customization.destinations

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Scaffold
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.findNavController
import com.stt.android.common.viewstate.ViewState
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.divecustomization.R
import com.stt.android.divecustomization.customization.logic.DiveCustomizationViewModel
import com.stt.android.divecustomization.customization.ui.common.DiveTopBar
import com.stt.android.divecustomization.customization.ui.gases.DivingGasesMainContentView
import com.stt.android.divecustomization.customization.ui.gases.modegases.DiveModeGasesMainContentView
import com.stt.android.divecustomization.customization.ui.theme.DiveMaterialTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DiveCustomizationGasesFragment : Fragment() {

    private val viewModel: DiveCustomizationViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setContent {
            val diveGasesContent by viewModel.getDiveGasesFlow()
                .collectAsState(ViewState.Loading())
            DiveMaterialTheme {
                Scaffold(
                    topBar = {
                        DiveTopBar(
                            titleText = stringResource(R.string.dive_modes_gases_screen_title),
                            onBackPressed = {
                                findNavController().navigateUp()
                            }
                        )
                    }
                ) { internalPadding ->
                    ContentCenteringColumn(
                        modifier = Modifier
                            .padding(internalPadding)
                            .verticalScroll(rememberScrollState())
                    ) {
                        DivingGasesMainContentView(
                            content = diveGasesContent,
                            controller = viewModel,
                            onSetPointItemClicked = {
                                findNavController().navigate(R.id.action_diveModeGasesFragment_to_diveModeSetPointFragment)
                            }
                        )

                        DiveModeGasesMainContentView(
                            content = diveGasesContent,
                            onGasItemClicked = { gasId ->
                                val action =
                                    DiveCustomizationGasesFragmentDirections.diveCustomizationAddGasAction(
                                        diveGasId = gasId
                                    )
                                findNavController().navigate(action)
                            },
                            onAddGasClicked = { state ->
                                val action =
                                    DiveCustomizationGasesFragmentDirections.diveCustomizationAddGasAction(
                                        diveGasState = state
                                    )
                                findNavController().navigate(action)
                            }
                        )
                    }
                }
            }
        }
    }
}
