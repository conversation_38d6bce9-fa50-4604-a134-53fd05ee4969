package com.stt.android.divecustomization.customization.logic.displays

import com.soy.algorithms.divemodecustomization.entities.Mode
import com.soy.algorithms.divemodecustomization.entities.View
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.flatMap
import com.stt.android.divecustomization.customization.entities.CustomizationModeWithAvailableOptions
import com.stt.android.divecustomization.customization.entities.DiveC12FieldType
import com.stt.android.divecustomization.customization.entities.DiveC21FieldType
import com.stt.android.divecustomization.customization.entities.DiveC22FieldType
import com.stt.android.divecustomization.customization.entities.DiveC2FieldType
import com.stt.android.divecustomization.customization.entities.DiveFieldType
import com.stt.android.divecustomization.customization.entities.DiveModeType
import com.stt.android.divecustomization.customization.entities.displays.DiveDisplay
import com.stt.android.divecustomization.customization.entities.displays.DiveDisplaysDataListItem
import com.stt.android.divecustomization.customization.entities.displays.DiveDisplaysMainContent
import com.stt.android.divecustomization.customization.getSettingOrNull
import com.stt.android.divecustomization.customization.logic.CustomizationModeParent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

interface DiveDisplays : CustomizationModeParent {

    fun getDiveDisplaysContentFlow(): Flow<ViewState<DiveDisplaysMainContent>> {
        return currentModeFlow.map { value ->
            value.flatMap { modeWithAvailableOptions ->
                ViewState.Loaded(
                    getDiveDisplaysContent(modeWithAvailableOptions)
                )
            }
        }
    }

    fun getDiveDisplaysContent(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveDisplaysMainContent {
        return DiveDisplaysMainContent(
            layouts = getDiveDisplayLayouts(modeWithAvailableOptions),
            deviceName = getDeviceName(),
            diveMode = getCurrentMode().diving.diveMode?.value?.let { DiveModeType.from(it) },
            maxLayoutsReached = getMaxLayoutsReached(modeWithAvailableOptions),
            fixedDataFields = getDiveDisplayFixedDataFields(modeWithAvailableOptions),
            switchableDataFields = getDiveDisplaySwitchableDataFields(modeWithAvailableOptions)
        )
    }

    fun deleteDisplay(index: Int) {
        getCurrentMode().let { currentMode ->
            val views = currentMode.views.view.toMutableList()
            views.removeAt(index)
            setCurrentMode(getModeWithUpdatedViews(currentMode, views))
        }
    }

    private fun getModeWithUpdatedViews(
        currentMode: Mode,
        views: List<View>
    ): Mode {
        return currentMode.copy(
            views = currentMode.views.copy(
                view = views
            )
        )
    }

    fun getMaxLayoutsReached(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): Boolean {
        // If maximum is null for some reason, we assume maximum to be four
        val maxDisplays =
            modeWithAvailableOptions.availableOptions.modeViews.viewsListAvailableOptions.conditions.getOrNull(
                0
            )?.max ?: 4

        return modeWithAvailableOptions.availableOptions.modeViews.view.size == maxDisplays
    }

    fun getDiveDisplayLayouts(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): List<DiveDisplay> {
        // if minimum is null for some reason, we assume minimum to be one
        val minDisplays =
            modeWithAvailableOptions.availableOptions.modeViews.viewsListAvailableOptions.conditions.getOrNull(
                0
            )?.min ?: 1

        val layoutOptions =
            modeWithAvailableOptions.availableOptions.modeViews.view.flatMap { view ->
                view.layout.values
            }.distinct()

        val layouts = modeWithAvailableOptions.mode.views.view.mapNotNull {
            val layout = getSettingOrNull(it.layout)
            layout
        }

        return layouts.mapIndexed { index, value ->
            DiveDisplay(
                label = layoutOptions.find { it.value == value }?.label,
                value = value,
                deletable = index >= minDisplays
            )
        }
    }

    /**
     * This method returns the list of fix data fields for each display view in the config. The fields for a view can be
     * null if it does not contain any fixed data fields. So, list1 -> layout, list2 -> field (single value and nullable)
     */
    fun getDiveDisplayFixedDataFields(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): List<List<DiveDisplaysDataListItem>?> {
        val mappedList = modeWithAvailableOptions.mode.views.view.mapIndexed { viewIndex, view ->
            view.fields?.field?.mapIndexed { fieldIndex, field ->
                DiveDisplaysDataListItem(
                    type = DiveFieldType.from(field.name.value),
                    value = field.type.value,
                    label = getFieldLabel(
                        modeWithAvailableOptions,
                        field.type.value,
                        viewIndex,
                        fieldIndex
                    )
                )
            }
        }
        // If there are C2.1 & C2.2 fields we remove them and add a C2 field to be shown to the user
        val c2CombinedList: MutableList<List<DiveDisplaysDataListItem>?> = mutableListOf()
        mappedList.forEach { fieldList ->
            if (hasC2Fields(fieldList)) {
                val c21 =
                    DiveC21FieldType.from(fieldList?.find { it.type == DiveFieldType.C21 }?.value)
                val c22 =
                    DiveC22FieldType.from(fieldList?.find { it.type == DiveFieldType.C22 }?.value)
                val updatedList =
                    fieldList?.filterNot { it.type == DiveFieldType.C21 || it.type == DiveFieldType.C22 } as MutableList

                val c2FieldType = DiveC2FieldType.fromC2Fields(c21, c22)

                updatedList.add(
                    DiveDisplaysDataListItem(
                        type = DiveFieldType.C2,
                        value = c2FieldType.value,
                        label = getC2FieldLabel(c2FieldType)
                    )
                )
                c2CombinedList.add(updatedList)
            } else {
                c2CombinedList.add(fieldList)
            }
        }

        // We need to change the labels for C12 field
        val result = c2CombinedList.map { fieldList ->
            fieldList?.map {
                if (it.type == DiveFieldType.C12) {
                    val newLabel = DiveC12FieldType.labelFromTypeString(it.value)
                    it.copy(
                        label = newLabel
                    )
                } else {
                    it
                }
            }
        }

        // Sorting the fields according to their custom order defined in enum type
        return result.map { view -> view?.sortedBy { it.type } }
    }

    fun getC2FieldLabel(c2FieldType: DiveC2FieldType): String? {
        return when (c2FieldType) {
            DiveC2FieldType.EMPTY -> {
                "TXT_APP_Empty"
            }

            DiveC2FieldType.TEMPERATURE -> {
                "TXT_APP_Temperature"
            }

            DiveC2FieldType.CNS -> {
                "TXT_APP_CNS"
            }

            DiveC2FieldType.OTU -> {
                "TXT_APP_OTU"
            }

            DiveC2FieldType.TEMPERATURE_AND_TIME -> {
                "TXT_APP_Temperature_Time"
            }
        }
    }

    fun hasC2Fields(fieldList: List<DiveDisplaysDataListItem>?): Boolean {
        return fieldList?.find { it.type == DiveFieldType.C21 } != null && fieldList.find { it.type == DiveFieldType.C22 } != null
    }

    /**
     * This method returns the list of switchable data fields for each display view in the config. Since the switchable
     * fields can have multiple values, it's a list itself as well. So, list1 -> layout, list2 -> field, list3 -> field values
     */
    fun getDiveDisplaySwitchableDataFields(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): List<List<List<DiveDisplaysDataListItem>>> {
        return modeWithAvailableOptions.mode.views.view.mapIndexed { viewIndex, view ->
            view.selectionFields.field.mapIndexed { fieldIndex, field ->
                // First we add the requiredValues to the field list
                val requiredTypes =
                    modeWithAvailableOptions.availableOptions.modeViews.view[viewIndex]
                        .selectionFields.field[fieldIndex].types.type[0].requiredValues
                val mappedFields = mutableListOf<DiveDisplaysDataListItem>()
                requiredTypes.forEach { type ->
                    mappedFields.add(
                        DiveDisplaysDataListItem(
                            type = DiveFieldType.from(field.name.value),
                            value = type.value,
                            label = type.label,
                            isRequiredValue = true
                        )
                    )
                }
                mappedFields.addAll(
                    field.types.type.map { type ->
                        DiveDisplaysDataListItem(
                            type = DiveFieldType.from(field.name.value),
                            value = type.value,
                            label = getSelectionFieldLabel(
                                modeWithAvailableOptions,
                                type.value,
                                viewIndex,
                                fieldIndex
                            )
                        )
                    }
                )
                mappedFields.distinctBy { it.value }.toList()
            }
        }.map { view -> view.map { field -> field.sortedBy { it.type } } }
    }

    private fun getFieldLabel(
        modeWithAvailableOptions: CustomizationModeWithAvailableOptions,
        fieldValue: String,
        viewIndex: Int,
        fieldIndex: Int
    ): String? {
        val view = modeWithAvailableOptions.availableOptions.modeViews.view[viewIndex]
        return view.fields?.let { availableOptionsFieldValue ->
            availableOptionsFieldValue.field[fieldIndex].type.values.find { it.value == fieldValue }?.label
        }
    }

    private fun getSelectionFieldLabel(
        modeWithAvailableOptions: CustomizationModeWithAvailableOptions,
        fieldValue: String,
        viewIndex: Int,
        fieldIndex: Int
    ): String? {
        val diveMode = modeWithAvailableOptions.mode.diving.diveMode?.value
        val diveModeType = diveMode?.let { DiveModeType.from(it) }
        // Handle the special cases
        return if (fieldValue == "PO2") {
            if (diveModeType == DiveModeType.CCR) {
                "TXT_APP_SetPoint"
            } else {
                "TXT_APP_PO2"
            }
        } else {
            val view = modeWithAvailableOptions.availableOptions.modeViews.view[viewIndex]
            view.selectionFields.field[fieldIndex].types.type[0].values.find { it.value == fieldValue }?.label
        }
    }
}
