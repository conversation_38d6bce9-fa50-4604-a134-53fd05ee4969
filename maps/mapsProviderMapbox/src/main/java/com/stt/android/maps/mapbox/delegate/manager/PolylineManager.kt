package com.stt.android.maps.mapbox.delegate.manager

import com.mapbox.maps.MapView
import com.mapbox.maps.MapboxMap
import com.mapbox.maps.Style
import com.mapbox.maps.extension.style.layers.generated.LineLayer
import com.mapbox.maps.extension.style.layers.getLayer
import com.mapbox.maps.extension.style.layers.properties.generated.LineCap
import com.mapbox.maps.extension.style.layers.properties.generated.LineJoin
import com.mapbox.maps.plugin.annotation.AnnotationConfig
import com.mapbox.maps.plugin.annotation.annotations
import com.mapbox.maps.plugin.annotation.generated.OnPolylineAnnotationClickListener
import com.mapbox.maps.plugin.annotation.generated.OnPolylineAnnotationDragListener
import com.mapbox.maps.plugin.annotation.generated.PolylineAnnotation
import com.mapbox.maps.plugin.annotation.generated.PolylineAnnotationManager
import com.mapbox.maps.plugin.annotation.generated.PolylineAnnotationOptions
import com.mapbox.maps.plugin.annotation.generated.createPolylineAnnotationManager
import com.stt.android.maps.SuuntoPolyline
import com.stt.android.maps.SuuntoPolylineOptions
import com.stt.android.maps.mapbox.delegate.MapboxPolylineDelegate
import com.stt.android.maps.mapbox.toMapbox

class PolylineManager(
    map: MapboxMap,
    mapView: MapView,
    private val pxToDpFactor: Float,
) : BaseAnnotationManager<
    SuuntoPolyline,
    SuuntoPolylineOptions,
    PolylineAnnotation,
    PolylineAnnotationOptions,
    OnPolylineAnnotationDragListener,
    OnPolylineAnnotationClickListener,
    PolylineAnnotationManager
    >(
    MANAGER_NAME,
    map,
    mapView
) {

    override val annotationFlavors = listOf(TYPE_NORMAL, TYPE_DASHED, TYPE_RULER)

    override fun addAnnotation(options: SuuntoPolylineOptions): SuuntoPolyline {
        val polylineDelegate = MapboxPolylineDelegate(options, this)
        return SuuntoPolyline(polylineDelegate).apply {
            polylineDelegate.attach(this)
        }
    }

    override fun getAnnotationFlavor(options: SuuntoPolylineOptions) =
        when {
            options.rulerLine -> TYPE_RULER
            options.dashedLine -> TYPE_DASHED
            else -> TYPE_NORMAL
        }

    override fun createMapboxAnnotationManager(
        flavor: AnnotationFlavor,
        layerId: String,
        belowLayerId: String
    ) = mapView.annotations.createPolylineAnnotationManager(
        AnnotationConfig(
            layerId = getLayerIdByFlavor(flavor),
            belowLayerId = belowLayerId
        )
    )

    override fun optionsToMapbox(options: SuuntoPolylineOptions): PolylineAnnotationOptions =
        with(options) {
            PolylineAnnotationOptions()
                .withPoints(points.toMapbox())
                .withLineColor(color)
                // Convert width from pixels to dp for Mapbox
                .withLineWidth(width.toDouble() * pxToDpFactor)
                .withLineSortKey(options.zIndex.toDouble())
                .apply {
                    if (borderColor != 0 && borderWidth > 0.0F) {
                        withLineBorderColor(borderColor)
                        // Convert width from pixels to dp for Mapbox
                        withLineBorderWidth(borderWidth.toDouble() * pxToDpFactor)
                    }
                }
        }

    fun onStyleLoaded(style: Style) {
        annotationFlavors.map { flavor ->
            flavor to getLayerIdByFlavor(flavor)
        }.forEach { (flavor, layerId) ->
            style.getLayer(layerId).apply {
                with(this as LineLayer) {
                    // Use rounded gaps so there are no visible gaps between polylines with
                    // thicker line widths
                    lineCap(LineCap.SQUARE)
                    lineJoin(LineJoin.ROUND)

                    if (flavor == TYPE_RULER) {
                        // Hard coded dash array for the white dashed line in map ruler
                        lineDasharray(listOf(3.0, 6.0))
                    } else if (flavor == TYPE_DASHED) {
                        // This hard coded dash array needs to be changed when we need to support
                        // different dash and gap widths.
                        lineDasharray(listOf(2.0, 2.0))
                    }
                }
            }
        }
    }

    override fun createDragListener(flavor: AnnotationFlavor): OnPolylineAnnotationDragListener =
        DelegatingPolylineAnnotationDragListener(flavor)

    override fun createClickListener(flavor: AnnotationFlavor): OnPolylineAnnotationClickListener =
        DelegatingPolylineAnnotationClickListener(flavor)

    private inner class DelegatingPolylineAnnotationDragListener(flavor: AnnotationFlavor) :
        OnPolylineAnnotationDragListener, DelegatingAnnotationDragListener(flavor)

    private inner class DelegatingPolylineAnnotationClickListener(flavor: AnnotationFlavor) :
        OnPolylineAnnotationClickListener, DelegatingAnnotationClickListener(flavor)

    companion object {
        const val MANAGER_NAME = "line"
        const val TYPE_NORMAL: AnnotationFlavor = 1
        const val TYPE_DASHED: AnnotationFlavor = 2
        // Only for the dashed white line in the ruler
        const val TYPE_RULER: AnnotationFlavor = 3
    }
}
