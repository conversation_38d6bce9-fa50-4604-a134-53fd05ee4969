package com.stt.android.maps.snapshotter

import android.content.Context
import com.stt.android.maps.SuuntoMaps
import com.stt.android.maps.delegate.MapSnapshotterDelegate

/**
 * Creates a map snapshot that is a bitmap rendered with a map in specified location, overlaid with optional
 * polylines and markers.
 *
 * @param context Context
 * @param options Options specifying map type, location, camera position, polylines, markers etc.
 */
class SuuntoMapSnapshotter(
    context: Context,
    options: SuuntoMapSnapshotterOptions
) : MapSnapshotterDelegate {

    fun interface SnapshotReadyCallback {
        /**
         * Called when the requested [snapshot] is ready.
         */
        fun onSnapshotReady(snapshot: SuuntoMapSnapshot)
    }

    fun interface SnapshotErrorCallback {
        /**
         * Called when creating a snapshot fails with an [error] message.
         */
        fun onError(error: String)
    }

    private val delegate = SuuntoMaps.getProviderOrDefault(options.mapsProvider)
        .createMapSnapshotterDelegate(context, options)

    /**
     * Starts creating a snapshot.
     * [readyCallback] will be called on main thread when the requested snapshot is ready.
     * [errorCallback] will be called if creating a snapshot fails.
     */
    override fun start(readyCallback: SnapshotReadyCallback, errorCallback: SnapshotErrorCallback) =
        delegate.start(readyCallback, errorCallback)

    /**
     * Cancels creating a snapshot. Callbacks given in [start] will not be called after cancelling.
     */
    override fun cancel() = delegate.cancel()
}
