package com.stt.android.maps.delegate

import androidx.annotation.GravityInt

interface UiSettingsDelegate {

    fun setAllGesturesEnabled(enabled: Boolean)

    fun setMapToolbarEnabled(enabled: Boolean)

    fun setMyLocationButtonEnabled(enabled: Boolean)

    fun setZoomControlsEnabled(enabled: Boolean)

    fun setCompassMargins(left: Int, top: Int, right: Int, bottom: Int)

    fun updateCompassMargins(
        left: Int? = null,
        top: Int? = null,
        right: Int? = null,
        bottom: Int? = null
    )

    fun setLogoMargins(left: Int, top: Int, right: Int, bottom: Int)

    fun updateLogoMargins(
        left: Int? = null,
        top: Int? = null,
        right: Int? = null,
        bottom: Int? = null
    )

    fun setLogoPosition(@GravityInt position: Int)

    fun setAttributionMargins(left: Int, top: Int, right: Int, bottom: Int)

    fun updateAttributionMargins(
        left: Int? = null,
        top: Int? = null,
        right: Int? = null,
        bottom: Int? = null
    )

    fun setTiltTo3dEnabled(enabled: Boolean)
}
