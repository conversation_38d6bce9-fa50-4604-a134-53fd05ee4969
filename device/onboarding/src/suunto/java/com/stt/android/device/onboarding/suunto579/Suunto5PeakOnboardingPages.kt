package com.stt.android.device.onboarding.suunto579

import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.device.onboarding.OnboardingPage
import com.stt.android.device.onboarding.R
import com.stt.android.R as BaseR

val Suunto5PeakOnboardingExplore = OnboardingPage(
    AnalyticsPropertyValue.OnboardingSuunto5PeakPageNameProperty.EXPLORE,
    R.string.onboarding_s5peak_explore_title,
    R.string.onboarding_s5peak_explore_detail,
    R.drawable.onboarding_s5peak_photo_explore
)

val Suunto5PeakOnboardingRoutine = OnboardingPage(
    AnalyticsPropertyValue.OnboardingSuunto5PeakPageNameProperty.ROUTINE,
    R.string.onboarding_s5peak_routine_title,
    R.string.onboarding_s5peak_routine_detail,
    R.drawable.onboarding_s5peak_photo_routine
)

val Suunto5PeakOnboardingMusic = OnboardingPage(
    AnalyticsPropertyValue.OnboardingSuunto5PeakPageNameProperty.MUSIC,
    R.string.onboarding_s5peak_music_title,
    R.string.onboarding_s5peak_music_detail,
    R.drawable.onboarding_s5peak_photo_music
)

val Suunto5PeakOnboardingGuidance = OnboardingPage(
    AnalyticsPropertyValue.OnboardingSuunto5PeakPageNameProperty.GUIDANCE,
    R.string.onboarding_s5peak_guidance_title,
    R.string.onboarding_s5peak_guidance_detail,
    R.drawable.onboarding_s5peak_photo_guidance
)

val Suunto5PeakOnboardingSleep = OnboardingPage(
    AnalyticsPropertyValue.OnboardingSuunto5PeakPageNameProperty.SLEEP,
    R.string.onboarding_s5peak_sleep_title,
    R.string.onboarding_s5peak_sleep_detail,
    R.drawable.onboarding_s5peak_photo_sleep
)

val Suunto5PeakOnboardingEnd = OnboardingPage(
    AnalyticsPropertyValue.OnboardingSuunto5PeakPageNameProperty.END,
    R.string.onboarding_s5peak_final_title,
    R.string.onboarding_s5peak_final_detail,
    R.drawable.onboarding_s5peak_photo_final,
    primaryButtonResId = BaseR.string.ok,
    secondaryButtonResId = BaseR.string.onboarding_visit_help
)

val Suunto5PeakOnboardingPages = listOf(
    Suunto5PeakOnboardingExplore,
    Suunto5PeakOnboardingRoutine,
    Suunto5PeakOnboardingMusic,
    Suunto5PeakOnboardingGuidance,
    Suunto5PeakOnboardingSleep,
    Suunto5PeakOnboardingEnd
)
