package com.stt.android.device.onboarding

import com.stt.android.watch.DeviceOnboardingNavigator
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class OnboardingActivityModule {
    @Binds
    abstract fun bindDeviceOnboardingNavigator(
        deviceOnboardingNavigatorImpl: DeviceOnboardingNavigatorImpl
    ): DeviceOnboardingNavigator
}
