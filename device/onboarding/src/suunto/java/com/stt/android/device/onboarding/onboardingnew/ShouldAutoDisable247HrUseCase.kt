package com.stt.android.device.onboarding.onboardingnew

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.domain.gear.Gear
import com.stt.android.domain.gear.GearRepository
import com.stt.android.utils.STTConstants
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class ShouldAutoDisable247HrUseCase @Inject constructor(
    private val gearRepository: GearRepository,
    @SuuntoSharedPrefs private val sharedPreferences: SharedPreferences
) {

    suspend fun shouldAutoDisable247Hr(serial: String): Boolean {
        val gear = initializeGearIfNeeded(serial)
        if (gear.firstSyncDate != gear.lastSyncDate) {
            return false
        }
        // Ensure that first and last sync dates are not equal
        val updatedLastSyncDate =
            gear.lastSyncDate?.plus(TimeUnit.SECONDS.toMillis(1)) ?: System.currentTimeMillis()
        gearRepository.sendPairEvent(serial, gear.copy(lastSyncDate = updatedLastSyncDate))

        // Add serial to sharedprefs, so that GearEventSender won't send new event
        sharedPreferences.edit {
            putString(STTConstants.SuuntoPreferences.KEY_SUUNTO_LAST_PAIR_EVENT_SERIAL, serial)
        }
        return true
    }

    private suspend fun initializeGearIfNeeded(serial: String): Gear {
        gearRepository.updateGear()
        return gearRepository.fetchAll().find { it.serialNumber == serial } ?: run {
            val lastSyncDate = System.currentTimeMillis()
            buildGear(
                lastSyncDate = lastSyncDate,
                firstSyncDate = lastSyncDate
            ).also { initialGear ->
                gearRepository.sendPairEvent(serial, initialGear)
            }
        }
    }

    private fun buildGear(lastSyncDate: Long? = null, firstSyncDate: Long? = null) = Gear(
        serialNumber = sharedPreferences.getString(STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_SERIAL_NUMBER, "") ?: "",
        manufacturer = sharedPreferences.getString(STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MANUFACTURER, "") ?: "",
        name = sharedPreferences.getString(STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL, "") ?: "",
        softwareVersion = sharedPreferences.getString(STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_FW_VERSION, "") ?: "",
        hardwareVersion = sharedPreferences.getString(STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_HW_VERSION, "") ?: "",
        lastSyncDate = lastSyncDate,
        firstSyncDate = firstSyncDate
    )
}

