package com.stt.android.device.suuntoplusfeature.details

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.device.R
import com.stt.android.R as BaseR

@Composable
internal fun ConfirmRemoveDialog(
    onRemoveClick: () -> Unit,
    onCancelClick: () -> Unit,
    isWatchface: Boolean,
    modifier: Modifier = Modifier
) {
    ConfirmationDialog(
        text = buildAnnotatedString {
            if (isWatchface) {
                withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                    append(stringResource(id = R.string.suunto_plus_remove_watch_face_from_watch_faces_confirmation_title))
                }
            } else {
                withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                    append(stringResource(id = R.string.suunto_plus_remove_sports_app_confirmation_title))
                }
                append("\n\n")
                append(stringResource(id = R.string.suunto_plus_uninstall_sports_app_confirmation_text))
            }
        },
        cancelButtonText = stringResource(id = BaseR.string.cancel),
        confirmButtonText = stringResource(id = R.string.suunto_plus_uninstall_sports_app_confirmation_button),
        onDismissRequest = onCancelClick,
        onConfirm = onRemoveClick,
        useDestructiveColorForConfirm = true,
        modifier = modifier
    )
}

@Composable
internal fun ConfirmUninstallDialog(
    onUninstallClick: () -> Unit,
    onCancelClick: () -> Unit,
    isWatchface: Boolean,
    modifier: Modifier = Modifier
) {
    ConfirmationDialog(
        text = buildAnnotatedString {
            withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                append(stringResource(id = R.string.suunto_plus_uninstall_sports_app_confirmation_title))
            }

            if (!isWatchface) {
                append("\n\n")
                append(stringResource(id = R.string.suunto_plus_uninstall_sports_app_confirmation_text))
            }
        },
        cancelButtonText = stringResource(id = BaseR.string.cancel),
        confirmButtonText = stringResource(id = R.string.suunto_plus_uninstall_sports_app_confirmation_button),
        onDismissRequest = onCancelClick,
        onConfirm = onUninstallClick,
        useDestructiveColorForConfirm = true,
        modifier = modifier
    )
}

@Composable
internal fun ConfirmMandatorySettingsInvalidDialog(
    onSetUpClick: () -> Unit,
    onDismissClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    ConfirmationDialog(
        text = buildAnnotatedString {
            withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                append(stringResource(id = R.string.suunto_plus_mandatory_settings_required_title))
            }

            append("\n\n")
            append(stringResource(id = R.string.suunto_plus_mandatory_settings_required_text))
        },
        cancelButtonText = stringResource(id = R.string.suunto_plus_mandatory_settings_required_dismiss_button),
        confirmButtonText = stringResource(id = R.string.suunto_plus_mandatory_settings_required_set_up_button),
        onDismissRequest = onDismissClick,
        onConfirm = onSetUpClick,
        useDestructiveColorForConfirm = false,
        modifier = modifier
    )
}

@Composable
fun CannotRemoveWatchfaceDialog(
    onDismissClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    ConfirmationDialog(
        text = buildAnnotatedString {
            append(stringResource(id = R.string.suunto_plus_watch_face_cannot_be_removed))
        },
        cancelButtonText = "",
        confirmButtonText = stringResource(id = com.stt.android.R.string.ok),
        onDismissRequest = onDismissClick,
        onConfirm = onDismissClick,
        useDestructiveColorForConfirm = false,
        showDismissButton = false,
        modifier = modifier
    )
}

@Preview
@Composable
private fun ConfirmRemoveDialogPreview() {
    AppTheme {
        ConfirmRemoveDialog({}, {}, false)
    }
}

@Preview
@Composable
private fun ConfirmUninstallDialogPreview() {
    AppTheme {
        ConfirmUninstallDialog({}, {}, false)
    }
}

@Preview
@Composable
private fun ConfirmMandatorySettingsInvalidDialogPreview() {
    AppTheme {
        ConfirmMandatorySettingsInvalidDialog({}, {})
    }
}

@Preview
@Composable
private fun CannotRemoveWatchfaceDialogPreview() {
    AppTheme {
        CannotRemoveWatchfaceDialog({})
    }
}
