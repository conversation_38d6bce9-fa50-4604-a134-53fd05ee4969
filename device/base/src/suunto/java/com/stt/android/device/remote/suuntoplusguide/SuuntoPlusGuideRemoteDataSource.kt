package com.stt.android.device.remote.suuntoplusguide

import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuide
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.exceptions.device.WatchPluginNotSupportedByCapabilities
import com.stt.android.exceptions.remote.ClientError
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import timber.log.Timber
import javax.inject.Inject

class SuuntoPlusGuideRemoteDataSource
@Inject constructor(
    private val remoteApi: SuuntoPlusGuideRemoteAPI,
) {
    suspend fun fetchAll(): List<SuuntoPlusGuide> =
        remoteApi.fetchAll().map(RemoteGuideInfo::toDomain)

    suspend fun fetchZAPPFile(
        guideId: SuuntoPlusGuideId,
        capabilities: SuuntoWatchCapabilities
    ): ByteArray = try {
        remoteApi.fetchZAPPFile(guideId.id, capabilities.stringValue)
    } catch (e: ClientError.NotFound) {
        Timber.w("Backend reports guide $guideId not supported by capabilities ($capabilities)")
        throw WatchPluginNotSupportedByCapabilities()
    }

    suspend fun fetchJsonFile(
        guideId: SuuntoPlusGuideId,
        capabilities: SuuntoWatchCapabilities
    ): ByteArray = try {
        remoteApi.fetchJsonFile(guideId.id, capabilities.stringValue)
    } catch (e: ClientError.NotFound) {
        Timber.w("Backend reports guide $guideId not supported by capabilities ($capabilities)")
        throw WatchPluginNotSupportedByCapabilities()
    }

    suspend fun fetchSource(
        guideId: SuuntoPlusGuideId,
    ): ByteArray = remoteApi.fetchSource(guideId.id)

    suspend fun uploadGuide(guideZip: ByteArray): SuuntoPlusGuide =
        remoteApi.uploadGuide(guideZip).toDomain()

    suspend fun updateGuide(guideId: SuuntoPlusGuideId, guideZip: ByteArray): SuuntoPlusGuide =
        remoteApi.updateGuide(guideId.id, guideZip).toDomain()

    suspend fun updatePinnedStatus(guideId: SuuntoPlusGuideId, pinned: Boolean) =
        remoteApi.updatePinnedStatus(guideId.id, pinned)

    suspend fun delete(guideId: SuuntoPlusGuideId) =
        remoteApi.delete(guideId.id)

    suspend fun fetchPriorityIndices(): Map<SuuntoPlusGuideId, Int> =
        remoteApi.fetchPriorityIndices()
}
