package com.stt.android.device.remote.suuntoplusfeature.manifest

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.stt.android.device.domain.suuntoplusfeature.settings.BooleanVariable
import com.stt.android.device.domain.suuntoplusfeature.settings.FloatingPointVariable
import com.stt.android.device.domain.suuntoplusfeature.settings.IntegerVariable
import com.stt.android.device.domain.suuntoplusfeature.settings.StringVariable
import com.stt.android.device.domain.suuntoplusfeature.settings.SuuntoPlusVariable
import com.stt.android.device.suuntoplusfeature.settings.SettingsJson
import timber.log.Timber

@JsonClass(generateAdapter = true)
data class SuuntoPlusManifestVariable(
    @Json(name = "shownName") val displayName: String,
    @Json(name = "path") val path: String,
    @Json(name = "type") val typeString: String, // Parse into String instead of enum so we don't choke on unsupported values
) {
    val type: SuuntoPlusManifestSettingType?
        get() = SuuntoPlusManifestSettingType.entries.firstOrNull { it.type == typeString }
}

fun SuuntoPlusManifestVariable.toDomain(settingsJson: SettingsJson): SuuntoPlusVariable? =
    when (type) {
        SuuntoPlusManifestSettingType.FLOAT -> FloatingPointVariable(
            path = path,
            displayName = displayName.nameWithoutUnit,
            unit = displayName.unit,
            value = settingsJson.doubleForPath(path)
        )

        SuuntoPlusManifestSettingType.INTEGER -> IntegerVariable(
            path = path,
            displayName = displayName.nameWithoutUnit,
            unit = displayName.unit,
            value = settingsJson.integerForPath(path)
        )

        SuuntoPlusManifestSettingType.STRING -> StringVariable(
            path = path,
            displayName = displayName,
            value = settingsJson.stringForPath(path)
        )

        SuuntoPlusManifestSettingType.BOOLEAN -> BooleanVariable(
            path = path,
            displayName = displayName,
            value = settingsJson.booleanForPath(path)
        )

        SuuntoPlusManifestSettingType.ENUM -> {
            Timber.w("Ignoring '$displayName': 'enum' variables are not supported")
            null
        }

        null -> null
    }
