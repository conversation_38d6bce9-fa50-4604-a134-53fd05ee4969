package com.stt.android.device.widget.v3

import WatchWidgetCustomizationListItemMore
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.util.DraggableItem
import com.stt.android.compose.util.createLocaleContext
import com.stt.android.compose.util.dragContainer
import com.stt.android.compose.util.rememberDragDropState
import com.stt.android.device.domain.widget.entities.WatchWidget
import com.stt.android.device.domain.widget.entities.WatchWidgetType
import kotlinx.collections.immutable.ImmutableList
import java.util.Locale

@Composable
fun WatchWidgetCustomizationListForV3(
    widgets: ImmutableList<WatchWidget>?,
    enabled: Boolean,
    setWidgetEnabled: (String, Boolean) -> Unit,
    onWidgetMoved: (Int, Int) -> Unit,
    modifier: Modifier = Modifier,
    onWidgetDropped: (Int, Int) -> Unit = { _, _ -> }
) {
    val indexOffset = 1
    var controlPanelWidgetIndex by remember { mutableIntStateOf(0) }
    var lastCanMovedWidgetIndex by remember { mutableIntStateOf(0) }
    val listState = rememberLazyListState()
    val enabledState = rememberUpdatedState(enabled)
    val dragDropState = rememberDragDropState(
        lazyListState = listState,
        canMove = {
            // control panel cannot be removed or sorted
            // unselected widget also cannot be removed or sorted
            val canMoveStartIndex = indexOffset + controlPanelWidgetIndex
            val canMoveEndIndex = indexOffset + lastCanMovedWidgetIndex
            it in canMoveStartIndex..canMoveEndIndex && enabledState.value
        },
        onMove = { from, to ->
            val fromIndex = from - indexOffset
            val toIndex = to - indexOffset
            if (fromIndex > lastCanMovedWidgetIndex
                || toIndex < controlPanelWidgetIndex
                || toIndex > lastCanMovedWidgetIndex
            ) return@rememberDragDropState
            onWidgetMoved(fromIndex, toIndex)
        },
        onDrop = { from, to ->
            val fromIndex = from - indexOffset
            val toIndex = to - indexOffset
            if (fromIndex > lastCanMovedWidgetIndex
                || toIndex < controlPanelWidgetIndex
                || toIndex > lastCanMovedWidgetIndex
            ) return@rememberDragDropState
            onWidgetDropped(fromIndex, toIndex)
        },
        canMoveOver = {
            val canMoveStartIndex = indexOffset + controlPanelWidgetIndex
            val canMoveEndIndex = indexOffset + lastCanMovedWidgetIndex
            it in canMoveStartIndex .. canMoveEndIndex
        }
    )

    val context = LocalContext.current
    val locale = remember { Locale.US }
    val localizedContext by rememberUpdatedState(context.createLocaleContext(locale))

    LazyColumn(
        modifier = modifier
            .dragContainer(dragDropState)
            .background(MaterialTheme.colors.lightGrey),
        state = listState
    ) {
        item("header") {
            WatchWidgetCustomizationListHeaderForV3()
        }
        if (widgets == null) return@LazyColumn
        // for v3, control panel is fixed at the first position of the widget,
        // and control panel cannot be removed or sorted
        widgets.find { it.type == WatchWidgetType.WIDGET_CTRLP }?.let { widget ->
            controlPanelWidgetIndex = 1
            item("controlPanel") {
                Box {
                    WatchWidgetControlPanel(widget = widget)
                    Divider(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .fillMaxWidth()
                    )
                }
            }
        }

        val selectedWidgets =
            widgets.filter { it.enabled && it.type != WatchWidgetType.WIDGET_CTRLP }

        // for unselect widgets, sort by the English translation of widget, alphabetically from A～Z
        val unselectedWidgets =
            widgets.filter { !it.enabled }.sortedBy { localizedContext.getString(it.nameRes) }
        lastCanMovedWidgetIndex = selectedWidgets.lastIndex + controlPanelWidgetIndex

        itemsIndexed(
            items = selectedWidgets,
            key = { _, widget -> widget.id }
        ) { index, widget ->
            DraggableItem(
                dragDropState = dragDropState,
                index = index + indexOffset + controlPanelWidgetIndex
            ) { isDragging ->
                val elevation = animateDpAsState(targetValue = if (isDragging) 8.dp else 0.dp)
                val isControlPanelWidget = widget.type == WatchWidgetType.WIDGET_CTRLP
                WatchWidgetCustomizationListItem(
                    elevation = elevation.value,
                    widget = widget,
                    isWidgetEnabled = enabled,
                    isShowDragDropIcon = !isControlPanelWidget,
                    isShowEndIcon = !isControlPanelWidget,
                    onEnabledChange = {
                        setWidgetEnabled(widget.id, it)
                    }
                )

                if (index < selectedWidgets.lastIndex) {
                    Divider(
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }

        if (unselectedWidgets.isNotEmpty()) {
            item("more widgets") {
                WatchWidgetCustomizationListItemMore()
            }

            itemsIndexed(
                items = unselectedWidgets,
                key = { _, widget -> widget.id }
            ) { index, widget ->
                Box {
                    WatchWidgetCustomizationListItem(
                        widget = widget,
                        isWidgetEnabled = enabled,
                        isShowDragDropIcon = false,
                        isShowEndIcon = true,
                        onEnabledChange = {
                            setWidgetEnabled(widget.id, it)
                        }
                    )

                    if (index < unselectedWidgets.lastIndex) {
                        Divider(
                            modifier = Modifier
                                .align(Alignment.BottomCenter)
                                .fillMaxWidth()
                        )
                    }
                }
            }
        }
    }
}
