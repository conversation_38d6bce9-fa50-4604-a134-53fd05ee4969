package com.stt.android.device.domain.suuntoplusfeature.settings

import com.stt.android.data.source.local.suuntoplusfeature.SportsAppSettingsStateDao
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import javax.inject.Inject

class SportsAppSettingsStateDataSource @Inject constructor(
    private val sportsAppSettingsStateDao: SportsAppSettingsStateDao,
) {
    suspend fun markAsLocked(watchSerial: String, pluginId: String) =
        sportsAppSettingsStateDao.setLockedFlag(watchSerial, pluginId, true)

    suspend fun markAsUnlocked(watchSerial: String, pluginId: String) =
        sportsAppSettingsStateDao.setLockedFlag(watchSerial, pluginId, false)

    suspend fun isMarkedAsLocked(watchSerial: String, pluginId: String): Boolean =
        sportsAppSettingsStateDao.isMarkedAsLocked(watchSerial, pluginId)

    suspend fun findDataJson(watchSerial: String, pluginId: String): String? =
        sportsAppSettingsStateDao.findDataJson(watchSerial, pluginId)

    suspend fun setDataJson(watchSerial: String, pluginId: String, dataJson: String?) =
        sportsAppSettingsStateDao.setDataJson(watchSerial, pluginId, dataJson)

    /**
     * Set the busy flag, run given [operation] and clear the busy flag. If the busy flag was
     * already set, throw an exception and abort the operation.
     *
     * This is meant to protect operations like downloading or uploading the 'data.jsn' file on the
     * watch so that we can never have two parallel operations ongoing.
     */
    suspend fun<T> runWithBusyState(watchSerial: String, pluginId: String, operation: suspend () -> T): T {
        sportsAppSettingsStateDao.setBusy(watchSerial, pluginId)
        return try {
            operation()
        } finally {
            withContext(NonCancellable) {
                sportsAppSettingsStateDao.clearBusy(watchSerial, pluginId)
            }
        }
    }

    suspend fun resetForNewWatchConnection() =
        sportsAppSettingsStateDao.resetForNewWatchConnection()

    fun anySportsAppBusy(watchSerial: String): Flow<Boolean> =
        sportsAppSettingsStateDao.anySportsAppBusy(watchSerial)
}
