package com.stt.android.device.remote.watchkey

import com.squareup.moshi.Moshi
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient

@Module
@InstallIn(SingletonComponent::class)
object WatchKeyModule {
    @Provides
    fun provideServerStatusRestApi(
        @SharedOkHttpClient sharedClient: OkHttpClient,
        @BaseUrl baseUrl: String,
        @UserAgent userAgent: String,
        authProvider: AuthProvider,
        moshi: Moshi
    ): WatchKeyRestApi = RestApiFactory.buildRestApi(
        sharedClient,
        baseUrl,
        WatchKeyRestApi::class.java,
        BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
        moshi
    )
}
