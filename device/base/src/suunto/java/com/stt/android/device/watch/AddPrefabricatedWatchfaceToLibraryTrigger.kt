package com.stt.android.device.watch

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.device.datasource.suuntoplusguide.OnDeviceConnectedInitializer
import com.stt.android.watch.suuntoplusguide.AddPrefabricatedWatchfaceToLibrary
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import com.suunto.connectivity.hooks.WatchInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject

class AddPrefabricatedWatchfaceToLibraryTrigger @Inject constructor(
    private val addPrefabricatedWatchfaceToLibrary: AddPrefabricatedWatchfaceToLibrary,
    coroutinesDispatchers: CoroutinesDispatchers,
) : OnDeviceConnectedInitializer {

    private val coroutineScope = CoroutineScope(coroutinesDispatchers.io + SupervisorJob())

    override suspend fun onDeviceConnected(
        serial: String,
        model: String,
        fwVersion: String,
        hwVersion: String,
        capabilities: List<String>
    ) {
        val suuntoWatchCapabilities = SuuntoWatchCapabilities(capabilities)
        if (!suuntoWatchCapabilities.areSuuntoPlusWatchfaceSupported) return

        // Try add prefabricated watch face to library.
        coroutineScope.launch {
            addPrefabricatedWatchfaceToLibrary.addToLibraryAndSyncRemote(
                WatchInfo(
                    capabilities = suuntoWatchCapabilities,
                    variant = model,
                    swVersion = fwVersion,
                    serial = serial
                )
            )
        }
    }
}
