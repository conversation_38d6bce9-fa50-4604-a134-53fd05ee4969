package com.stt.android.device.widget

import android.content.SharedPreferences
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.toOnOff
import com.stt.android.device.domain.widget.entities.WatchWidget
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.utils.STTConstants
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper
import javax.inject.Inject

class WatchWidgetAnalytics @Inject constructor(
    @SuuntoSharedPrefs suuntoSharedPreferences: SharedPreferences,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) {
    private val firmwareVersion: String? = suuntoSharedPreferences.getString(
        STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_FW_VERSION,
        "N/A"
    )
    private val watchVariantName: String? = suuntoSharedPreferences.getString(
        STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
        "N/A"
    )
    private val watchModel: String? = watchVariantName?.let {
        AnalyticsDevicePropertyHelper.getWatchModelNameForVariantName(it)
    }
    private val serialNumber: String? = suuntoSharedPreferences.getString(
        STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_SERIAL_NUMBER,
        "N/A"
    )

    fun trackWatchEditWidgetsScreen(
        analyticsSource: String,
        currentWidgets: List<WatchWidget>
    ) {
        val widgetsInUse = currentWidgets
            .filter { it.enabled }
            .map { it.type.analyticsName }

        AnalyticsProperties().apply {
            put(AnalyticsEventProperty.SOURCE, analyticsSource)
            put(AnalyticsEventProperty.WIDGETS_IN_USE, widgetsInUse)
            put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModel)
            put(AnalyticsEventProperty.SUUNTO_WATCH_SERIAL_NUMBER, serialNumber)
            put(AnalyticsEventProperty.SUUNTO_WATCH_FIRMWARE_VERSION, firmwareVersion)
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.WATCH_EDIT_WIDGETS_SCREEN, this)
        }
    }

    fun trackWatchWidgetsUsedChanged(widget: WatchWidget) {
        AnalyticsProperties().apply {
            put(AnalyticsEventProperty.WATCH_WIDGET_NAME, widget.type.analyticsName)
            put(AnalyticsEventProperty.NEW_STATUS, widget.enabled.toOnOff())
            put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModel)
            put(AnalyticsEventProperty.SUUNTO_WATCH_SERIAL_NUMBER, serialNumber)
            put(AnalyticsEventProperty.SUUNTO_WATCH_FIRMWARE_VERSION, firmwareVersion)
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.WATCH_WIDGETS_USED_CHANGED, this)
        }
    }

    fun trackWatchWidgetsOrderChanged(widget: WatchWidget, newIndex: Int) {
        AnalyticsProperties().apply {
            put(AnalyticsEventProperty.WATCH_WIDGET_NAME, widget.type.analyticsName)
            put(AnalyticsEventProperty.NEW_POSITION, newIndex)
            put(AnalyticsEventProperty.USE_IN_WATCH_STATUS, widget.enabled.toOnOff())
            put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModel)
            put(AnalyticsEventProperty.SUUNTO_WATCH_SERIAL_NUMBER, serialNumber)
            put(AnalyticsEventProperty.SUUNTO_WATCH_FIRMWARE_VERSION, firmwareVersion)
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.WATCH_WIDGETS_ORDER_CHANGED, this)
        }
    }
}
