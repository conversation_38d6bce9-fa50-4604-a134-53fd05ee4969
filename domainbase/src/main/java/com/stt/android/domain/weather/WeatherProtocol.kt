package com.stt.android.domain.weather

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types

/**
 * This data class is used for sync weather data to watch.
 */
@JsonClass(generateAdapter = true)
data class WeatherProtocol(
    val weatherInfo: WeatherInfo,
    val weatherList: List<WeatherData>,
    val weatherDaily: List<WeatherDaily>? = null,
    val weatherDailyUvi: List<WeatherDailyUvi>? = null,
    val weatherHourlyAqi: List<WeatherHourlyAqi>? = null,
) {

    companion object {
        /**
         * Convert weather data object to contract.
         */
        fun toContract(weatherObject: Any): String {
            val jsonMap = HashMap<String, Any>().apply {
                put("value", weatherObject)
            }

            return Moshi.Builder().build().adapter<Map<String, Any>>(
                Types.newParameterizedType(
                    Map::class.java,
                    String::class.java,
                    Any::class.java
                )
            ).toJson(jsonMap)
        }
    }
}

@JsonClass(generateAdapter = true)
data class WeatherInfo(
    @Json(name = "country")
    val country: String?,

    @Json(name = "city")
    val city: String?,

    @Json(name = "latitude")
    val latitude: Double?,

    @Json(name = "longitude")
    val longitude: Double?,

    @Json(name = "sunrise")
    val sunrise: Int?,

    @Json(name = "sunset")
    val sunset: Int?,

    @Json(name = "uvi")
    val uvi: Float?,

    @Json(name = "aqi")
    val aqi: Int?,

    val requestTimeMills: Long
)

@JsonClass(generateAdapter = true)
data class WeatherData(
    @Json(name = "dt")
    val dateTime: Long?,

    @Json(name = "humidity")
    val humidity: Int?,

    @Json(name = "grndLevel")
    val grndLevel: Int = 0,

    @Json(name = "seaLevel")
    val seaLevel: Int = 0,

    @Json(name = "pressure")
    val pressure: Int?,

    @Json(name = "feelsLike")
    val feelsLike: Float?,

    @Json(name = "temp")
    val temp: Float?,

    @Json(name = "tempMax")
    val tempMax: Float?,

    @Json(name = "tempMin")
    val tempMin: Float?,

    @Json(name = "weatherType")
    val weatherType: Int?,

    @Json(name = "windDeg")
    val windDeg: Int?,

    @Json(name = "windType")
    val windType: Int?,

    @Json(name = "windGust")
    val windGust: Float?,

    @Json(name = "windSpeed")
    val windSpeed: Float?,

    @Json(name = "visibility")
    val visibility: Int?,

    @Json(name = "pop")
    val pop: Float?,

    @Json(name = "rain")
    val rain: Float = 0F,
)

@JsonClass(generateAdapter = true)
data class WeatherDaily(
    @Json(name = "dt")
    val dateTime: Long?,

    @Json(name = "tempMax")
    val tempMax: Float?,

    @Json(name = "tempMin")
    val tempMin: Float?,

    @Json(name = "weatherType")
    val weatherType: Int?
)

@JsonClass(generateAdapter = true)
data class WeatherHourlyAqi(
    @Json(name = "aqi")
    val aqi: Int,
    @Json(name = "dt")
    val timeInSeconds: Long,
)

@JsonClass(generateAdapter = true)
data class WeatherDailyUvi(
    @Json(name = "uvi")
    val uvi: Double,
    @Json(name = "dt")
    val dateInSeconds: Long,
)

enum class WeatherType(val value: Int) {
    CLEAR_SKY(0),
    FEW_CLOUD(1),
    SCATTERED_CLOUDS(2),
    BROKEN_CLOUDS(3),
    SHOWER_RAIN(4),
    RAIN(5),
    THUNDERSTORM(6),
    SNOW(7),
    MIST(8)
}

enum class WindDirection(val value: Int) {
    NORTH_WIND(0),
    NORTH_EAST_WIND(1),
    EAST_WIND(2),
    SOUTH_EAST_WIND(3),
    SOUTH_WIND(4),
    SOUTH_WEST_WIND(5),
    WEST_WIND(6),
    NORTH_WEST_WIND(7)
}
