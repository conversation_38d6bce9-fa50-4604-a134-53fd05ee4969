<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    style="@style/WorkoutDetailCard.TopMargin"
    android:layout_width="match_parent"
    android:paddingBottom="@dimen/size_spacing_medium"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/graphTitle"
        style="@style/WorkoutDetailCardTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_medium"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Speed" />

    <TextView
        android:id="@+id/graphSubtitle"
        style="@style/Body.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_xxsmall"
        app:layout_constraintLeft_toLeftOf="@id/graphTitle"
        app:layout_constraintTop_toBottomOf="@id/graphTitle"
        tools:text="Average 8.25 km/h" />

    <TextView
        android:id="@+id/graphButton"
        style="@style/Body.Medium.Black"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:paddingEnd="@dimen/size_spacing_xsmall"
        android:textColor="@color/primary_blue"
        app:layout_constraintBaseline_toBaselineOf="@id/graphTitle"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="VIEW ON MAP" />

    <com.stt.android.workout.details.charts.AnalysisWorkoutLineChart
        android:id="@+id/workoutLineChart"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_chart_height"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_large"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/graphSubtitle" />
</androidx.constraintlayout.widget.ConstraintLayout>

