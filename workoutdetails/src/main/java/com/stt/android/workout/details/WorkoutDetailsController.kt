package com.stt.android.workout.details

import android.content.Context
import android.content.SharedPreferences
import androidx.annotation.StringRes
import androidx.fragment.app.FragmentManager
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.achievementItem
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.common.viewstate.ViewState
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.di.ActivityFragmentManager
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.user.workout.SimilarWorkoutSummary
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummaryData
import com.stt.android.domain.workouts.extensions.SwimmingExtension
import com.stt.android.fastestOnRouteItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.newfeed.AchievementItem
import com.stt.android.newfeed.AchievementUtil.createDisplayTextForCumulative
import com.stt.android.newfeed.AchievementUtil.createDisplayTextForPersonalBests
import com.stt.android.newfeed.BaseAchievementItem
import com.stt.android.newfeed.FastestOnThisRouteItem
import com.stt.android.rankings.getDescription
import com.stt.android.ui.utils.ThrottlingOnModelClickListener
import com.stt.android.utils.CalendarProvider
import com.stt.android.workout.details.comments.addComment
import com.stt.android.workout.details.divelocation.diveLocation
import com.stt.android.workout.details.shareactivity.getSharingFlagByViewId
import com.stt.android.workout.details.summary.recentWorkoutSummary
import com.stt.android.workout.details.suuntocoach.getCoachValues
import com.stt.android.workout.details.suuntocoach.swimmingCoach
import com.stt.android.workout.details.trend.recentTrendData
import com.stt.android.workoutdetail.tags.tagsCarousel
import dagger.hilt.android.qualifiers.ActivityContext
import javax.inject.Inject
import com.stt.android.R as BaseR

class WorkoutDetailsController @Inject constructor(
    @ActivityContext context: Context,
    @ActivityFragmentManager fragmentManager: FragmentManager,
    infoModelFormatter: InfoModelFormatter,
    private val workoutHeaderController: WorkoutHeaderController,
    private val calendarProvider: CalendarProvider,
    unitConverter: JScienceUnitConverter,
    @FeatureTogglePreferences featureTogglePreferences: SharedPreferences,
    private val analyticsTracker: AmplitudeAnalyticsTracker,
) : BaseWorkoutDetailsController(
    context,
    fragmentManager,
    infoModelFormatter,
    unitConverter,
    featureTogglePreferences
) {

    override fun addTopModel(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        addReactions(workoutDetailsViewState)
        addAchievements(workoutDetailsViewState)
        addTags(workoutDetailsViewState)
        addComments(workoutDetailsViewState)
    }

    private fun addTags(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        val data = workoutDetailsViewState.data ?: return
        val tagsData = data.tagsData ?: return
        val isSubscribedToPremium = data.isSubscribedToPremium
        if (tagsData.hasSomethingToShow) {
            tagsCarousel {
                id("tagsCarousel")
                tagsData(tagsData)
                isSubscribedToPremium(isSubscribedToPremium)
                onTagClicked(data.onTagClicked)
            }
        }
    }

    private fun addReactions(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        val reactionsData = workoutDetailsViewState.data?.reactionsData?.data
        if (reactionsData != null) {
            workoutReactions {
                id("workoutReactions")
                reactionsData(reactionsData)
                onLikeClicked(
                    ThrottlingOnModelClickListener { model, _, _, _ ->
                        val reactionsDataModel = model.reactionsData()
                        val reactionSummary = reactionsDataModel.reactionSummary
                            ?: return@ThrottlingOnModelClickListener
                        val userReacted = reactionSummary.isUserReacted
                        reactionsDataModel.likeClickHandler?.invoke(
                            reactionsDataModel.workoutId,
                            reactionSummary.toBuilder()
                                .userReacted(!userReacted).build()
                        )
                    }
                )
                onAvatarClicked(
                    ThrottlingOnModelClickListener { model, _, _, _ ->
                        val workoutKey =
                            model.reactionsData().reactionSummary?.workoutKey
                                ?: return@ThrottlingOnModelClickListener
                        model.reactionsData().avatarClickHandler?.invoke(workoutKey)
                    }
                )
            }
        }
    }

    override fun addDiveLocation(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        val diveLocationData = workoutDetailsViewState.data?.diveLocationData?.data ?: return
        val domainWorkoutHeader = workoutDetailsViewState.data?.workoutHeader?.data
        diveLocation {
            id("diveLocation")
            workoutHeader(domainWorkoutHeader)
            diveLocationData(diveLocationData)
            fragmentManager(fragmentManager)
        }
    }

    private fun addComments(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        val commentsData = workoutDetailsViewState.data?.commentsData?.data
        if (commentsData != null) {
            if (!commentsData.description.isNullOrBlank()) {
                workoutDescription {
                    id("workoutDescription")
                    description(commentsData.description)
                }
            }
            if (commentsData.commentsCount > 4) {
                viewMoreComments {
                    id("viewMoreComments")
                    commentsCount(commentsData.commentsCount)
                    clickListener(
                        ThrottlingOnModelClickListener { _, _, _, _ ->
                            commentsData.viewMoreCommentClickHandler?.invoke()
                        }
                    )
                }
            }
            commentsData.comments.forEach { comment ->
                workoutComment {
                    id(comment.key)
                    commentText(comment.message)
                    realNameOrUsername(comment.realNameOrUsername)
                    clickListener(
                        ThrottlingOnModelClickListener { _, _, _, _ ->
                            commentsData.viewMoreCommentClickHandler?.invoke()
                        }
                    )
                }
            }
            addComment {
                id("addComment")
                text(commentsData.formText)
                workoutKey(commentsData.workoutKey)
                enabled(commentsData.formEnabled)
                editMode(commentsData.editModel)
                onTextSubmittedHandler(commentsData.onTextSubmittedHandler)
                addCommentClickHandler(commentsData.addCommentClickHandler)
                backKeyPressImeListener(commentsData.backKeyPressImeListener)
            }
        }
    }

    private fun addAchievements(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        val achievementsData = workoutDetailsViewState.data?.achievementsData?.data ?: return
        val domainWorkoutHeader = workoutDetailsViewState.data?.workoutHeader?.data ?: return

        if (achievementsData.isCurrentUserWorkout) {
            val combinedList: MutableList<BaseAchievementItem> = mutableListOf()
            val resources = context.resources
            val achievement = achievementsData.achievement
            val activityType = achievement?.run { ActivityType.valueOf(activityType) }
            val activityName = activityType?.run { context.getString(localizedStringId) }

            if (achievementsData.achievement != null && achievement != null && activityName != null) {
                // Add Personal Best achievements
                val personalBestAchievements = achievement.personalBestAchievements
                val personalBestItems = createDisplayTextForPersonalBests(
                    personalBestAchievements,
                    resources,
                    activityName
                )
                combinedList.addAll(personalBestItems)
            }

            // Add old "Fastest on this route" item to the list
            val summary = achievementsData.similarWorkoutSummary
            if (summary != null) {
                getDesc(summary)?.let { desc ->
                    val fastestOnThisRouteItem = FastestOnThisRouteItem(
                        title = desc,
                        workoutHeader = domainWorkoutHeader,
                        similarWorkoutSummary = summary
                    )
                    combinedList.add(fastestOnThisRouteItem)
                }
            }

            if (achievementsData.achievement != null && achievement != null && activityName != null) {
                // Add Cumulative achievements
                val cumulativeAchievements = achievement.cumulativeAchievements
                val cumulativeItems = createDisplayTextForCumulative(
                    cumulativeAchievements,
                    resources,
                    activityName
                )
                combinedList.addAll(cumulativeItems)
            }

            if (combinedList.isNotEmpty()) {
                combinedList.mapIndexed { index, achievementItem ->
                    when (achievementItem) {
                        is AchievementItem -> {
                            achievementsData.achievement?.let { achievement ->
                                achievementItem {
                                    id("${achievement.id}_$index")
                                    item(achievementItem)
                                }
                            }
                        }

                        is FastestOnThisRouteItem -> {
                            fastestOnRouteItem {
                                id("fastestOnThisRoute")
                                item(achievementItem)
                            }
                        }
                    }
                }
            }
        } else {
            if (achievementsData.oldFollowerRankings.isNotEmpty()) {
                val rankings = achievementsData.oldFollowerRankings
                val description: String = rankings[0].getDescription(context.resources) ?: return
                oldFollowerRanking {
                    id("oldFollowerRanking")
                    text(description)
                }
            }
        }
    }

    @StringRes
    private fun getDesc(summary: SimilarWorkoutSummary): Int? {
        val rank = summary.similarRoute ?: return null
        val ranking = rank.rank
        val total = rank.total
        return if (ranking == 1 && total > 1) {
            BaseR.string.achievement_route_first
        } else if (ranking == 2 && total > 1) {
            BaseR.string.achievement_route_second
        } else if (ranking == 3 && total > 2) {
            BaseR.string.achievement_route_third
        } else {
            null
        }
    }

    override fun addShareActivity(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        val shareActivityData = workoutDetailsViewState.data?.shareActivityData?.data
        if (shareActivityData != null && shareActivityData.isOwnWorkout) {
            shareActivity {
                id("shareActivity")
                sharingFlags(shareActivityData.sharingFlags)
                clickListener(
                    ThrottlingOnModelClickListener { _, _, clickedView, _ ->
                        shareActivityData.shareActivityClickHandler(
                            getSharingFlagByViewId(
                                clickedView.id
                            )
                        )
                    }
                )
            }
        }
    }

    override fun addRecentWorkoutSummary(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        val data = workoutDetailsViewState.data?.recentWorkoutSummaryData?.data ?: return
        if (data.summary.workouts > 0) {
            recentWorkoutSummary {
                id("recentWorkoutSummary")
                recentWorkoutSummary(data.summary)
                currentPage(data.currentSummaryPage)
                infoModelFormatter(infoModelFormatter)
                unitConverter(unitConverter)
                workoutHeaderController(workoutHeaderController)
                calendarProvider(calendarProvider)
                lifecycleScope(lifecycleScope)
                amplitudeAnalyticsTracker(analyticsTracker)
                onViewMoreClicked(
                    ThrottlingOnModelClickListener { _, _, _, _ ->
                        analyticsTracker.trackEvent(AnalyticsEvent.THIRTY_DAY_SUMMARY_VIEW_CLICK)
                        data.onViewMoreClicked()
                    }
                )
                onSummaryPageSelected(workoutDetailsViewState.data?.onSummaryPageSelected)
            }
        }
    }

    override fun addRecentTrendData(
        competitionWorkoutSummaryData: CompetitionWorkoutSummaryData?,
        workoutHeader: WorkoutHeader,
        recentTrendData: RecentTrendData,
        onPageSelected: OnPageSelected?
    ) {
        recentTrendData {
            id("recentTrend")
            competitionWorkoutSummaryData(competitionWorkoutSummaryData)
            workoutHeader(workoutHeader)
            onWorkoutCompetitionClick(
                competitionWorkoutSummaryData?.let {
                    ThrottlingOnModelClickListener { model, _, _, _ ->
                        model.competitionWorkoutSummaryData().onCompetitionWorkoutClick()
                    }
                }
            )
            recentTrendData(recentTrendData)
            infoModelFormatter(infoModelFormatter)
            currentPage(recentTrendData.currentPage)
            onPageSelected(onPageSelected)
            unitConverter(unitConverter)
        }
    }

    override fun addSwimmingCoach(swimmingExtension: SwimmingExtension) {
        val coachItems = swimmingExtension.getCoachValues()
        if (coachItems.isNotEmpty())
            swimmingCoach {
                id("swimmingCoach")
                coachValue(coachItems)
            }
    }
}
