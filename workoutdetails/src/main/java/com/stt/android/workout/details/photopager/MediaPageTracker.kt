package com.stt.android.workout.details.photopager

import androidx.viewpager2.widget.ViewPager2
import com.airbnb.epoxy.DiffResult
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.OnModelBuildFinishedListener
import com.stt.android.workout.details.WorkoutDetailsViewModelNew
import timber.log.Timber
import javax.inject.Inject

class MediaPageTracker
@Inject constructor() : OnModelBuildFinishedListener, ViewPager2.OnPageChangeCallback() {
    private var viewPager2: ViewPager2? = null
    private var controller: EpoxyController? = null
    private var viewModel: WorkoutDetailsViewModelNew? = null
    private var skipNextPageChangeEvent: Boolean = false

    override fun onModelBuildFinished(diffResult: DiffResult) {
        setCurrentPagerPosition()
    }

    override fun onPageSelected(position: Int) {
        Timber.d("Current photo pager position is: $position")
        if (!skipNextPageChangeEvent) {
            viewModel?.currentPhotoPagerPosition = position
        }
        skipNextPageChangeEvent = false
    }

    fun attach(
        viewPager2: ViewPager2,
        controller: EpoxyController,
        viewModel: WorkoutDetailsViewModelNew,
        // Set as true during orientation change prevents an extra page change event from messing
        // up the correct view pager position
        skipNextPageChangeEvent: Boolean = false
    ) {
        this.viewPager2 = viewPager2
        this.controller = controller
        this.viewModel = viewModel
        this.skipNextPageChangeEvent = skipNextPageChangeEvent
        setCurrentPagerPosition()
        viewPager2.registerOnPageChangeCallback(this)
        controller.addModelBuildListener(this)
    }

    fun detach() {
        viewPager2?.unregisterOnPageChangeCallback(this)
        controller?.removeModelBuildListener(this)
        viewPager2 = null
        controller = null
        viewModel = null
    }

    val currentPhotoPagerPosition: Int
        get() = viewModel?.currentPhotoPagerPosition ?: 0

    private fun setCurrentPagerPosition() {
        if (viewPager2?.currentItem != viewModel?.currentPhotoPagerPosition) {
            Timber.d("Setting photo pager to position: ${viewModel?.currentPhotoPagerPosition}")
            viewPager2?.setCurrentItem(viewModel?.currentPhotoPagerPosition ?: 0, false)
        }
    }
}
