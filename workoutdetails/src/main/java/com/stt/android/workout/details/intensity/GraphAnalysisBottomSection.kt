package com.stt.android.workout.details.intensity

import androidx.annotation.StringRes
import com.stt.android.utils.ZoneDurationData
import com.stt.android.workout.details.AnalysisDiveGasData
import kotlin.time.Duration

sealed class GraphAnalysisBottomSection {
    data class HeartRate(
        @StringRes val title: Int,
        val zoneDurationData: List<ZoneDurationData>,
    ) : GraphAnalysisBottomSection()

    data class DiveGases(
        val diveGases: List<AnalysisDiveGasData>
    ) : GraphAnalysisBottomSection()

    data class AerobicZone(
        val vo2Max: ZoneDurationData,
        val anaerobic: ZoneDurationData,
        val aerobic: ZoneDurationData,
    ) : GraphAnalysisBottomSection() {
        val hasValue: Boolean = vo2Max.duration != Duration.ZERO ||
            anaerobic.duration != Duration.ZERO ||
            aerobic.duration != Duration.ZERO
    }
}
