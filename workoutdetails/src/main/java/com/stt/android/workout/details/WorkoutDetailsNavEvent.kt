package com.stt.android.workout.details

import android.content.SharedPreferences
import androidx.annotation.IdRes
import androidx.lifecycle.LiveData
import androidx.navigation.NavController
import com.stt.android.analytics.AnalyticsPropertyValue.SourceProperty
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummary
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.graphanalysis.GraphAnalysisSelections
import com.stt.android.workout.details.graphanalysis.fullscreen.FullscreenGraphAnalysisActivityArgs
import com.stt.android.workouts.details.values.WorkoutValue
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR

interface WorkoutDetailsNavEvent {
    fun navigate(navController: NavController, featureTogglePreferences: SharedPreferences)
}

class WorkoutDetailsMapGraphAnalysisNavEvent constructor(
    private val workoutHeader: WorkoutHeader,
    private val multisportPartActivity: MultisportPartActivity?,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val autoPlayback: Boolean,
    private val initialMainGraphType: GraphType?,
    private val analyticsSource: String,
    private val analyticsContext: String?
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        if (navController.currentDestination?.id == R.id.workoutDetailsFragmentNew) {
            trackWorkoutAnalysisNavigationEvent()
            navController.navigate(
                WorkoutDetailsFragmentNewDirections
                    .actionWorkoutDetailsFragmentNewToWorkoutMapGraphAnalysisFragment(
                        workoutHeader.id,
                        multisportPartActivity,
                        autoPlayback,
                        analyticsSource,
                        initialMainGraphType,
                    )
            )
        }
    }

    private fun trackWorkoutAnalysisNavigationEvent() {
        val handler = CoroutineExceptionHandler { _, exception ->
            Timber.w(exception, "Tracking workout analysis (map) event failed")
        }

        GlobalScope.launch(handler) {
            workoutDetailsAnalytics.trackWorkoutAnalysisScreenEvent(
                analyticsSource,
                analyticsContext
            )
        }
    }
}

class WorkoutDetailsMultisportDetailsNavEvent(
    private val workoutId: Int,
    private val multisportPartActivity: MultisportPartActivity,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val sml: Sml? = null // Only for analytics
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        Timber.d("Navigating to multisport activity details")

        trackNavigationEvents()

        navController.navigate(
            WorkoutDetailsFragmentNewDirections
                .actionWorkoutDetailsFragmentNewToWorkoutDetailsFragmentNew(
                    workoutId,
                    multisportPartActivity,
                    false,
                )
        )
    }

    private fun trackNavigationEvents() {
        val sml = this.sml ?: return
        val handler = CoroutineExceptionHandler { _, exception ->
            Timber.w(exception, "Tracking workout details multisport screen event failed")
        }
        GlobalScope.launch(handler) {
            workoutDetailsAnalytics.trackWorkoutMultisportDetailsScreenEvent(
                multisportPartActivity,
                sml
            )
        }
    }
}

class WorkoutDetailsPhotoPagerNavEvent : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        if (navController.currentDestination?.id == R.id.workoutDetailsFragmentNew) {
            Timber.d("Navigating to full screen photo pager")
            navController.navigate(
                WorkoutDetailsFragmentNewDirections
                    .actionWorkoutDetailsFragmentNewToWorkoutPhotoPagerFragment()
            )
        }
    }
}

class WorkoutDetailsProfileNavEvent(val username: String) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        Timber.d("Navigating to user profile activity")
        navController.navigate(
            WorkoutDetailsFragmentNewDirections
                .actionWorkoutDetailsFragmentNewToUserProfileActivity(username)
        )
    }
}

class WorkoutDetailsEditNavEvent constructor(
    private val workoutId: Int,
    private val sourceProperty: String,
    private val showEditLocation: Boolean = false
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        Timber.d("Navigating to edit details activity")
        navController.navigate(
            WorkoutDetailsFragmentNewDirections
                .actionWorkoutDetailsFragmentNewToEditDetailsActivity(
                    workoutId,
                    sourceProperty,
                    showEditLocation
                )
        )
    }
}

class WorkoutDetailsSharePhotoNavEvent(
    val workoutHeader: WorkoutHeader,
    val index: Int,
    private val shareSource: SportieShareSource,
    val workoutExtension: WorkoutExtension?,
    val byScreenshot: Boolean,
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        navController.navigate(
            WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToWorkoutSharePreviewActivity(
                workoutHeader,
                index,
                shareSource
            )
        )
    }
}

class WorkoutDetailsFollowRouteNavEvent(
    private val workoutHeader: WorkoutHeader
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        navController.navigate(
            WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToFollowRoute(
                workoutHeader
            )
        )
    }
}

class WorkoutDetailsGhostTargetNavEvent(
    private val workoutHeader: WorkoutHeader
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        navController.navigate(
            WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToGhostTarget(
                workoutHeader
            )
        )
    }
}

class WorkoutDetailsValueDescriptionNavEvent(
    val workoutValue: WorkoutValue,
    val analyticsContext: String?,
    val workoutId: Int,
    val workoutKey: String?,
) :
    WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        Timber.d("Navigating to workout value description bottom sheet")
        throw Error(
            NotImplementedError(
                "Not currently supported. " +
                    "See commit 918a657e75661296527088af2d1616915cfb1cec for more info."
            )
        )
    }
}

class WorkoutDetailsFullscreenChartNavEvent(
    private val workoutHeader: WorkoutHeader,
    private val graphType: GraphType,
    private val analyticsSource: String,
    private val multisportPartActivity: MultisportPartActivity?,
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        val activityTypeForDiveCheck = if (multisportPartActivity != null) {
            ActivityType.valueOf(multisportPartActivity.activityType)
        } else {
            workoutHeader.activityType
        }

        if (activityTypeForDiveCheck.isDiving) {
            if (multisportPartActivity != null) {
                navController.navigate(
                    WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToLandscapeMultisportAnalysisGraphActivity(
                        graphType,
                        workoutHeader,
                        multisportPartActivity
                    )
                )
            } else {
                navController.navigate(
                    WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToLandscapeAnalysisGraphActivity(
                        graphType,
                        workoutHeader
                    )
                )
            }
        } else {
            navController.navigate(
                WorkoutDetailsNavDirections.actionGlobalToFullscreenGraphAnalysisActivity().actionId,
                FullscreenGraphAnalysisActivityArgs(
                    lockLandscape = true,
                    autoPlayback = false,
                    initialSelections = GraphAnalysisSelections(0L, null),
                    trackWorkoutAnalysisScreenEvent = false,
                    analyticsSource = analyticsSource,
                    workoutHeader = workoutHeader,
                    multisportPartActivity = multisportPartActivity,
                    initialMainGraphType = graphType
                ).toBundle()
            )
        }
    }
}


class WorkoutCompetitionNavEvent(
    private val workoutHeader: WorkoutHeader,
    private val competitionWorkoutSummary: CompetitionWorkoutSummary
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        navController.navigate(
            WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToWorkoutCompetitionActivity(
                competitionWorkoutSummary,
                workoutHeader
            )
        )
    }
}

class WorkoutDetailsSimilarWorkoutsNavEvent(
    private val workoutHeader: WorkoutHeader,
    private val similarTag: String
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        navController.navigate(
            WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToSimilarWorkoutsActivity(
                similarTag,
                workoutHeader
            )
        )
    }
}

class WorkoutDetailsFollowersListNavEvent(
    private val workoutKey: String
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        navController.navigate(
            WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToReactionUsersList(
                workoutKey
            )
        )
    }
}

class WorkoutDetailsCommentListDialogNavEvent(
    val workoutHeader: WorkoutHeader
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        val ormLiteWorkoutHeader = workoutHeader
        navController.navigate(
            WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToCommentsDialogFragment(
                ormLiteWorkoutHeader
            )
        )
    }
}

class WorkoutDetailsRecentWorkoutTrendActivityNavEvent(
    private val workoutHeader: WorkoutHeader
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        navController.navigate(
            WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToRecentWorkoutTrendActivity(
                workoutHeader
            )
        )
    }
}

class WorkoutDetailsWorkoutComparisonActivityNavEvent(
    private val current: WorkoutHeader,
    private val currentRank: Int,
    private val target: WorkoutHeader?,
    private val targetRank: Int,
    private val analyticsSource: String
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        navController.navigate(
            WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToWorkoutComparisonActivity(
                current,
                currentRank,
                target,
                targetRank,
                analyticsSource
            )
        )
    }
}

class WorkoutDetailsRecentWorkoutSummaryActivityNavEvent(
    private val workoutHeader: WorkoutHeader
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        navController.navigate(
            WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToRecentWorkoutSummaryActivity(
                workoutHeader
            )
        )
    }
}

class WorkoutDetailsFullscreenGraphAnalysisNavEvent(
    private val lockLandscape: Boolean = false,
    private val autoPlayback: Boolean = false,
    private val initialSelectedMillisInWorkout: Long = 0,
    private val trackWorkoutAnalysisScreenEvent: Boolean = false,
    private val analyticsSource: String,
    private val workoutHeader: WorkoutHeader,
    private val multisportPartActivity: MultisportPartActivity?
) : WorkoutDetailsNavEvent {
    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        navController.navigate(
            WorkoutDetailsNavDirections.actionGlobalToFullscreenGraphAnalysisActivity().actionId,
            FullscreenGraphAnalysisActivityArgs(
                lockLandscape = lockLandscape,
                autoPlayback = autoPlayback,
                initialSelections = GraphAnalysisSelections(initialSelectedMillisInWorkout, null),
                trackWorkoutAnalysisScreenEvent = trackWorkoutAnalysisScreenEvent,
                analyticsSource = analyticsSource,
                workoutHeader = workoutHeader,
                multisportPartActivity = multisportPartActivity
            ).toBundle()
        )
    }
}

class DiveTrackFullscreenNavEvent(private val workoutHeader: WorkoutHeader) : WorkoutDetailsNavEvent {

    override fun navigate(
        navController: NavController,
        featureTogglePreferences: SharedPreferences
    ) {
        Timber.i("navigate to dive track fullscreen.")
        navController.navigate(WorkoutDetailsFragmentNewDirections.actionWorkoutDetailsFragmentNewToFullscreenDiveTrackActivity(workoutHeader))
    }
}

fun createMenuClickedEvent(
    @IdRes menuId: Int,
    workoutHeader: WorkoutHeader
): WorkoutDetailsNavEvent? {
    return when (menuId) {
        R.id.followRoute -> WorkoutDetailsFollowRouteNavEvent(workoutHeader)
        BaseR.id.edit -> WorkoutDetailsEditNavEvent(workoutHeader.id, SourceProperty.EDIT_BUTTON)
        R.id.ghostTarget -> WorkoutDetailsGhostTargetNavEvent(workoutHeader)
        else -> null
    }
}

@ActivityRetainedScoped
class NavigationEventDispatcher
@Inject constructor() {
    private val _navigationEvent = SingleLiveEvent<WorkoutDetailsNavEvent>()
    val navigationEvent: LiveData<WorkoutDetailsNavEvent> = _navigationEvent

    fun dispatchEvent(navEvent: WorkoutDetailsNavEvent) {
        _navigationEvent.value = navEvent
    }
}
