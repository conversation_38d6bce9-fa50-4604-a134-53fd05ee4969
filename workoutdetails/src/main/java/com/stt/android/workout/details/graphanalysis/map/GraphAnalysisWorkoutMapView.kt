package com.stt.android.workout.details.graphanalysis.map

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Point
import android.view.LayoutInflater
import android.view.MotionEvent
import com.google.android.gms.maps.model.LatLng
import com.stt.android.maps.MarkerZPriority
import com.stt.android.maps.SuuntoBitmapDescriptorFactory
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.mapbox.MapboxMapsProvider
import com.stt.android.ui.components.WorkoutMapView
import com.stt.android.workout.details.databinding.GraphAnalysisWorkoutMapViewBinding
import com.stt.android.workout.details.graphanalysis.laps.LapMarker

/**
 *
 * Users of this class must forward all the life cycle methods from the Activity or Fragment
 * containing this view to the corresponding ones in this class. In particular, you must forward on
 * the following methods:
 *
 *  * onCreate(Bundle)
 *  * onStart()
 *  * onResume()
 *  * onPause()
 *  * onStop()
 *  * onDestroy()
 *  * onSaveInstanceState()
 *  * onLowMemory()
 *
 */
@SuppressLint("ViewConstructor", "ClickableViewAccessibility") // Not used from XML
class GraphAnalysisWorkoutMapView(
    context: Context,
    suuntoMapOptions: SuuntoMapOptions?,
    topMapPadding: Int?
) : WorkoutMapView(context, suuntoMapOptions, topMapPadding), SuuntoMap.OnMarkerClickListener {
    private val graphAnalysisMapViewBinding: GraphAnalysisWorkoutMapViewBinding =
        GraphAnalysisWorkoutMapViewBinding.inflate(LayoutInflater.from(context), this)

    private val bitmapDescriptorFactory: SuuntoBitmapDescriptorFactory =
        SuuntoBitmapDescriptorFactory(context)

    private var marker: SuuntoMarker? = null
    private var locationMarker3dShown = false
    private var bottomMapPadding: Int = 0

    private var lapMarkersData: List<LapMarker>? = null
    private val lapMarkers = mutableListOf<SuuntoMarker>()

    private val hideCenteredLocationViewRunnable =
        Runnable { graphAnalysisMapViewBinding.centeredLocationView.visibility = INVISIBLE }

    init {
        // Merge graph analysis view layout with map view layout from parent
        graphAnalysisMapViewBinding.centeredLocationView.setImageBitmap(
            bitmapDescriptorFactory.forCurrentLocationDot().asBitmap()
        )

        graphAnalysisMapViewBinding.touchDetector.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                hideCenteredLocationView()
            }
            false
        }

        getMapAsync { map ->
            map.addOnMarkerClickListener(this)
        }
    }

    fun updateLapMarkers(markers: List<LapMarker>) {
        lapMarkersData = markers
        lapMarkers.forEach { it.remove() }
        lapMarkers.clear()
        getMapAsync { map ->
            fun addMarker(marker: LapMarker) = map.addMarker(
                createLapMarker(
                    marker.number,
                    marker.position,
                    if (marker.highlight) 1.0f else 0.5f,
                )
            )

            val newMarkers = map.batchUpdate {
                // Display order: When there is overlap between lap markers,
                // the ones from earlier laps should be displayed on top
                if (map.getProviderName() == MapboxMapsProvider.NAME) {
                    markers.mapNotNull { addMarker(it) }
                } else {
                    markers.reversed().mapNotNull { addMarker(it) }.reversed()
                }
            }
            lapMarkers.addAll(newMarkers)
        }
    }

    private fun createLapMarker(
        lapNumber: Int,
        latLan: LatLng,
        alpha: Float,
    ) = SuuntoMarkerOptions().anchor(0.5f, 0.5f)
        .icon(bitmapDescriptorFactory.forLapMapDot(lapNumber))
        .position(latLan)
        .alpha(alpha)
        .iconScale(0.8f)
        .zPriority(MarkerZPriority.LAP_POSITION)

    fun update2dLocationMarker(position: LatLng) {
        if (locationMarker3dShown) {
            remove3dLocationMarker()
            locationMarker3dShown = false
        }

        if (marker == null && map != null) {
            marker = map!!.addMarker(
                SuuntoMarkerOptions().anchor(0.5f, 0.5f)
                    .icon(bitmapDescriptorFactory.forCurrentLocationDot())
                    .position(position)
                    .iconScale(0.8f)
                    .zPriority(MarkerZPriority.SELECTED_GEOPOINT)
            )
        } else {
            marker?.setPosition(position)
        }
    }

    fun update3dLocationMarker(position: LatLng, altitude: Double) {
        if (!locationMarker3dShown) {
            remove2dLocationMarker()
            locationMarker3dShown = true
        }
        map?.update3dLocation(position, altitude)
    }

    private fun remove2dLocationMarker() {
        hideCenteredLocationView()
        marker?.remove()
        marker = null
    }

    private fun remove3dLocationMarker() {
        map?.update3dLocation(null, 0.0)
    }

    override fun setBottomMapPadding(bottomPadding: Int) {
        super.setBottomMapPadding(bottomPadding)
        bottomMapPadding = bottomPadding
    }

    fun getBottomMapPadding(): Int {
        return bottomMapPadding
    }

    fun showCenteredLocationView(centerPoint: Point) {
        with(graphAnalysisMapViewBinding.centeredLocationView) {
            x = centerPoint.x.toFloat() - (width / 2)
            y = centerPoint.y.toFloat() - (height / 2)
        }

        if (marker != null && marker!!.isVisible()) {
            removeCallbacks(hideCenteredLocationViewRunnable)
            graphAnalysisMapViewBinding.centeredLocationView.visibility = VISIBLE
            marker!!.setVisible(false)
        }
    }

    fun hideCenteredLocationView() {
        if (marker != null && !marker!!.isVisible()) {
            marker!!.setVisible(true)
            // It takes a while for the marker to get visible. Hide the centered location view
            // with a small delay to avoid flickering.
            postDelayed(hideCenteredLocationViewRunnable, 50)
        }
    }

    fun setMapClicksDisabled(disabled: Boolean) {
        mapView.clicksDisabled = disabled
    }

    override fun onAnnotationClick(annotation: SuuntoMarker): Boolean {
        val index = lapMarkers.indexOf(annotation).takeIf { it != -1 } ?: return false
        val marker = lapMarkersData?.getOrNull(index) ?: return false
        marker.onSelected.invoke()
        return true
    }
}
