package com.stt.android.workout.details.intensity

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.UserSettings
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.domain.workouts.isMultisport
import com.stt.android.extensions.findResumePauseEventPairs
import com.stt.android.logbook.SuuntoLogbookSample
import com.stt.android.usecases.CalculateDFAUseCase
import com.stt.android.usecases.CalculateRRDataFromRawIbiUseCase
import com.stt.android.utils.ZoneSenseUtils
import com.suunto.algorithms.data.HeartRate.Companion.bpm
import com.suunto.algorithms.ddfa.DynamicDFAUtils
import com.suunto.connectivity.zonesense.ZoneSenseSyncDataProvider
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.max
import kotlin.math.min

class CalculateDFAUseCaseImpl @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val calculateRRDataFromRawIbiUseCase: CalculateRRDataFromRawIbiUseCase,
    private val zoneSenseUtils: ZoneSenseUtils,
    private val zoneSenseDataProvider: ZoneSenseSyncDataProvider,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : CalculateDFAUseCase {
    private val dynamicDFAUtils = DynamicDFAUtils()

    override suspend operator fun invoke(
        rrs: List<Int>,
        maxHeartRateSetByTheUserAtTheTimeOfRecordingTheWorkout: Double,
        previousBaseline: Double?,
        ignoreFirstTenMinutes: Boolean,
    ): DynamicDFAUtils.Result = withContext(coroutinesDispatchers.computation) {
        val param = DynamicDFAUtils.Param(
            maxHr = maxHeartRateSetByTheUserAtTheTimeOfRecordingTheWorkout.bpm,
            rrs = rrs,
            previousCumulativeBaseline = previousBaseline,
            ignoreFirstTenMinutes = ignoreFirstTenMinutes,
        )

        Timber.d("Calculating DFA, param = %s", param)
        dynamicDFAUtils.calculate(param)
            .also { result ->
                Timber.d(
                    "Baseline = %f, cumulative baseline = %f, aerobicThreshold = %f, anaerobicThreshold = %f, time in zones = [%d, %d, %d]",
                    result.baseline,
                    result.cumulativeBaseline,
                    result.zones.aerobicThreshold?.inBpm,
                    result.zones.anaerobicThreshold?.inBpm,
                    result.zones.timeInAerobicZone.inWholeMilliseconds,
                    result.zones.timeInAnaerobicZone.inWholeMilliseconds,
                    result.zones.timeInVo2MaxZone.inWholeMilliseconds,
                )
            }
    }

    override suspend fun invoke(
        workoutId: Int,
        sml: Sml?,
    ): List<DynamicDFAUtils.Result> = withContext(coroutinesDispatchers.computation) {
        if (sml == null) return@withContext emptyList()
        if (!zoneSenseUtils.isZoneSenseEnabled(workoutId)) {
            return@withContext emptyList()
        }

        val workoutHeader = workoutHeaderDataSource.findById(workoutId)
            ?: return@withContext emptyList()
        if (workoutHeader.isMultisport) {
            calculateDdfaForMultisport(workoutHeader, sml)
        } else {
            listOfNotNull(calculateDdfaForNonMultisport(workoutHeader, sml))
        }
    }

    private suspend fun calculateDdfaForMultisport(
        workoutHeader: WorkoutHeader,
        sml: Sml,
    ): List<DynamicDFAUtils.Result> {
        val maxHeartRateInBpm = sml.maxHeartRateInBpm(workoutHeader)
        val resumePauseTimePairs = sml.resumePauseTimePairs
        val ddfaResults = mutableListOf<DynamicDFAUtils.Result>()

        var currentPartIndex = 0
        var currentPart = sml.streamData.multisportPartActivities[currentPartIndex]
        val rawIbis = mutableListOf<SuuntoLogbookSample.HR.RawIbi>()
        sml.streamData
            .samplePoint
            .forEach { samplePoint ->
                val rawIbi = samplePoint.rawIbiSuuntoLogbookSample as? SuuntoLogbookSample.HR.RawIbi
                    ?: return@forEach

                if (rawIbi.timestamp <= currentPart.stopTime) {
                    rawIbis.add(rawIbi)
                } else {
                    calculateDdfa(
                        maxHeartRateInBpm = maxHeartRateInBpm,
                        workoutId = workoutHeader.id,
                        multisportPart = currentPart,
                        resumePauseTimePairs = resumePauseTimePairs,
                        rawIbis = rawIbis,
                        ignoreFirstTenMinutes = currentPartIndex == 0,
                    )?.let(ddfaResults::add)

                    rawIbis.clear()

                    currentPartIndex++
                    currentPart = sml.streamData.multisportPartActivities[currentPartIndex]
                }
            }

        calculateDdfa(
            maxHeartRateInBpm = maxHeartRateInBpm,
            workoutId = workoutHeader.id,
            multisportPart = currentPart,
            resumePauseTimePairs = resumePauseTimePairs,
            rawIbis = rawIbis,
            ignoreFirstTenMinutes = currentPartIndex == 0,
        )?.let(ddfaResults::add)

        return ddfaResults
    }

    private suspend fun calculateDdfa(
        maxHeartRateInBpm: Double,
        workoutId: Int,
        multisportPart: MultisportPartActivity,
        resumePauseTimePairs: List<Pair<Long, Long>>,
        rawIbis: List<SuuntoLogbookSample.HR.RawIbi>,
        ignoreFirstTenMinutes: Boolean,
    ): DynamicDFAUtils.Result? {
        if (rawIbis.isEmpty()) {
            return null
        }

        val filteredResumePauseTimePairs = resumePauseTimePairs.mapNotNull { (resumeTimestamp, pauseTimestamp) ->
            if (resumeTimestamp >= multisportPart.stopTime || pauseTimestamp <= multisportPart.startTime) {
                return@mapNotNull null
            }
            max(resumeTimestamp, multisportPart.startTime) to min(pauseTimestamp, multisportPart.stopTime)
        }

        val rrs = calculateRRDataFromRawIbiUseCase(
            workoutResumePauseTimePairs = filteredResumePauseTimePairs,
            rawIbis = rawIbis,
        )
            .takeUnless(List<*>::isEmpty)
            ?: return null

        return invoke(
            maxHeartRateSetByTheUserAtTheTimeOfRecordingTheWorkout = maxHeartRateInBpm,
            rrs = rrs,
            previousBaseline = zoneSenseDataProvider.getPreviousBaseline(
                workoutId = workoutId,
                activityTypeId = multisportPart.activityType,
            ),
            ignoreFirstTenMinutes = ignoreFirstTenMinutes,
        ).takeUnless(DynamicDFAUtils.Result::isEmpty)
    }

    private suspend fun calculateDdfaForNonMultisport(
        workoutHeader: WorkoutHeader,
        sml: Sml,
    ): DynamicDFAUtils.Result? {
        val rawIbis = sml
            .streamData
            .samplePoint
            .mapNotNull { samplePoint ->
                samplePoint.rawIbiSuuntoLogbookSample as? SuuntoLogbookSample.HR.RawIbi
            }
            .takeUnless(List<*>::isEmpty)
            ?: return null
        val rrs = calculateRRDataFromRawIbiUseCase(
            workoutResumePauseTimePairs = sml.resumePauseTimePairs,
            rawIbis = rawIbis,
        )
            .takeUnless(List<*>::isEmpty)
            ?: return null

        return invoke(
            maxHeartRateSetByTheUserAtTheTimeOfRecordingTheWorkout = sml.maxHeartRateInBpm(workoutHeader),
            rrs = rrs,
            previousBaseline = zoneSenseDataProvider.getPreviousBaseline(
                username = workoutHeader.username,
                activityTypeId = workoutHeader.activityTypeId,
                untilInMillsSinceEpoch = workoutHeader.startTime,
            ),
            ignoreFirstTenMinutes = true,
        ).takeUnless(DynamicDFAUtils.Result::isEmpty)
    }

    private fun Sml.maxHeartRateInBpm(workoutHeader: WorkoutHeader): Double {
        summary.header?.personal?.personalMaxHrInBpm
            ?.let { return it.toDouble() }

        workoutHeader.heartRateUserSetMax
            .takeIf { it > 0.0 }
            ?.let { return it }

        return if (currentUserController.username == workoutHeader.username) {
            userSettingsController.settings.hrMaximum.toDouble()
        } else {
            UserSettings.DEFAULT_MAX_HR.toDouble()
        }
    }

    private companion object {
        val Sml.resumePauseTimePairs: List<Pair<Long, Long>> get() = findResumePauseEventPairs
            .map { (resume, pause) ->
                resume.timestamp to pause.timestamp
            }
    }
}
