<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="workoutKey"
            type="String" />

        <variable
            name="showFollowButton"
            type="boolean" />

        <variable
            name="userFollowStatus"
            type="com.stt.android.follow.UserFollowStatus" />

        <variable
            name="onUserClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="onFollowClickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/smaller_padding"
        android:paddingLeft="@dimen/padding"
        android:paddingRight="@dimen/padding"
        android:paddingTop="@dimen/smaller_padding">

        <ImageView
            android:id="@+id/user_avatar"
            android:foreground="?android:attr/selectableItemBackgroundBorderless"
            android:layout_width="@dimen/size_icon_large"
            android:layout_height="@dimen/size_icon_large"
            android:layout_gravity="center_vertical"
            android:clickable="true"
            android:focusable="true"
            android:onClick="@{onUserClickListener}"
            android:contentDescription="@string/user_profile_picture"
            app:avatar="@{userFollowStatus.profileImageUrl}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_default_profile_image_light"
            tools:src="@drawable/ic_default_profile_image_light"
            tools:ignore="UnusedAttribute" />

        <TextView
            android:id="@+id/name"
            android:background="?android:attr/selectableItemBackground"
            style="@style/Body.Medium.Secondary"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:clickable="true"
            android:focusable="true"
            android:onClick="@{onUserClickListener}"
            android:text="@{userFollowStatus.realNameOrUsername}"
            android:layout_marginStart="@dimen/padding"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/following_status"
            app:layout_constraintStart_toEndOf="@id/user_avatar"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="This is actually a very long Brazilian name which is longer than two rows" />

        <TextView
            android:id="@+id/following_status"
            style="@style/HeaderLabel.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:onClick="@{onFollowClickListener}"
            android:drawablePadding="2dp"
            android:layout_marginStart="@dimen/padding"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:padding="@dimen/smaller_padding"
            app:followStatus="@{userFollowStatus.status}"
            app:visible="@{showFollowButton}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="FOLLOW" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
