package com.stt.android.menstrualcycle.onboarding.onboardingview.composes

import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material3.DatePickerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.menstrualcycle.domain.LengthConstants.CYCLE_LENGTH_SELECTABLE
import com.stt.android.menstrualcycle.domain.LengthConstants.DEFAULT_PERIOD_DURATION
import com.stt.android.menstrualcycle.domain.LengthConstants.PERIOD_DURATION_SELECTABLE
import com.stt.android.menstrualcycle.onboarding.R
import com.stt.android.menstrualcycle.onboarding.onboardingview.OnboardingMenstrualCycleRegularity
import com.stt.android.menstrualcycle.onboarding.onboardingview.OnboardingSummary
import com.stt.android.menstrualcycle.onboarding.onboardingview.SummaryEditType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SummaryEditSheetContent(
    summaryEditType: SummaryEditType,
    summaryEditSheetState: ModalBottomSheetState,
    coroutineScope: CoroutineScope,
    onboardingSummary: OnboardingSummary,
    onCycleRegularityChange: (OnboardingMenstrualCycleRegularity) -> Unit,
    onCycleLengthChange: (Int?) -> Unit,
    onPeriodDurationChange: (Int) -> Unit,
    lastPeriodStartedDatePickerState: DatePickerState,
    onLastPeriodChange: (Long?, Int) -> Unit,
    onWheelScrollInProgressChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    fun hideSheet(block: (() -> Unit)? = null) {
        coroutineScope.launch { summaryEditSheetState.hide() }
        block?.invoke()
    }

    when (summaryEditType) {
        SummaryEditType.CYCLE_REGULARITY -> {
            fun onClick(regularity: OnboardingMenstrualCycleRegularity) =
                hideSheet { onCycleRegularityChange(regularity) }

            MenstrualCycleRegularity(
                onRegularClick = ::onClick,
                onIrregularClick = ::onClick,
                onNotSureClick = ::onClick,
                showEndDivider = false,
                modifier = modifier
            )
        }

        SummaryEditType.CYCLE_LENGTH -> {
            fun onClick(cycleLength: Int?) = hideSheet { onCycleLengthChange(cycleLength) }
            MenstrualCycleDays(
                title = stringResource(id = R.string.onboarding_menstrual_cycle_cycle_length_title),
                selectableList = CYCLE_LENGTH_SELECTABLE,
                selectedIndex = onboardingSummary.getCycleLengthSelectedIndex(),
                onDoneClick = { onClick(it) },
                onNotSureClick = { onClick(null) },
                modifier = modifier
            )
        }

        SummaryEditType.PERIOD_DURATION -> {
            var openNotSureDialog by rememberSaveable { mutableStateOf(false) }
            if (openNotSureDialog) {
                DurationNotSureDialog(
                    onConfirmClick = {
                        hideSheet {
                            onPeriodDurationChange(DEFAULT_PERIOD_DURATION)
                            openNotSureDialog = false
                        }
                    },
                    onDismissClick = { openNotSureDialog = false }
                )
            }
            MenstrualCycleDays(
                title = stringResource(id = R.string.onboarding_menstrual_cycle_duration_title),
                selectableList = PERIOD_DURATION_SELECTABLE,
                selectedIndex = onboardingSummary.getCycleDurationSelectedIndex(),
                onDoneClick = { hideSheet { onPeriodDurationChange(it) } },
                onNotSureClick = { openNotSureDialog = true },
                modifier = modifier
            )
        }

        SummaryEditType.LAST_PERIOD_STARTED -> {
            MenstrualCycleLastPeriod(
                onboardingSummary = onboardingSummary,
                onDoneClick = { lastPeriodStarted, lastPeriodDuration ->
                    hideSheet { onLastPeriodChange(lastPeriodStarted, lastPeriodDuration) }
                },
                onNotSureClick = { hideSheet { onLastPeriodChange(null, it) } },
                datePickerState = lastPeriodStartedDatePickerState,
                onWheelScrollInProgressChanged = onWheelScrollInProgressChanged,
                modifier = modifier
            )
        }
    }
}

