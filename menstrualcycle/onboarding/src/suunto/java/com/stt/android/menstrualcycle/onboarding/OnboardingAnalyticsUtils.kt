package com.stt.android.menstrualcycle.onboarding

import android.content.SharedPreferences
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsEventProperty.MENSTRUAL_CYCLE_ONBOARDING_DONE_CYCLE_LENGTH
import com.stt.android.analytics.AnalyticsEventProperty.MENSTRUAL_CYCLE_ONBOARDING_DONE_CYCLE_REGULARITY
import com.stt.android.analytics.AnalyticsEventProperty.MENSTRUAL_CYCLE_ONBOARDING_DONE_PERIOD_DURATION
import com.stt.android.analytics.AnalyticsEventProperty.MENSTRUAL_CYCLE_ONBOARDING_DONE_REASON
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.menstrualcycle.OnboardingDoneReason
import com.stt.android.menstrualcycle.onboarding.onboardingview.OnboardingSummary
import com.stt.android.utils.STTConstants
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper
import javax.inject.Inject

class OnboardingAnalyticsUtils @Inject constructor(
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    @SuuntoSharedPrefs private val sharedPreferences: SharedPreferences,
) {

    fun trackOnboardingDone(
        reason: OnboardingDoneReason?,
        onboardingSummary: OnboardingSummary
    ) {
        getWatchProperties().apply {
            reason?.let { put(MENSTRUAL_CYCLE_ONBOARDING_DONE_REASON, it.analyticsName) }
            put(
                MENSTRUAL_CYCLE_ONBOARDING_DONE_CYCLE_REGULARITY,
                onboardingSummary.cycleRegularity.analyticsName
            )
            onboardingSummary.cycleLength?.let {
                put(MENSTRUAL_CYCLE_ONBOARDING_DONE_CYCLE_LENGTH, it)
            }
            put(MENSTRUAL_CYCLE_ONBOARDING_DONE_PERIOD_DURATION, onboardingSummary.periodDuration)
        }.also {
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.MENSTRUAL_CYCLE_ONBOARDING_DONE, it)
        }
    }

    private fun getWatchProperties(): AnalyticsProperties {
        val firmwareVersion: String? = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_FW_VERSION,
            "N/A"
        )
        val watchVariantName: String? = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
            "N/A"
        )
        val watchModel: String? = watchVariantName?.let {
            AnalyticsDevicePropertyHelper.getWatchModelNameForVariantName(it)
        }
        val watchSerial: String? = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_SERIAL_NUMBER,
            "N/A"
        )

        return AnalyticsProperties().apply {
            put(AnalyticsEventProperty.SUUNTO_WATCH_FIRMWARE_VERSION, firmwareVersion)
            put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModel)
            put(AnalyticsEventProperty.SUUNTO_WATCH_SERIAL_NUMBER, watchSerial)
        }
    }
}
