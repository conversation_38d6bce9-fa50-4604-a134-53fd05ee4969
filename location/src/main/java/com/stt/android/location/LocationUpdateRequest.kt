package com.stt.android.location

data class LocationUpdateRequest internal constructor(
    @get:JvmName("intervalInMilliSeconds")
    val intervalInMilliSeconds: Long,
    @get:JvmName("smallestDistanceInMeters")
    val smallestDistanceInMeters: Float
) {

    fun toBuilder(): Builder =
        Builder(
            intervalInMilliSeconds = intervalInMilliSeconds,
            smallestDistanceInMeters = smallestDistanceInMeters
        )

    class Builder internal constructor(
        private var intervalInMilliSeconds: Long = 0L,
        private var smallestDistanceInMeters: Float = 0f
    ) {
        fun intervalInMilliSeconds(milliSeconds: Long): Builder =
            apply { this.intervalInMilliSeconds = milliSeconds }

        fun smallestDistanceInMeters(meters: Float): Builder =
            apply { this.smallestDistanceInMeters = meters }

        fun build(): LocationUpdateRequest =
            LocationUpdateRequest(
                intervalInMilliSeconds = intervalInMilliSeconds,
                smallestDistanceInMeters = smallestDistanceInMeters
            )
    }

    companion object {
        @JvmStatic
        fun builder(): Builder = Builder()
    }
}
