package com.stt.android.remote.gearevent

import javax.inject.Inject

class GearEventRemoteApi @Inject constructor(
    private val gearEventRestApi: GearEventRestApi,
) {
    suspend fun sendPairEvent(serial: String, event: GearPairEvent) {
        gearEventRestApi.sendPairEvent(serial, event)
    }

    suspend fun sendUnpairEvent(serial: String) {
        gearEventRestApi.sendUnpairEvent(serial)
    }

    suspend fun getGear(): List<GearPairEvent> {
        return gearEventRestApi.getGear()
            .payloadOrThrow()
    }
}
