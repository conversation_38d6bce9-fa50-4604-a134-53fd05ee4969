<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:locale="en">

    <string name="recording">Recording</string>
    <string name="paused">Paused</string>
    <string name="auto_paused">Autopaused</string>

    <string name="workout_values_headline_duration">Duration</string>
    <string name="workout_values_headline_total_time">Total time</string>
    <string name="workout_values_headline_pause_time">Pause time</string>
    <string name="workout_values_headline_moving_time">Moving time</string>
    <string name="workout_values_headline_rest_time">Rest time</string>
    <string name="workout_values_headline_ascent_duration">Ascent duration</string>
    <string name="workout_values_headline_descent_duration">Descent duration</string>
    <string name="workout_values_headline_avg_temperature">Temperature</string>
    <string name="workout_values_headline_max_temperature">Max temperature</string>
    <string name="workout_values_headline_high_altitude">Highest point</string>
    <string name="workout_values_headline_low_altitude">Lowest point</string>
    <string name="workout_values_headline_peak_vertical_speed_30s">Peak vertical speed 30s</string>
    <string name="workout_values_headline_peak_vertical_speed_1m">Peak vertical speed 1min</string>
    <string name="workout_values_headline_peak_vertical_speed_3m">Peak vertical speed 3min</string>
    <string name="workout_values_headline_peak_vertical_speed_5m">Peak vertical speed 5min</string>
    <string name="average">Average</string>
    <string name="average_with_value_and_unit">Average %1$d %2$s</string>
    <string name="average_with_decimal_value_and_unit">Average %.2f %2$s</string>
    <string name="duration_capital">DURATION</string>
    <string name="estimated_duration">Est. duration</string>
    <string name="workout_values_headline_distance">Distance</string>
    <string name="workout_values_headline_nautical_distance">Nautical distance</string>
    <string name="distance_capital">DISTANCE</string>
    <string name="energy_capital">CALORIES</string>
    <string name="energy_normal">Energy</string>
    <string name="workout_values_headline_energy">Calories</string>
    <string name="workout_values_headline_pte">PTE</string>
    <string name="workout_values_headline_feeling">Feeling</string>
    <string name="workout_values_headline_avg_power">Avg power</string>
    <string name="workout_values_headline_avg_power_with_zero">Power with zeros</string>
    <string name="workout_values_headline_peak_power_30s">Peak power 30s</string>
    <string name="workout_values_headline_peak_power_1m">Peak power 1min</string>
    <string name="workout_values_headline_peak_power_3m">Peak power 3min</string>
    <string name="workout_values_headline_peak_power_5m">Peak power 5min</string>
    <string name="workout_values_headline_recoveryTime">Recovery time</string>
    <string name="workout_values_headline_peak_epoc">EPOC</string>
    <string name="workout_values_headline_vo2_max">Est VO₂Max</string>
    <string name="workout_values_headline_performance">Performance</string>
    <string name="workout_values_headline_swolf">SWOLF</string>
    <string name="workout_values_headline_swim_stroke_rate">Stroke rate</string>
    <string name="workout_values_headline_shot_count">Shot count</string>
    <string name="workout_values_headline_catch_count">Catch count</string>
    <string name="workout_values_headline_aerobic_hr_threshold">Aerobic HR threshold</string>
    <string name="workout_values_headline_anaerobic_hr_threshold">Anaerobic HR threshold</string>
    <string name="workout_values_headline_zone_sense_baseline" translatable="false">ZoneSense baseline</string>
    <string name="workout_values_headline_zone_sense_cumulative_baseline" translatable="false">ZoneSense cumulative baseline</string>
    <string name="workout_values_headline_aerobic_power_threshold">Aerobic power threshold</string>
    <string name="workout_values_headline_anaerobic_power_threshold">Anaerobic power threshold</string>
    <string name="workout_values_headline_aerobic_pace_threshold">Aerobic pace threshold</string>
    <string name="workout_values_headline_anaerobic_pace_threshold">Anaerobic pace threshold</string>
    <string name="workout_values_headline_aerobic_duration">Aerobic duration</string>
    <string name="workout_values_headline_anaerobic_duration">Anaerobic duration</string>
    <string name="workout_values_headline_vo2_max_duration">VO₂Max duration</string>
    <string name="workout_values_headline_breathing_rate">Breathing rate</string>
    <string name="workout_values_headline_breaststroke_duration">Duration, breaststroke</string>
    <string name="workout_values_headline_breaststroke_percent">Breaststroke percentage</string>
    <string name="workout_values_headline_breaststroke_glide_time">Glide time, breaststroke</string>
    <string name="workout_values_headline_breaststroke_max_breath_angle">Max. breath angle, breaststroke</string>
    <string name="workout_values_headline_breaststroke_avg_breath_angle">Avg. breath angle, breaststroke</string>
    <string name="workout_values_headline_freestyle_duration">Duration, freestyle</string>
    <string name="workout_values_headline_freestyle_percent">Freestyle percentage</string>
    <string name="workout_values_headline_freestyle_avg_breath_angle">Avg. breath angle, freestyle</string>
    <string name="workout_values_headline_freestyle_max_breath_angle">Max. breath angle, freestyle</string>
    <string name="workout_values_headline_freestyle_head_angel">Head angle, freestyle</string>
    <string name="workout_values_headline_breaststroke_head_angel">Head angle, breaststroke</string>
    <string name="workout_values_headline_stride_length">Avg. stride length</string>
    <string name="workout_values_headline_step_length">Avg. step length</string>
    <string name="workout_values_headline_climbs">Climbs</string>
    <string name="workout_values_headline_climbs_category_1">Climbs (Category 1)</string>
    <string name="workout_values_headline_climbs_category_2">Climbs (Category 2)</string>
    <string name="workout_values_headline_climbs_category_3">Climbs (Category 3)</string>
    <string name="workout_values_headline_climbs_category_4">Climbs (Category 4)</string>
    <string name="workout_values_headline_climbs_category_hc">Climbs (Hors Categorie)</string>
    <string name="workout_values_headline_climb_ascent_category_1">Ascent (Category 1)</string>
    <string name="workout_values_headline_climb_ascent_category_2">Ascent (Category 2)</string>
    <string name="workout_values_headline_climb_ascent_category_3">Ascent (Category 3)</string>
    <string name="workout_values_headline_climb_ascent_category_4">Ascent (Category 4)</string>
    <string name="workout_values_headline_climb_ascent_category_hc">Ascent (Hors Categorie)</string>
    <string name="workout_values_headline_climb_distance_category_1">Distance (Category 1)</string>
    <string name="workout_values_headline_climb_distance_category_2">Distance (Category 2)</string>
    <string name="workout_values_headline_climb_distance_category_3">Distance (Category 3)</string>
    <string name="workout_values_headline_climb_distance_category_4">Distance (Category 4)</string>
    <string name="workout_values_headline_climb_distance_category_hc">Distance (Hors Categorie)</string>
    <string name="workout_values_headline_climb_duration_category_1">Duration (Category 1)</string>
    <string name="workout_values_headline_climb_duration_category_2">Duration (Category 2)</string>
    <string name="workout_values_headline_climb_duration_category_3">Duration (Category 3)</string>
    <string name="workout_values_headline_climb_duration_category_4">Duration (Category 4)</string>
    <string name="workout_values_headline_climb_duration_category_hc">Duration (Hors Categorie)</string>
    <string name="workout_values_headline_ascent_speed">Ascent speed</string>
    <string name="workout_values_headline_descent_speed">Descent speed</string>
    <string name="workout_values_headline_max_ascent_speed">Max Ascent speed</string>
    <string name="workout_values_headline_max_descent_speed">Max Descent speed</string>
    <string name="workout_values_headline_avg_distance_per_stroke">Avg Distance per stroke</string>
    <string name="workout_values_headline_fatConsumption">Fat consumption</string>
    <string name="workout_values_headline_carbohydrateConsumption">Carbohydrate consumption</string>
    <string name="workout_values_headline_avgGroundContactTime">Avg. ground contact time</string>
    <string name="workout_values_headline_avgVerticalOscillation">Avg. vertical oscillation</string>
    <string name="workout_values_headline_avgGroundContactBalance">Avg. ground contact balance</string>
    <string name="workout_values_headline_skips_per_min">Skips per min</string>
    <string name="workout_values_headline_max_skips_per_min">Max. skips per min</string>
    <string name="workout_values_headline_rounds">Rounds</string>
    <string name="workout_values_headline_avg_skip_per_round">Avg. skips per round</string>
    <string name="workout_values_headline_skip_per_round">Skips per round</string>
    <string name="workout_values_headline_max_consecutive_skips">Max. consecutive skips</string>
    <string name="workout_values_headline_avg_rowing_pace">Pace</string>
    <string name="speed_capital">SPEED</string>
    <string name="speed_knots_capital">SPEED KNOTS</string>
    <string name="speed">Speed</string>
    <string name="pace_capital">PACE</string>
    <string name="heart_rate_capital">HEART RATE</string>
    <string name="cadence_capital">CADENCE</string>
    <string name="max_depth_with_value_and_unit">Max depth %1$s %2$s</string>
    <string name="max_bottom_temperature_with_value_and_unit">Bottom temp. %1$d %2$s</string>
    <plurals name="tank_pressure_delta_with_gas_values">
        <item quantity="one">Used pressure %1$s</item>
        <item quantity="few">Used pressure: %1$s</item>
        <item quantity="many">Used pressure: %1$s</item>
        <item quantity="other">Used pressure: %1$s</item>
    </plurals>
    <string name="gas_pressure_with_value_unit_and_gas_name">%1$d %2$s (%3$s)</string>

    <string name="all_altitude">Altitude</string>
    <string name="all_cadence">Cadence</string>
    <string name="all_epoc">EPOC</string>
    <string name="all_temperature">Temperature</string>
    <string name="all_power">Power</string>
    <string name="all_sea_level_pressure">Sea level pressure</string>
    <string name="all_bike_cadence">Bike cadence</string>
    <string name="swim_stroke_rate">Stroke rate</string>
    <string name="all_swim_pace">Swim pace</string>
    <string name="all_swolf">SWOLF</string>
    <string name="all_speed_knots">Speed knots</string>
    <string name="all_depth">Depth</string>
    <string name="all_heart_rate">Heart rate</string>
    <string name="all_vertical_speed">Vertical speed</string>
    <string name="all_ascent">Ascent</string>
    <string name="all_descent">Descent</string>
    <string name="all_gas_consumption">Gas consumption</string>
    <string name="all_tank_pressure">Tank pressure</string>
    <string name="zone_sense" translatable="false">ZoneSense</string>
    <string name="aerobic_hr_thresholds">Aerobic HR thresholds</string>
    <string name="aerobic_power_thresholds">Aerobic power thresholds</string>
    <string name="hr_list_in_three_mins">Recovery HR</string>
    <string name="all_vertical_oscillation">Vertical oscillation</string>
    <string name="all_ground_contact_time">Ground contact time</string>
    <string name="duration_lap">Duration, per lap</string>
    <string name="all_none">None</string>

    <string name="lap_capital">LAP</string>
    <string name="lap_distance_capital">LAP DISTANCE</string>
    <string name="lap_duration_capital">LAP TIME</string>

    <string name="bpm_capital">BPM</string>
    <string name="avg_bpm_capital">AVG BPM</string>
    <string name="rpm_capital">RPM</string>
    <string name="avg_capital">AVG</string>
    <string name="max_capital">MAX</string>

    <!--
        Names of activities should be according to the SIM
        https://bitbucket.org/suunto/suunto-information-model/src/master/Specifications/Activities/Activities.json
        Exception: Crossfit is Cross training
    -->
    <string name="running">Running</string>
    <string name="walking">Walking</string>
    <string name="cycling">Cycling</string>
    <string name="mountain_biking">Mountain biking</string>
    <string name="hiking">Hiking</string>
    <string name="roller_skating">Roller skating</string>
    <string name="cross_country_skiing">Cross-country skiing</string>
    <string name="downhill_skiing">Alpine skiing</string>
    <string name="paddling">Paddling</string>
    <string name="rowing">Rowing</string>
    <string name="golf">Golf</string>
    <string name="swimming">Pool swimming</string>
    <string name="open_water_swimming">Open water swimming</string>
    <string name="trailrunning">Trail running</string>
    <string name="ballgames">Ball games</string>
    <string name="gym">Weight training</string>
    <string name="nordicwalking">Nordic walking</string>
    <string name="horsebackriding">Horseback riding</string>
    <string name="motorsports">Motorsports</string>
    <string name="skateboarding">Skateboarding</string>
    <string name="watersports">Water sports</string>
    <string name="climbing">Climbing</string>
    <string name="snowboarding">Snowboarding</string>
    <string name="skitouring">Ski touring</string>
    <string name="fitnessclass">Fitness class</string>
    <string name="soccer">Soccer / football</string>
    <string name="indoor">Indoor training</string>
    <string name="other1">Other1</string>
    <string name="other2">Other2</string>
    <string name="other3">Other3</string>
    <string name="other4">Other4</string>
    <string name="other5">Other5</string>
    <string name="other6">Unspecified sport</string>
    <string name="dancing">Dancing</string>
    <string name="snow_shoeing">Snow shoeing</string>
    <string name="frisbee_golf">Frisbee</string>
    <string name="futsal">Futsal</string>
    <string name="american_football">American football</string>
    <string name="badminton">Badminton</string>
    <string name="baseball">Baseball</string>
    <string name="basketball">Basketball</string>
    <string name="bowling">Bowling</string>
    <string name="cricket">Cricket</string>
    <string name="floorball">Floorball</string>
    <string name="handball">Handball</string>
    <string name="outdoor_gym">Outdoor gym</string>
    <string name="parkour">Parkour</string>
    <string name="racquet_ball">Racquet ball</string>
    <string name="rugby">Rugby</string>
    <string name="softball">Softball</string>
    <string name="squash">Squash</string>
    <string name="table_tennis">Table tennis</string>
    <string name="tennis">Tennis</string>
    <string name="volleyball">Volleyball</string>
    <string name="ice_skating">Ice skating</string>
    <string name="ice_hockey">Ice hockey</string>
    <string name="yoga">Yoga / pilates</string>
    <string name="indoor_cycling">Indoor cycling</string>
    <string name="treadmill">Treadmill</string>
    <string name="crossfit">Cross training</string>
    <string name="cross_trainer">Crosstrainer</string>
    <string name="roller_skiing">Roller skiing</string>
    <string name="indoor_rowing">Indoor rowing</string>
    <string name="strecthing">Stretching</string>
    <string name="orienteering">Orienteering</string>
    <string name="combat_sport">Martial arts</string>
    <string name="kettlebell">Kettlebell</string>
    <string name="sup">Standup paddling</string>
    <string name="track_and_field">Track and field</string>
    <string name="multisport">Multisport</string>
    <string name="aerobics">Aerobics</string>
    <string name="trekking">Trekking</string>
    <string name="sailing">Sailing</string>
    <string name="kayaking">Kayaking</string>
    <string name="circuit_training">Circuit training</string>
    <string name="triathlon">Triathlon</string>
    <string name="padel">Padel</string>
    <string name="cheerleading">Cheerleading</string>
    <string name="boxing">Boxing</string>
    <string name="scuba_diving">Scuba diving</string>
    <string name="free_diving">Freediving</string>
    <string name="obstacle_race">Obstacle racing</string>
    <string name="adventure_racing">Adventure racing</string>
    <string name="gymnastics">Gymnastics</string>
    <string name="canoeing">Canoeing</string>
    <string name="mountaineering">Mountaineering</string>
    <string name="telemark">Telemark skiing</string>
    <string name="windsurfing">Windsurfing</string>
    <string name="surfing">Surfing</string>
    <string name="kitesurfing_kiting">Kitesurfing / kiting</string>
    <string name="paragliding">Paragliding</string>
    <string name="fishing">Fishing</string>
    <string name="hunting">Hunting</string>
    <string name="snorkeling">Snorkeling</string>
    <string name="aquathlon">Aquathlon</string>
    <string name="swimrun">Swimrun</string>
    <string name="duathlon">Duathlon</string>
    <string name="gravel_cycling">Gravel cycling</string>
    <string name="mermaiding">Mermaiding</string>
    <string name="jump_rope">Jump rope</string>
    <string name="trackrunning">Track running</string>
    <string name="calisthenics">Calisthenics</string>
    <string name="e_biking">E-Biking</string>
    <string name="e_mtb">E-MTB</string>
    <string name="backcountry_skiing">Backcountry skiing</string>
    <string name="wheelchairing">Wheelchairing</string>
    <string name="handcycling">Handcycling</string>
    <string name="splitboarding">Splitboarding</string>
    <string name="biathlon">Biathlon</string>
    <string name="meditation">Meditation</string>
    <string name="field_hockey">Field hockey</string>
    <string name="cyclocross">Cyclocross</string>
    <string name="vertical_running">Vertical Running</string>
    <string name="ski_mountaineering">Ski mountaineering</string>
    <string name="skate_skiing">Skate skiing</string>
    <string name="classic_skiing">Classic skiing</string>
    <string name="pilates">Pilates</string>
    <string name="chores">Chores</string>
    <string name="new_yoga">Yoga</string>

    <string name="all_trails">All trails</string>
    <string name="all_walking">All walking</string>
    <string name="all_downhill">Downhill</string>
    <string name="all_swimming">Swimming</string>
    <string name="all_paddling">All paddling</string>
    <string name="all_roller_sports">Roller skiing and skating</string>
    <string name="all_surf_and_beach">Surf and beach</string>

    <string name="fast_cycling">Fast cycling</string>

    <!-- Activity groups -->
    <string name="martial_arts">All martial arts</string>
    <string name="racket_sports">All racket sports</string>
    <string name="ride">All cycling</string>
    <string name="rowing_group">All rowing</string>
    <string name="run_group">All running</string>
    <string name="strength">All strength training</string>
    <string name="swim_group">All swimming</string>
    <string name="team_sports">All team sports</string>
    <string name="walking_group">All walking</string>

    <!-- Measurement Units -->
    <string name="km">km</string>
    <string name="mile">mi</string>
    <string name="km_h">km/h</string>
    <string name="m_min">m/min</string>
    <string name="mph">mph</string>
    <string name="ft_min">ft/min</string>
    <string name="per_km">/km</string>
    <string name="per_mi">/mi</string>
    <string name="per_100_m">/100m</string>
    <string name="per_100_yard">/100yd</string>
    <string name="seconds">s</string>
    <string name="minute">min</string>
    <string name="hour">h</string>
    <string name="hours">Hours</string>
    <string name="days">days</string>
    <string name="meters">m</string>
    <string name="feet">ft</string>
    <string name="per_minute">/min</string>
    <string name="minute_km">min/km</string>
    <string name="pounds">lbs</string>
    <string name="kilograms">kg</string>
    <string name="bpm">bpm</string>
    <string name="kcal">kcal</string>
    <string name="watt">W</string>
    <string name="vo2maxUnit">ml/kg/min</string>
    <string name="bar">bar</string>
    <string name="psi">psi</string>
    <string name="sec">sec</string>
    <string name="liters_per_minute">l/min</string>
    <string name="cubic_feet_per_minute">ft³/min</string>
    <string name="cubic_meter_per_second">m³/s</string>
    <string name="kilometers">Kilometers</string>
    <string name="miles">Miles</string>
    <string name="knots">knots</string>
    <string name="nautical_mile">nmi</string>
    <string name="meters_long">meters</string>
    <string name="feet_long">feet</string>
    <string name="ms">ms</string>
    <string name="liters">l</string>
    <string name="cubic_meter">m³</string>
    <string name="cm">cm</string>
    <string name="m_s">m/s</string>
    <string name="inch">inch</string>
    <string name="ft_s">ft/s</string>
    <string name="grams">grams</string>
    <string name="ounce">ounce</string>
    <string name="round">/round</string>
    <string name="min_sec">min\'sec</string>
    <!-- Measurement Units -->

    <!-- Suunto Information Model units -->
    <string name="TXT_M">m</string>
    <string name="TXT_M_SEC">m/s</string>
    <string name="TXT_KM">km</string>
    <string name="TXT_FEET">ft</string>
    <string name="TXT_KFT_POSTFIX">kft</string>
    <string name="TXT_MI">mi</string>
    <string name="TXT_NMI">nmi</string>
    <string name="TXT_KMH">km/h</string>
    <string name="TXT_MPH">mph</string>
    <string name="TXT_KN">kn</string>
    <string name="TXT_W">W</string>
    <string name="TXT_J_JOULE">J</string>
    <string name="TXT_BPM">bpm</string>
    <string name="TXT_KCAL">kcal</string>
    <string name="TXT_EPOC_ML_KG">ml/kg</string>
    <string name="TXT_ML_KG_MIN">ml/kg/min</string>
    <string name="TXT_RPM">rpm</string>
    <string name="TXT_PER_MIN">/min</string>
    <string name="TXT_M_MIN">m/min</string>
    <string name="TXT_FT_MIN">ft/min</string>
    <string name="TXT_HPA_POSTFIX">hPa</string>
    <string name="TXT_KPA_POSTFIX">kPa</string>
    <string name="TXT_PA_POSTFIX">Pa</string>
    <string name="TXT_INHG_POSTFIX">inHg</string>
    <string name="TXT_CELSIUS">°C</string>
    <string name="TXT_FAHRENHEIT">°F</string>
    <string name="TXT_H">h</string>
    <string name="TXT_S_SECONDS">s</string>
    <string name="TXT_MS">ms</string>
    <string name="TXT_YD">yd</string>
    <string name="TXT_PER_100M">/100m</string>
    <string name="TXT_PER_100YD">/100yd</string>
    <string name="TXT_PER_500M">/500m</string>
    <string name="TXT_PER_KM">/km</string>
    <string name="TXT_PER_MILE">/mi</string>
    <string name="TXT_DEGREE_SYMBOL">°</string>
    <string name="TXT_KG_POSTFIX">kg</string>
    <string name="TXT_LB_POSTFIX">lb</string>
    <string name="TXT_CM">cm</string>
    <string name="TXT_IN">in</string>
    <string name="TXT_M_HOUR">m/hour</string>
    <string name="TXT_FT_HOUR">ft/h</string>
    <string name="TXT_KNM">kN/m</string>
    <string name="TXT_MILS">Mils</string>
    <string name="TXT_K">K</string>
    <string name="TXT_HZ">Hz</string>
    <string name="TXT_RAD">rad</string>
    <string name="TXT_G_POSTFIX">g</string>
    <string name="TXT_OZ_POSTFIX">oz</string>
    <string name="ddfa_index" translatable="false">DDFA Index</string>
    <!-- Suunto Information Model units -->

    <!-- Offline region units -->
    <string name="square_kilometers">km²</string>
    <string name="square_miles">mi²</string>
    <!-- END Offline region units -->

    <!-- Laps data categories -->
    <string name="summary_item_category_cadence">Cadence</string>
    <string name="summary_item_category_distance">Distance</string>
    <string name="summary_item_category_dive">Dive</string>
    <string name="summary_item_category_duration">Duration</string>
    <string name="summary_item_category_heartRate">Heart rate</string>
    <string name="summary_item_category_physiology">Physiology</string>
    <string name="summary_item_category_power">Power</string>
    <string name="summary_item_category_speed_and_pace">Speed and pace</string>
    <string name="summary_item_category_vertical">Vertical</string>
    <string name="summary_item_category_breath">Breath</string>
    <string name="summary_item_category_head_angle">Head angle</string>
    <string name="summary_item_category_other">Other</string>
    <string name="suunto_plus_category">SuuntoPlus™</string>
    <!-- Laps data categories -->

    <!-- Laps data items -->
    <string name="summary_item_title_max_altitude">Altitude (MAX)</string>
    <string name="summary_item_title_min_altitude">Altitude (MIN)</string>
    <string name="summary_item_title_ascent">Ascent</string>
    <string name="summary_item_title_avg_cadence">Cadence (AVG)</string>
    <string name="summary_item_title_cumulated_distance">Cumulated distance</string>
    <string name="summary_item_title_cumulated_duration">Cumulated duration</string>
    <string name="summary_item_title_descent">Descent</string>
    <string name="summary_item_title_descent_downhill">Descent downhill</string>
    <string name="summary_item_title_distance">Distance</string>
    <string name="summary_item_title_nautical_distance">Nautical distance</string>
    <string name="summary_item_title_distance_downhill">Distance downhill</string>
    <string name="summary_item_title_duration">Duration</string>
    <string name="summary_item_title_duration_downhill">Duration downhill</string>
    <string name="summary_item_title_energy">Energy</string>
    <string name="summary_item_title_avg_hr">Heart rate (AVG)</string>
    <string name="summary_item_title_min_hr">Heart rate (MIN)</string>
    <string name="summary_item_title_max_hr">Heart rate (MAX)</string>
    <string name="summary_item_title_avg_pace">Pace (AVG)</string>
    <string name="summary_item_title_max_pace">Pace (MAX)</string>
    <string name="summary_item_title_avg_power">Power (AVG)</string>
    <string name="summary_item_title_max_power">Power (MAX)</string>
    <string name="summary_item_title_avg_speed">Speed (AVG)</string>
    <string name="summary_item_title_avg_speed_downhill">Speed downhill (AVG)</string>
    <string name="summary_item_title_max_speed">Speed (MAX)</string>
    <string name="summary_item_title_max_speed_downhill">Speed downhill (MAX)</string>
    <string name="summary_item_title_nautical_avg_speed">Nautical Speed (AVG)</string>
    <string name="summary_item_title_nautical_max_speed">Nautical Speed (MAX)</string>
    <string name="summary_item_title_avg_swim_stroke_rate">Stroke rate (AVG)</string>
    <string name="summary_item_title_avg_swim_pace">Swim pace (AVG)</string>
    <string name="summary_item_title_swim_style">Swim style</string>
    <string name="summary_item_title_avg_temperature">Temperature (AVG)</string>
    <string name="summary_item_title_max_temperature">Temperature (MAX)</string>
    <string name="summary_item_title_interval_type">Interval type</string>
    <string name="summary_item_title_avg_vertical_speed">Vertical speed (AVG)</string>
    <string name="summary_item_title_avg_sea_level_pressure">Sea level pressure (AVG)</string>
    <string name="summary_item_title_avg_swolf">Swolf (AVG)</string>
    <string name="summary_item_title_max_depth">Depth (MAX)</string>
    <string name="summary_item_title_min_depth">Depth (MIN)</string>
    <string name="summary_item_title_dive_time">Dive time</string>
    <string name="summary_item_title_dive_time_max">Dive time max</string>
    <string name="summary_item_title_dive_surface_time">Surface time</string>
    <string name="summary_item_title_dive_recovery_time">Dive recovery time</string>
    <string name="summary_item_title_skip_count">Skips</string>
    <string name="summary_item_title_rowing_stroke_count">Strokes</string>
    <string name="summary_item_title_revolution_count">Revolutions</string>
    <string name="summary_item_title_avg_stride">Stride (AVG)</string>
    <string name="summary_item_title_fat_consumption">Fat consumption</string>
    <string name="summary_item_title_carbohydrate_consumption">Carbohydrate consumption</string>
    <string name="summary_item_title_avg_ground_contact_time">Ground contact time (AVG)</string>
    <string name="summary_item_title_avg_vertical_oscillation">Vertical oscillation (AVG)</string>
    <string name="summary_item_title_avg_ground_contact_balance">Ground contact balance (AVG)</string>
    <string name="suuntoplus_item_title_avg">%s (AVG)</string>

    <!-- todo fix TSS labels with actual ones -->
    <string name="summary_item_np">Normalized Power®</string>
    <string name="summary_item_ngp">Normalized Graded Pace™</string>
    <string name="summary_item_agap">Average Grade Adjusted Pace</string>
    <string name="summary_item_if">Intensity Factor</string>
    <string name="summary_item_tss">Training Stress Score®</string>
    <string name="summary_item_tss_title">PROGRESS</string>
    <!-- Laps data items -->

    <!-- Laps tables -->
    <string name="laps_show_markers_on_map">Show lap markers on route</string>
    <string name="laps_table_type_manual">Manual</string>
    <string name="laps_table_type_downhill">Downhill</string>
    <string name="laps_table_type_interval">Interval</string>
    <string name="laps_table_type_dive">Dive</string>
    <string name="laps_table_type_autolap_distance_format">%s %s</string>
    <string name="laps_table_type_autolap_duration">%s</string>
    <string name="laps_table_type_autolap_fallback">Autolap</string>
    <string name="laps_table_type_manual_abbr" translatable="false">M</string>
    <!-- Laps tables -->

    <!-- Swim styles -->
    <string name="swimstyle_other">Other</string>
    <string name="swimstyle_freestyle">Free</string>
    <string name="swimstyle_butterfly">Butterfly</string>
    <string name="swimstyle_breaststroke">Breast</string>
    <string name="swimstyle_backstroke">Back</string>
    <string name="swimstyle_drill">Drill</string>
    <!-- Swim styles -->

    <!-- Dive event localizations -->
    <!-- Dive -->
    <string name="event_bookmark">Bookmark saved by user</string>

    <!-- Dive -->
    <string name="event_notify_depth">Depth alarm</string>
    <!-- Dive -->
    <string name="event_notify_depth_description">User set depth level reached.</string>
    <!-- Dive -->
    <string name="event_notify_surface_time">Surfaced / Dive ended</string>
    <!-- Dive -->
    <string name="event_notify_surface_time_description">Dive time calculation stopped.</string>
    <!-- Dive -->
    <string name="event_notify_deco">Decompression dive</string>
    <!-- Dive -->
    <string name="event_notify_deco_description">Dive turned to decompression dive and mandatory decompression stops needed during ascent.</string>
    <!-- Dive -->
    <string name="event_notify_deco_window">Decompression stop started</string>
    <!-- Dive -->
    <string name="event_notify_deco_window_description">Decompression stop calculation started.</string>
    <!-- Dive -->
    <string name="event_notify_setpoint_switch">Setpoint switched</string>
    <!-- Dive -->
    <string name="event_notify_setpoint_switch_description">Automatic setpoint switch.</string>
    <!-- Dive -->
    <string name="event_notify_safety_stop_broken">Voluntary safety stop notify</string>
    <!-- Dive -->
    <string name="event_notify_safety_stop_broken_description">Voluntary safety stop omitted.</string>
    <!-- Dive -->
    <string name="event_notify_safety_stop">Safety stop started</string>
    <!-- Dive -->
    <string name="event_notify_safety_stop_description">Safety stop window entered and timer started.</string>
    <!-- Dive -->
    <string name="event_notify_deep_stop">Deepstop started</string>
    <!-- Dive -->
    <string name="event_notify_deep_stop_description">Deepstop window entered and timer started.</string>
    <!-- Dive -->
    <string name="event_notify_deep_stop_ahead">Deepstops turned on</string>
    <!-- Dive -->
    <string name="event_notify_deep_stop_ahead_description">Deepstops are turned on for ascent.</string>
    <!-- Dive -->
    <string name="event_notify_diluent_hypoxia">Diluent low pO₂</string>
    <!-- Dive -->
    <string name="event_notify_diluent_hypoxia_description">Diluent oxygen partial pressure below safe level &lt; 0.18 bar.</string>
    <!-- Dive -->
    <string name="event_notify_air_time">Gas time alarm</string>
    <!-- Dive -->
    <string name="event_notify_air_time_description">User set gas time alarm.</string>
    <!-- Dive -->
    <string name="event_notify_tank_pressure">Tank pressure alarm</string>
    <!-- Dive -->
    <string name="event_notify_tank_pressure_description">User set tank pressure alarm %s.</string>
    <!-- Dive -->
    <string name="event_notify_missed_deco_ack">Decompression stop omit approved by user</string>
    <!-- Dive -->
    <string name="event_notify_missed_deco_ack_description">User has approved decompression stop was omitted and decided to continue the dive.</string>
    <!-- Dive -->
    <string name="event_notify_dive_time">Dive time alarm</string>
    <!-- Dive -->
    <string name="event_notify_dive_time_description">User set dive time alarm.</string>
    <!-- Dive -->
    <string name="event_notify_user_ndl">NDL alarm</string>
    <!-- Dive -->
    <string name="event_notify_user_ndl_description">User set NDL alarm reached.</string>

    <!-- Dive -->
    <string name="event_warning_icd_penalty">Isobaric counter diffusion (ICD) warning</string>
    <!-- Dive -->
    <string name="event_warning_icd_penalty_description">Isobaric counterdiffusion (ICD) occurs when different inert gases (such as nitrogen and helium) diffuse in different directions during a dive.</string>
    <!-- Dive -->
    <string name="event_warning_deep_stop_penalty">Deepstop omitted</string>
    <!-- Dive -->
    <string name="event_warning_deep_stop_penalty_description">Recommended deepstop omitted.</string>
    <!-- Dive -->
    <string name="event_warning_mandatory_safety_stop">Mandatory safety stop warning</string>
    <!-- Dive -->
    <string name="event_warning_mandatory_safety_stop_description">Safety stop changed to mandatory because ascent speed violation has been more than 10 m/min (32.8 ft/min) for more than 5 seconds.</string>
    <!-- Dive -->
    <string name="event_warning_cns80" formatted="false">CNS 80% warning</string>
    <!-- Dive -->
    <string name="event_warning_cns80_description" formatted="false">Central nervous system toxicity level reached  80% of the recommended limit.</string>
    <!-- Dive -->
    <string name="event_warning_cns100" formatted="false">CNS 100% warning</string>
    <!-- Dive -->
    <string name="event_warning_cns100_description" formatted="false">Central nervous system toxicity level reached 100% of recommended limit.</string>
    <!-- Dive -->
    <string name="event_warning_otu250">OTU 250 warning</string>
    <!-- Dive -->
    <string name="event_warning_otu250_description" formatted="false">Approximately 80% of recommended daily limit for Oxygen Tolerance Unit reached.</string>
    <!-- Dive -->
    <string name="event_warning_otu300">OTU 300 warning</string>
    <!-- Dive -->
    <string name="event_warning_otu300_description" formatted="false">Approximately 100% of recommended daily limit for Oxygen Tolerance Unit reached.</string>
    <!-- Dive -->
    <string name="event_warning_air_time">Gas time warning</string>
    <!-- Dive -->
    <string name="event_warning_air_time_description">Tank pressure below 35 bar / ~510 psi, which means gas time 0 min.</string>
    <!-- Dive -->
    <string name="event_warning_max_depth">Max depth warning</string>
    <!-- Dive -->
    <string name="event_warning_max_depth_description">User set max depth limit reached.</string>
    <!-- Dive -->
    <string name="event_warning_tank_pressure">Tank pressure warning</string>
    <!-- Dive -->
    <string name="event_warning_tank_pressure_description">This warning is mandatory tank pressure warning at 50 bar / ~725 psi.</string>
    <!-- Dive -->
    <string name="event_warning_po2_high">High pO₂ warning</string>
    <!-- Dive -->
    <string name="event_warning_po2_high_description">Oxygen partial pressure has exceeded user set limit.</string>
    <!-- Dive -->
    <string name="event_warning_deco_broken">Decompression stop omitted</string>
    <!-- Dive -->
    <string name="event_warning_deco_broken_description">After decompression stop omitted dive is outside of recommended algorithm profile.</string>
    <!-- Dive -->
    <string name="event_warning_mini_lock">Decompression stop omitted in previous dive</string>
    <!-- Dive -->
    <string name="event_warning_mini_lock_description">Dive algorithm calculation is outside of recommended algorithm profile.</string>
    <!-- Dive -->
    <string name="event_warning_no_deco_time">Low NDL</string>
    <!-- Dive -->
    <string name="event_warning_no_deco_time_description">The remaining NDL is 5 min.</string>

    <!-- Dive -->
    <string name="event_alarm_mandatory_safety_stop_broken">Mandatory safety stop alarm</string>
    <!-- Dive -->
    <string name="event_alarm_mandatory_safety_stop_broken_description">Mandatory safety stop omitted.</string>
    <!-- Dive -->
    <string name="event_alarm_ascent_speed">Ascent speed alarm</string>
    <!-- Dive -->
    <string name="event_alarm_ascent_speed_description">Ascent speed over safe 10 m/min (32.8 ft/min) limit.</string>
    <!-- Dive -->
    <string name="event_alarm_diluent_hyperoxia">Diluent high pO₂ alarm</string>
    <!-- Dive -->
    <string name="event_alarm_diluent_hyperoxia_description">Diluent oxygen partial pressure exceeds safe level &gt; 1.6 bar.</string>
    <!-- Dive -->
    <string name="event_alarm_ceiling_broken">Decompression ceiling broken alarm</string>
    <!-- Dive -->
    <string name="event_alarm_ceiling_broken_description">Decompression ceiling broken &gt; 0.6m / &gt; 2 ft and decompression calculation paused.</string>
    <!-- Dive -->
    <string name="event_alarm_po2_high">High pO₂ alarm</string>
    <!-- Dive -->
    <string name="event_alarm_po2_high_description">Partial pressure of oxygen exceeds safe level &gt; 1.6 bar.</string>
    <!-- Dive -->
    <string name="event_alarm_po2_low">Low pO₂ alarm</string>
    <!-- Dive -->
    <string name="event_alarm_po2_low_description">Partial pressure of oxygen below safe level &lt; 0.18 bar.</string>

    <!-- Dive -->
    <string name="event_error_ceiling_broken">Dive algorithm locked</string>
    <!-- Dive -->
    <string name="event_error_ceiling_broken_description">Dive algorithm is locked because decompression stops omitted for longer than 3 minutes.</string>

    <!-- Dive -->
    <string name="event_dive_active">Dive started</string>
    <!-- Dive -->
    <string name="event_dive_active_no_water_contact_description">Dive time calculation started without water contact on.</string>
    <!-- Dive -->
    <string name="event_dive_active_water_contact_description">Dive time calculation started.</string>

    <!-- Dive -->
    <string name="event_gas_switch">Gas switch</string>
    <!-- Dive -->
    <string name="event_gas_switch_to_gas">Gas switched by user\n→ %s</string>
    <!-- Dive -->
    <string name="event_gas_switch_from_to_gas">Gas switched by user\n%s → %s</string>

    <!-- Dive -->
    <string name="event_setpoint_switch">Setpoint switched</string>
    <!-- Dive -->
    <string name="event_setpoint_switch_description_manual">Manual setpoint switch.</string>
    <!-- Dive -->
    <string name="event_setpoint_switch_description_custom">Setpoint switched to custom.</string>
    <!-- Dive -->
    <string name="event_setpoint_switch_high">High setpoint</string>
    <!-- Dive -->
    <string name="event_setpoint_switch_low">Low setpoint</string>
    <!-- Dive -->
    <string name="event_setpoint_switch_custom">Custom setpoint</string>

    <!-- Dive -->
    <string name="event_gas_edit_insert">Inserted gas number %s</string>
    <!-- Dive -->
    <string name="event_gas_edit_remove">Removed gas number %s</string>

    <!-- Dive -->
    <string name="event_dive_timer_started">Timer started</string>
    <!-- Dive -->
    <string name="event_dive_timer_started_description">User has started timer.</string>
    <!-- Dive -->
    <string name="event_dive_timer_stopped">Timer stopped</string>
    <!-- Dive -->
    <string name="event_dive_timer_stopped_description">User has stopped timer.</string>

    <!-- Dive -->
    <string name="tissue_reset">Tissue reset done before dive</string>
    <!-- Dive -->
    <string name="tissue_reset_description">Tissue reset done by user before the dive.</string>

    <!-- Dive -->
    <string name="event_closed_circuit_mode">Closed circuit mode</string>
    <!-- Dive -->
    <string name="event_closed_circuit_mode_activated">Activated</string>
    <!-- Dive -->
    <string name="event_closed_circuit_mode_deactivated">Deactivated</string>

    <!-- Dive -->
    <string name="event_out_of_algorithm_model_generic_title">Deviation</string>
    <string name="event_out_of_algorithm_model_generic_description">Dive algorithm calculation is outside of recommended algorithm profile.</string>
    <string name="event_out_of_algorithm_model_ceiling_broken_description">After decompression stop was omitted, dive is outside of recommended algorithm profile.</string>
    <!-- END dive event localizations -->

    <!-- Notification channel names -->
    <string name="notification_channel_activity_recording">Activity recording</string>
    <string name="all_filter_tag">All</string>
    <string name="me_filter_tag">Me</string>
    <string name="suunto_filter_tag">Suunto</string>
    <string name="following_filter_tag">Following</string>

    <string name="no_search_results">No results found</string>

    <string name="not_sure">Not sure</string>
    <string name="cycle_length">Cycle length</string>
    <string name="period_length">Period length</string>
    <plurals name="value_days">
        <item quantity="one">%d day</item>
        <item quantity="few">%d days</item>
        <item quantity="many">%d days</item>
        <item quantity="other">%d days</item>
    </plurals>
    <plurals name="unit_days">
        <item quantity="one">day</item>
        <item quantity="few">days</item>
        <item quantity="many">days</item>
        <item quantity="other">days</item>
    </plurals>

    <string name="training_hub_impact_recovery">Recovery</string>
    <string name="training_hub_impact_aerobic">Aerobic</string>
    <string name="training_hub_impact_cardio_long_aerobic">Long Aerobic</string>
    <string name="training_hub_impact_cardio_vo2_max">VO₂max</string>
    <string name="training_hub_impact_cardio_anaerobic_hard">Anaerobic - hard</string>
    <string name="training_hub_impact_cardio_aerobic_anaerobic">Aerobic/Anaerobic</string>
    <string name="training_hub_impact_cardio_anaerobic">Anaerobic</string>
    <string name="training_hub_impact_cardio_heavy_aerobic">Heavy Aerobic</string>
    <string name="training_hub_impact_muscular_speed_and_agility">Speed &amp; Agility</string>
    <string name="training_hub_impact_muscular_speed_and_strength">Speed &amp; Strength</string>
    <string name="training_hub_impact_muscular_flexibility">Flexibility</string>
    <string name="training_hub_impact_muscular_strength">Strength</string>

    <string name="activity_type_all">All</string>
</resources>
