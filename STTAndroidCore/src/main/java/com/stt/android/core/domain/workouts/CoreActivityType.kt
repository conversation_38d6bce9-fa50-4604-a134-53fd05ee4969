package com.stt.android.core.domain.workouts

import android.content.res.Resources
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.core.R

enum class CoreActivityType(
    override val id: Int,
    @StringRes override val nameRes: Int,
    @DrawableRes override val icon: Int,
    @ColorRes override val color: Int,
    val hasDistance: Boolean = true,
    val hasAscent: <PERSON><PERSON>an = true,
) : CoreActivityGrouping {

    WALKING(
        id = 0,
        nameRes = R.string.walking,
        icon = R.drawable.ic_activity_walking,
        color = R.color.walking,
    ),
    RUNNING(
        id = 1,
        nameRes = R.string.running,
        icon = R.drawable.ic_activity_running,
        color = R.color.running,
    ),
    CYCLING(
        id = 2,
        nameRes = R.string.cycling,
        icon = R.drawable.ic_activity_cycling,
        color = R.color.cycling,
    ),
    CROSS_COUNTRY_SKIING(
        id = 3,
        nameRes = R.string.cross_country_skiing,
        icon = R.drawable.ic_activity_crosscountryskiing,
        color = R.color.cross_country_skiing,
    ),
    OTHER_1(
        id = 4,
        nameRes = R.string.other1,
        icon = R.drawable.ic_activity_other_1,
        color = R.color.other,
    ),
    OTHER_2(
        id = 5,
        nameRes = R.string.other2,
        icon = R.drawable.ic_activity_other_2,
        color = R.color.other,
    ),
    OTHER_3(
        id = 6,
        nameRes = R.string.other3,
        icon = R.drawable.ic_activity_other_3,
        color = R.color.other,
    ),
    OTHER_4(
        id = 7,
        nameRes = R.string.other4,
        icon = R.drawable.ic_activity_other_4,
        color = R.color.other,
    ),
    OTHER_5(
        id = 8,
        nameRes = R.string.other5,
        icon = R.drawable.ic_activity_other_5,
        color = R.color.other,
    ),
    OTHER_6(
        id = 9,
        nameRes = R.string.other6,
        icon = R.drawable.ic_activity_other_6,
        color = R.color.other,
    ),
    MOUNTAIN_BIKING(
        id = 10,
        nameRes = R.string.mountain_biking,
        icon = R.drawable.ic_activity_mountainbiking,
        color = R.color.mountain_biking,
    ),
    HIKING(
        id = 11,
        nameRes = R.string.hiking,
        icon = R.drawable.ic_activity_hiking,
        color = R.color.hiking,
    ),
    ROLLER_SKATING(
        id = 12,
        nameRes = R.string.roller_skating,
        icon = R.drawable.ic_activity_rollerskating,
        color = R.color.roller_skating,
    ),
    DOWNHILL_SKIING(
        id = 13,
        nameRes = R.string.downhill_skiing,
        icon = R.drawable.ic_activity_downhillskiing,
        color = R.color.downhill_skiing,
    ),
    PADDLING(
        id = 14,
        nameRes = R.string.paddling,
        icon = R.drawable.ic_activity_paddling,
        color = R.color.paddling,
        hasAscent = false
    ),
    ROWING(
        id = 15,
        nameRes = R.string.rowing,
        icon = R.drawable.ic_activity_rowing,
        color = R.color.rowing,
        hasAscent = false
    ),
    GOLF(
        id = 16,
        nameRes = R.string.golf,
        icon = R.drawable.ic_activity_golf,
        color = R.color.golf,
        hasAscent = false
    ),
    INDOOR(
        id = 17,
        nameRes = R.string.indoor,
        icon = R.drawable.ic_activity_indoor,
        color = R.color.indoor,
        hasAscent = false
    ),
    PARKOUR(
        id = 18,
        nameRes = R.string.parkour,
        icon = R.drawable.ic_activity_parkour,
        color = R.color.parkour,
        hasDistance = false
    ),
    BALLGAMES(
        id = 19,
        nameRes = R.string.ballgames,
        icon = R.drawable.ic_activity_ballgames,
        color = R.color.ballgames,
        hasAscent = false
    ),
    OUTDOOR_GYM(
        id = 20,
        nameRes = R.string.outdoor_gym,
        icon = R.drawable.ic_activity_outdoor_gym,
        color = R.color.outdoor_gym,
        hasDistance = false,
        hasAscent = false
    ),
    SWIMMING(
        id = 21,
        nameRes = R.string.swimming,
        icon = R.drawable.ic_activity_swimming,
        color = R.color.swimming,
        hasAscent = false
    ),
    TRAIL_RUNNING(
        id = 22,
        nameRes = R.string.trailrunning,
        icon = R.drawable.ic_activity_trailrunning,
        color = R.color.trailrunning,
    ),
    GYM(
        id = 23,
        nameRes = R.string.gym,
        icon = R.drawable.ic_activity_gym,
        color = R.color.gym,
        hasDistance = false,
        hasAscent = false
    ),
    NORDIC_WALKING(
        id = 24,
        nameRes = R.string.nordicwalking,
        icon = R.drawable.ic_activity_nordicwalking,
        color = R.color.nordicwalking,
    ),
    HORSEBACK_RIDING(
        id = 25,
        nameRes = R.string.horsebackriding,
        icon = R.drawable.ic_activity_horseback_riding,
        color = R.color.horsebackriding,
    ),
    MOTOR_SPORTS(
        id = 26,
        nameRes = R.string.motorsports,
        icon = R.drawable.ic_activity_motorsports,
        color = R.color.motorsports,
    ),
    SKATEBOARDING(
        id = 27,
        nameRes = R.string.skateboarding,
        icon = R.drawable.ic_activity_skateboarding,
        color = R.color.skateboarding,
        hasAscent = false,
        hasDistance = false
    ),
    WATER_SPORTS(
        id = 28,
        nameRes = R.string.watersports,
        icon = R.drawable.ic_activity_watersports,
        color = R.color.watersports,
        hasAscent = false
    ),
    CLIMBING(
        id = 29,
        nameRes = R.string.climbing,
        icon = R.drawable.ic_activity_climbing,
        color = R.color.climbing,
        hasDistance = false,
    ),
    SNOWBOARDING(
        id = 30,
        nameRes = R.string.snowboarding,
        icon = R.drawable.ic_activity_snowboarding,
        color = R.color.snowboarding,
        hasAscent = false
    ),
    SKI_TOURING(
        id = 31,
        nameRes = R.string.skitouring,
        icon = R.drawable.ic_activity_skitouring,
        color = R.color.skitouring,
    ),
    FITNESS_CLASS(
        id = 32,
        nameRes = R.string.fitnessclass,
        icon = R.drawable.ic_activity_fitness_class,
        color = R.color.fitnessclass,
        hasDistance = false,
        hasAscent = false
    ),
    SOCCER(
        id = 33,
        nameRes = R.string.soccer,
        icon = R.drawable.ic_activity_soccer,
        color = R.color.soccer,
        hasAscent = false,
        hasDistance = false
    ),
    TENNIS(
        id = 34,
        nameRes = R.string.tennis,
        icon = R.drawable.ic_activity_tennis,
        color = R.color.tennis,
        hasDistance = false,
        hasAscent = false
    ),
    BASKETBALL(
        id = 35,
        nameRes = R.string.basketball,
        icon = R.drawable.ic_activity_basketball,
        color = R.color.basketball,
        hasAscent = false,
        hasDistance = false
    ),
    BADMINTON(
        id = 36,
        nameRes = R.string.badminton,
        icon = R.drawable.ic_activity_badminton,
        color = R.color.badminton,
        hasDistance = false,
        hasAscent = false
    ),
    BASEBALL(
        id = 37,
        nameRes = R.string.baseball,
        icon = R.drawable.ic_activity_baseball,
        color = R.color.baseball,
        hasAscent = false,
        hasDistance = false
    ),
    VOLLEYBALL(
        id = 38,
        nameRes = R.string.volleyball,
        icon = R.drawable.ic_activity_volleyball,
        color = R.color.volleyball,
        hasDistance = false,
        hasAscent = false
    ),
    AMERICAN_FOOTBALL(
        id = 39,
        nameRes = R.string.american_football,
        icon = R.drawable.ic_activity_americanfootball,
        color = R.color.american_football,
        hasAscent = false,
        hasDistance = false
    ),
    TABLE_TENNIS(
        id = 40,
        nameRes = R.string.table_tennis,
        icon = R.drawable.ic_activity_tabletennis,
        color = R.color.table_tennis,
        hasDistance = false,
        hasAscent = false
    ),
    RACQUETBALL(
        id = 41,
        nameRes = R.string.racquet_ball,
        icon = R.drawable.ic_activity_racquetball,
        color = R.color.racquet_ball,
        hasDistance = false,
        hasAscent = false
    ),
    SQUASH(
        id = 42,
        nameRes = R.string.squash,
        icon = R.drawable.ic_activity_squash,
        color = R.color.squash,
        hasDistance = false,
        hasAscent = false
    ),
    FLOORBALL(
        id = 43,
        nameRes = R.string.floorball,
        icon = R.drawable.ic_activity_floorball,
        color = R.color.floorball,
        hasDistance = false,
        hasAscent = false
    ),
    HANDBALL(
        id = 44,
        nameRes = R.string.handball,
        icon = R.drawable.ic_activity_handball,
        color = R.color.handball,
        hasDistance = false,
        hasAscent = false
    ),
    SOFTBALL(
        id = 45,
        nameRes = R.string.softball,
        icon = R.drawable.ic_activity_softball,
        color = R.color.softball,
        hasAscent = false,
        hasDistance = false
    ),
    BOWLING(
        id = 46,
        nameRes = R.string.bowling,
        icon = R.drawable.ic_activity_bowling,
        color = R.color.bowling,
        hasDistance = false,
        hasAscent = false
    ),
    CRICKET(
        id = 47,
        nameRes = R.string.cricket,
        icon = R.drawable.ic_activity_cricket,
        color = R.color.cricket,
        hasAscent = false,
        hasDistance = false
    ),
    RUGBY(
        id = 48,
        nameRes = R.string.rugby,
        icon = R.drawable.ic_activity_rugby,
        color = R.color.rugby,
        hasAscent = false,
        hasDistance = false
    ),
    ICE_SKATING(
        id = 49,
        nameRes = R.string.ice_skating,
        icon = R.drawable.ic_activity_iceskating,
        color = R.color.ice_skating,
        hasAscent = false
    ),
    ICE_HOCKEY(
        id = 50,
        nameRes = R.string.ice_hockey,
        icon = R.drawable.ic_activity_icehockey,
        color = R.color.ice_hockey,
        hasDistance = false,
        hasAscent = false
    ),
    YOGA(
        id = 51,
        nameRes = R.string.yoga,
        icon = R.drawable.ic_activity_yoga,
        color = R.color.yoga,
        hasDistance = false,
        hasAscent = false
    ),
    INDOOR_CYCLING(
        id = 52,
        nameRes = R.string.indoor_cycling,
        icon = R.drawable.ic_activity_indoorcycling,
        color = R.color.indoor_cycling,
    ),
    TREADMILL(
        id = 53,
        nameRes = R.string.treadmill,
        icon = R.drawable.ic_activity_treadmill,
        color = R.color.treadmill,
        hasAscent = false
    ),
    CROSSFIT(
        id = 54,
        nameRes = R.string.crossfit,
        icon = R.drawable.ic_activity_crossfit,
        color = R.color.crossfit,
        hasAscent = false,
        hasDistance = false
    ),
    CROSSTRAINER(
        id = 55,
        nameRes = R.string.cross_trainer,
        icon = R.drawable.ic_activity_crosstrainer,
        color = R.color.cross_trainer,
    ),
    ROLLER_SKIING(
        id = 56,
        nameRes = R.string.roller_skiing,
        icon = R.drawable.ic_activity_rollerskiing,
        color = R.color.roller_skiing,
    ),
    INDOOR_ROWING(
        id = 57,
        nameRes = R.string.indoor_rowing,
        icon = R.drawable.ic_activity_indoorrowing,
        color = R.color.indoor_rowing,
        hasAscent = false
    ),
    STRETCHING(
        id = 58,
        nameRes = R.string.strecthing,
        icon = R.drawable.ic_activity_stretching,
        color = R.color.stretching,
        hasDistance = false,
        hasAscent = false
    ),
    TRACK_AND_FIELD(
        id = 59,
        nameRes = R.string.track_and_field,
        icon = R.drawable.ic_activity_trackandfield,
        color = R.color.track_and_field,
        hasAscent = false
    ),
    ORIENTEERING(
        id = 60,
        nameRes = R.string.orienteering,
        icon = R.drawable.ic_activity_orienteering,
        color = R.color.orienteering,
    ),
    SUP(
        id = 61,
        nameRes = R.string.sup,
        icon = R.drawable.ic_activity_sup,
        color = R.color.sup,
        hasAscent = false
    ),
    COMBAT_SPORTS(
        id = 62,
        nameRes = R.string.combat_sport,
        icon = R.drawable.ic_activity_combatsport,
        color = R.color.combat_sport,
        hasDistance = false,
        hasAscent = false
    ),
    KETTLEBELL(
        id = 63,
        nameRes = R.string.kettlebell,
        icon = R.drawable.ic_activity_kettlebell,
        color = R.color.kettlebell,
        hasDistance = false,
        hasAscent = false
    ),
    DANCING(
        id = 64,
        nameRes = R.string.dancing,
        icon = R.drawable.ic_activity_dancing,
        color = R.color.dancing,
        hasDistance = false,
        hasAscent = false
    ),
    SNOWSHOEING(
        id = 65,
        nameRes = R.string.snow_shoeing,
        icon = R.drawable.ic_activity_snowshoeing,
        color = R.color.snow_shoeing,
    ),
    FRISBEE_GOLF(
        id = 66,
        nameRes = R.string.frisbee_golf,
        icon = R.drawable.ic_activity_frisbeegolf,
        color = R.color.frisbee_golf,
    ),
    FUTSAL(
        id = 67,
        nameRes = R.string.futsal,
        icon = R.drawable.ic_activity_futsal,
        color = R.color.futsal,
        hasDistance = false,
        hasAscent = false
    ),
    MULTISPORT(
        id = 68,
        nameRes = R.string.multisport,
        icon = R.drawable.ic_activity_multisport,
        color = R.color.suunto_performance,
    ),
    AEROBICS(
        id = 69,
        nameRes = R.string.aerobics,
        icon = R.drawable.ic_activity_aerobics,
        color = R.color.suunto_indoor_sports,
        hasDistance = false,
        hasAscent = false
    ),
    TREKKING(
        id = 70,
        nameRes = R.string.trekking,
        icon = R.drawable.ic_activity_trekking,
        color = R.color.suunto_outdoor_adventures,
    ),
    SAILING(
        id = 71,
        nameRes = R.string.sailing,
        icon = R.drawable.ic_activity_sailing,
        color = R.color.suunto_watersports,
        hasAscent = false
    ),
    KAYAKING(
        id = 72,
        nameRes = R.string.kayaking,
        icon = R.drawable.ic_activity_kayaking,
        color = R.color.suunto_watersports,
        hasAscent = false
    ),
    CIRCUIT_TRAINING(
        id = 73,
        nameRes = R.string.circuit_training,
        icon = R.drawable.ic_activity_circuittraining,
        color = R.color.suunto_indoor_sports,
        hasDistance = false,
        hasAscent = false
    ),
    TRIATHLON(
        id = 74,
        nameRes = R.string.triathlon,
        icon = R.drawable.ic_activity_triathlon,
        color = R.color.suunto_performance,
    ),
    PADEL(
        id = 75,
        nameRes = R.string.padel,
        icon = R.drawable.ic_activity_padel,
        color = R.color.padel,
        hasDistance = false,
        hasAscent = false
    ),
    CHEERLEADING(
        id = 76,
        nameRes = R.string.cheerleading,
        icon = R.drawable.ic_activity_cheerleading,
        color = R.color.suunto_team_racket,
        hasDistance = false,
        hasAscent = false
    ),
    BOXING(
        id = 77,
        nameRes = R.string.boxing,
        icon = R.drawable.ic_activity_boxing,
        color = R.color.suunto_indoor_sports,
        hasDistance = false,
        hasAscent = false
    ),
    SCUBADIVING(
        id = 78,
        nameRes = R.string.scuba_diving,
        icon = R.drawable.ic_activity_scuba,
        color = R.color.suunto_diving,
        hasDistance = false,
        hasAscent = false
    ),
    FREEDIVING(
        id = 79,
        nameRes = R.string.free_diving,
        icon = R.drawable.ic_activity_freediving,
        color = R.color.suunto_diving,
        hasDistance = true,
        hasAscent = false
    ),
    ADVENTURE_RACING(
        id = 80,
        nameRes = R.string.adventure_racing,
        icon = R.drawable.ic_activity_adventureracing,
        color = R.color.suunto_performance,
    ),
    GYMNASTICS(
        id = 81,
        nameRes = R.string.gymnastics,
        icon = R.drawable.ic_activity_gymnastics,
        color = R.color.suunto_indoor_sports,
        hasDistance = false,
        hasAscent = false
    ),
    CANOEING(
        id = 82,
        nameRes = R.string.canoeing,
        icon = R.drawable.ic_activity_canoeing,
        color = R.color.suunto_watersports,
        hasAscent = false
    ),
    MOUNTAINEERING(
        id = 83,
        nameRes = R.string.mountaineering,
        icon = R.drawable.ic_activity_mountaineering,
        color = R.color.suunto_outdoor_adventures,
    ),
    TELEMARKSKIING(
        id = 84,
        nameRes = R.string.telemark,
        icon = R.drawable.ic_activity_telemarkskiing,
        color = R.color.suunto_winter_sports,
        hasAscent = false
    ),
    OPENWATER_SWIMMING(
        id = 85,
        nameRes = R.string.open_water_swimming,
        icon = R.drawable.ic_activity_openwaterswimming,
        color = R.color.suunto_watersports,
        hasAscent = false
    ),
    WINDSURFING(
        id = 86,
        nameRes = R.string.windsurfing,
        icon = R.drawable.ic_activity_windsurfing,
        color = R.color.suunto_watersports,
        hasAscent = false
    ),
    KITESURFING_KITING(
        id = 87,
        nameRes = R.string.kitesurfing_kiting,
        icon = R.drawable.ic_activity_kitesurfing,
        color = R.color.suunto_watersports,
        hasAscent = false
    ),
    PARAGLIDING(
        id = 88,
        nameRes = R.string.paragliding,
        icon = R.drawable.ic_activity_paragliding,
        color = R.color.suunto_outdoor_adventures,
    ),

    //    FRISBEE(89) {
    // We have frisbeee golf already
    SNORKELING(
        id = 90,
        nameRes = R.string.snorkeling,
        icon = R.drawable.ic_activity_snorkeling,
        color = R.color.suunto_diving,
        hasAscent = false
    ),
    SURFING(
        id = 91,
        nameRes = R.string.surfing,
        icon = R.drawable.ic_activity_surfing,
        color = R.color.suunto_watersports,
        hasAscent = false
    ),
    SWIMRUN(
        id = 92,
        nameRes = R.string.swimrun,
        icon = R.drawable.ic_activity_swimrun,
        color = R.color.suunto_performance,
    ),
    DUATHLON(
        id = 93,
        nameRes = R.string.duathlon,
        icon = R.drawable.ic_activity_duathlon,
        color = R.color.suunto_performance,
    ),
    AQUATHLON(
        id = 94,
        nameRes = R.string.aquathlon,
        icon = R.drawable.ic_activity_aquathlon,
        color = R.color.suunto_performance,
    ),
    OBSTACLE_RACING(
        id = 95,
        nameRes = R.string.obstacle_race,
        icon = R.drawable.ic_activity_obstaclerace,
        color = R.color.suunto_performance,
    ),
    FISHING(
        id = 96,
        nameRes = R.string.fishing,
        icon = R.drawable.ic_activity_fishing,
        color = R.color.suunto_outdoor_adventures,
    ),
    HUNTING(
        id = 97,
        nameRes = R.string.hunting,
        icon = R.drawable.ic_activity_hunting,
        color = R.color.suunto_outdoor_adventures,
    ),

    // let's hide transition for now
    // todo how should we handle transition as it is the transition between triathlon activities
    // TRANSITION(98, R.string.transition, R.drawable.ic_activity_transition, R.color.suunto_unspecified);

    GRAVEL_CYCLING(
        id = 99,
        nameRes = R.string.gravel_cycling,
        icon = R.drawable.ic_activity_gravelcycling,
        color = R.color.cycling,
    ),
    MERMAIDING(
        id = 100,
        nameRes = R.string.mermaiding,
        icon = R.drawable.ic_activity_mermaiding,
        color = R.color.suunto_diving,
        hasDistance = true,
        hasAscent = false
    ),
    // TODO support SPEARFISHING
//    SPEARFISHING(
//        id = 101,
//        nameRes = R.string.mermaiding,
//        icon = R.drawable.ic_activity_mermaiding,
//        color = R.color.suunto_outdoor_adventures,
//    ),
    JUMP_ROPE(
        id = 102,
        nameRes = R.string.jump_rope,
        icon = R.drawable.ic_activity_jump_rope,
        color = R.color.suunto_indoor_sports,
        hasDistance = false,
        hasAscent = false
    ),
    TRACK_RUNNING(
        id = 103,
        nameRes = R.string.trackrunning,
        icon = R.drawable.ic_activity_track_running,
        color = R.color.track_running,
    ),
    CALISTHENICS(
        id = 104,
        nameRes = R.string.calisthenics,
        icon = R.drawable.ic_activity_calisthenics,
        color = R.color.suunto_indoor_sports,
        hasDistance = false,
        hasAscent = false
    ),
    E_BIKING(
        id = 105,
        nameRes = R.string.e_biking,
        icon = R.drawable.ic_activity_ebiking,
        color = R.color.cycling,
    ),
    E_MTB(
        id = 106,
        nameRes = R.string.e_mtb,
        icon = R.drawable.ic_activity_emtb,
        color = R.color.cycling,
    ),
    BACKCOUNTRY_SKIING(
        id = 107,
        nameRes = R.string.backcountry_skiing,
        icon = R.drawable.ic_activity_backcountry_skiing,
        color = R.color.backcountry_skiing,
    ),
    WHEELCHAIRING(
        id = 108,
        nameRes = R.string.wheelchairing,
        icon = R.drawable.ic_activity_wheelchairing,
        color = R.color.suunto_performance,
    ),
    HANDCYCLING(
        id = 109,
        nameRes = R.string.handcycling,
        icon = R.drawable.ic_activity_handcycling,
        color = R.color.suunto_performance,
    ),
    SPLITBOARDING(
        id = 110,
        nameRes = R.string.splitboarding,
        icon = R.drawable.ic_activity_split_boarding,
        color = R.color.split_boarding,
    ),
    BIATHLON(
        id = 111,
        nameRes = R.string.biathlon,
        icon = R.drawable.ic_activity_biathlon,
        color = R.color.biathlon
    ),
    MEDITATION(
        id = 112,
        nameRes = R.string.meditation,
        icon = R.drawable.ic_activity_meditation,
        color = R.color.stretching,
        hasDistance = false,
        hasAscent = false
    ),
    FIELD_HOCKEY(
        id = 113,
        nameRes = R.string.field_hockey,
        icon = R.drawable.ic_activity_field_hockey,
        color = R.color.soccer,
        hasAscent = false,
        hasDistance = false
    ),
    CYCLOCROSS(
        id = 114,
        nameRes = R.string.cyclocross,
        icon = R.drawable.ic_activity_cyclocross,
        color = R.color.cycling,
    ),
    VERTICAL_RUN(
        id = 115,
        nameRes = R.string.vertical_running,
        icon = R.drawable.ic_activity_vertical_run,
        color = R.color.vertical_run,
    ),
    SKI_MOUNTAINEERING(
        id = 116,
        nameRes = R.string.ski_mountaineering,
        icon = R.drawable.ic_activity_skimountaneering,
        color = R.color.ski_mountaineering,
    ),
    SKATE_SKIING(
        id = 117,
        nameRes = R.string.skate_skiing,
        icon = R.drawable.ic_activity_skateskiing,
        color = R.color.skate_skiing
    ),
    CLASSIC_SKIING(
        id = 118,
        nameRes = R.string.classic_skiing,
        icon = R.drawable.ic_activity_classicskiing,
        color = R.color.classic_skiing
    ),
    PILATES(
        id = 120,
        nameRes = R.string.pilates,
        icon = R.drawable.ic_activity_pilates,
        color = R.color.pilates,
        hasDistance = false,
        hasAscent = false
    ),
    CHORES(
        id = 119,
        nameRes = R.string.chores,
        icon = R.drawable.ic_activity_chores,
        color = R.color.chores,
        hasDistance = true,
        hasAscent = false,
    ),
    NEW_YOGA(
        id = 121,
        nameRes = R.string.new_yoga,
        icon = R.drawable.ic_activity_yoga,
        color = R.color.new_yoga,
        hasDistance = false,
        hasAscent = false
    );

    override val activityTypes: Set<CoreActivityType>
        get() = setOf(this)

    private fun getLocalizedName(resources: Resources): String {
        return resources.getString(nameRes)
    }

    companion object {
        @JvmField
        val DEFAULT = RUNNING
        val UNKNOWN = OTHER_6

        private val OTHERS = arrayOf(OTHER_1, OTHER_2, OTHER_3, OTHER_4, OTHER_5, OTHER_6)
        private val ALL_BUT_OTHERS = entries - OTHERS

        /**
         * @return the Activity type based on the id
         */
        @JvmStatic
        fun valueOf(id: Int): CoreActivityType = entries.firstOrNull { it.id == id } ?: UNKNOWN

        /**
         * Get all activities sorted by local names activity type. Note that
         * [CoreActivityType.ALL_BUT_OTHERS] are sorted and [CoreActivityType.OTHERS]
         * concatenated to the end
         */
        @JvmStatic
        fun getAllActivitiesSortedByLocalNames(resources: Resources): List<CoreActivityType> =
            ALL_BUT_OTHERS.sortedWith(compareBy { it.getLocalizedName(resources) }) + OTHERS
    }
}
