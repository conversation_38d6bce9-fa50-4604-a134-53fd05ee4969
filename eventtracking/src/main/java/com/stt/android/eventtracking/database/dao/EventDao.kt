package com.stt.android.eventtracking.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.stt.android.eventtracking.database.entity.LocalEvent

@Dao
internal interface EventDao {
    @Transaction
    @Query("SELECT * FROM ${LocalEvent.TABLE_NAME} WHERE ${LocalEvent.COLUMN_USER_ID} IS :userId AND ${LocalEvent.COLUMN_UPLOADED} = :uploaded")
    suspend fun getAll(userId: String?, uploaded: Int): List<LocalEvent>

    @Transaction
    @Query("SELECT * FROM ${LocalEvent.TABLE_NAME} WHERE ${LocalEvent.COLUMN_USER_ID} IS :userId AND ${LocalEvent.COLUMN_UPLOADED} = :uploaded AND ${LocalEvent.COLUMN_SOURCE} IS :source LIMIT :limit")
    suspend fun getLimited(userId: String?, uploaded: Int, source: String?, limit: Int): List<LocalEvent>

    @Query("SELECT COUNT(id) FROM ${LocalEvent.TABLE_NAME} WHERE ${LocalEvent.COLUMN_USER_ID} IS :userId AND ${LocalEvent.COLUMN_UPLOADED} = :uploaded AND ${LocalEvent.COLUMN_SOURCE} IS :source")
    suspend fun count(userId: String?, uploaded: Int, source: String?): Int

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(event: LocalEvent)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(events: List<LocalEvent>)

    @Update
    suspend fun update(events: List<LocalEvent>)

    @Delete
    suspend fun delete(events: List<LocalEvent>)

    @Transaction
    suspend fun deleteAllUploaded(userId: String?) {
        val events = getAll(userId, 1)
        if (events.isNotEmpty()) {
            delete(events)
        }
    }
}
