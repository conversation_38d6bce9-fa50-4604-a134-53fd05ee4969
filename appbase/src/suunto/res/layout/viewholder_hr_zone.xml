<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="androidx.core.content.ContextCompat" />

        <import type="com.stt.android.home.settings.zones.ZoneInfoItem" />

        <import type="android.view.View" />

        <variable
            name="item"
            type="com.stt.android.home.settings.zones.ZoneInfoItem" />


        <variable
            name="onZoneClicked"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lap_row_layout"
        android:onClick="@{onZoneClicked}"
        android:layout_width="match_parent"
        android:layout_height="62dp">

        <View
            android:layout_width="8dp"
            android:layout_height="31dp"
            android:layout_marginLeft="16dp"
            android:background="@{item.topBarColor}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="#DE0101" />

        <View
            android:layout_width="8dp"
            android:layout_height="31dp"
            android:layout_marginLeft="16dp"
            android:background="@{item.bottomBarColor}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:background="@color/colorAccent" />

        <View
            android:id="@+id/line"
            android:layout_width="24dp"
            android:layout_height="1dp"
            android:layout_marginStart="16dp"
            android:background="@color/suunto_light_gray"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="61dp"
            android:layout_marginStart="40dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/hr_zone_shape_gray"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTile"
            style="@style/Body.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="@{item.title}"
            android:textColor="#303030"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintBottom_toTopOf="@id/value"
            app:layout_constraintStart_toEndOf="@id/line"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="Max Hr" />

        <TextView
            android:id="@+id/value"
            style="@style/Body.Larger.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{item.value}"
            android:textColor="@{item.valueColor}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@id/tvTile"
            app:layout_constraintTop_toBottomOf="@id/tvTile"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="189 bpm" />

        <TextView
            style="@style/Body.Medium.DarkGray"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="@{item.percentage}"
            app:layout_constraintStart_toEndOf="@id/value"
            app:layout_constraintTop_toTopOf="@id/value"
            app:layout_constraintBottom_toBottomOf="@id/value"
            tools:text="126%" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
