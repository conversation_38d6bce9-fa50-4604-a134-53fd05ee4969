<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/userProfile"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageButton
            android:id="@+id/dashboardNotificationBell"
            android:layout_width="@dimen/dashboard_toolbar_button_size"
            android:layout_height="@dimen/dashboard_toolbar_button_size"
            android:layout_marginEnd="@dimen/size_spacing_small"
            android:background="?actionBarItemBackground"
            android:contentDescription="@string/notifications_title"
            android:scaleType="centerInside"
            android:src="@drawable/ic_bell"
            android:tint="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/dashboardNotificationCount"
            android:layout_gravity="center"
            android:layout_marginEnd="8dp"
            android:layout_marginTop="6dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/dashboardNotificationBell"
            app:layout_constraintTop_toTopOf="@id/dashboardNotificationBell"
            tools:text="3"
            tools:visibility="visible"
            style="@style/NotificationCounterCircle"/>

        <FrameLayout
            android:id="@+id/dashboardWatchContainer"
            android:layout_width="@dimen/dashboard_toolbar_button_size"
            android:layout_height="@dimen/dashboard_toolbar_button_size"
            android:background="?actionBarItemBackground"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/dashboardHeadphoneContainer"
            app:layout_constraintTop_toTopOf="parent">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/dashboardWatch"
                android:layout_width="@dimen/dashboard_toolbar_icon_size"
                android:layout_height="@dimen/dashboard_toolbar_icon_size"
                android:layout_gravity="center"
                android:scaleType="centerInside"
                app:lottie_loop="true"
                tools:src="@drawable/toolbar_watch_not_paired"
                tools:tint="?android:textColorPrimary"/>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/dashboardHeadphoneContainer"
            android:layout_width="@dimen/dashboard_toolbar_button_size"
            android:layout_height="@dimen/dashboard_toolbar_button_size"
            android:background="?actionBarItemBackground"
            android:focusable="true"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/dashboardNotificationBell"
            app:layout_constraintTop_toTopOf="parent">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/dashboardHeadphone"
                android:layout_width="@dimen/dashboard_toolbar_icon_size"
                android:layout_height="@dimen/dashboard_toolbar_icon_size"
                android:layout_gravity="center"
                android:scaleType="centerInside"
                app:lottie_loop="true"
                android:src="@drawable/ic_headphones_toolbar"
                tools:tint="?android:textColorPrimary"/>
        </FrameLayout>

        <ImageView
            android:id="@+id/addWorkoutButton"
            android:layout_width="@dimen/dashboard_toolbar_button_size"
            android:layout_height="@dimen/dashboard_toolbar_button_size"
            android:layout_gravity="center_vertical"
            android:background="?actionBarItemBackground"
            android:contentDescription="@string/add_workout"
            android:focusable="true"
            android:scaleType="centerInside"
            android:src="@drawable/ic_plus_toolbar"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/dashboardWatchContainer"
            app:layout_constraintTop_toTopOf="parent"
            tools:tint="?android:textColorPrimary"/>

        <Button
            android:id="@+id/profileButton"
            android:layout_width="@dimen/dashboard_toolbar_button_size"
            android:layout_height="@dimen/dashboard_toolbar_button_size"
            android:layout_marginStart="@dimen/size_spacing_small"
            android:background="?actionBarItemBackground"
            android:contentDescription="@string/userProfile"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/ButtonFlat"/>

        <ImageView
            android:id="@+id/profileImage"
            android:layout_width="@dimen/dashboard_profile_image_width"
            android:layout_height="@dimen/dashboard_profile_image_height"
            android:contentDescription="@string/userProfile"
            app:layout_constraintBottom_toBottomOf="@id/profileButton"
            app:layout_constraintEnd_toEndOf="@id/profileButton"
            app:layout_constraintStart_toStartOf="@id/profileButton"
            app:layout_constraintTop_toTopOf="@id/profileButton"
            tools:src="@drawable/ic_default_profile_image_light"/>

        <View
            android:id="@+id/iv_profile_red_dot"
            android:layout_width="@dimen/size_spacing_small"
            android:layout_height="@dimen/size_spacing_small"
            android:background="@drawable/red_dot"
            android:layout_marginEnd="2dp"
            android:layout_marginTop="2dp"
            app:layout_constraintTop_toTopOf="@id/profileImage"
            app:layout_constraintEnd_toEndOf="@id/profileImage"/>

        <TextView
            android:id="@+id/toolbarTitle"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:singleLine="true"
            app:layout_constraintBottom_toBottomOf="@id/profileButton"
            app:layout_constraintStart_toEndOf="@id/profileButton"
            app:layout_constraintTop_toTopOf="@id/profileButton"
            tools:text="@string/sign_up"
            style="@style/ActionBarTitle"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
