<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="androidx.core.content.ContextCompat" />

        <import type="androidx.core.text.HtmlCompat" />

        <variable
            name="text"
            type="int" />

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="clickable"
            type="boolean" />

        <variable
            name="removeBottomPadding"
            type="boolean" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:background="@drawable/selectable_card_background"
        android:clickable="@{clickable}"
        android:onClick="@{onClickListener}"
        android:paddingTop="@dimen/settings_padding_top"
        android:paddingBottom="@{removeBottomPadding ? @dimen/size_spacing_zero : @dimen/settings_padding_bottom}">

        <TextView
            android:id="@+id/title"
            style="@style/Body.Medium.Bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:includeFontPadding="false"
            android:paddingHorizontal="50dp"
            android:paddingTop="@dimen/size_spacing_xsmall"
            android:gravity="center"
            android:text="@{HtmlCompat.fromHtml(context.getString(text),0)}"
            android:lineSpacingMultiplier="1.3"
            android:textColor="@color/medium_grey"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/watch_updates_low_battery" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
