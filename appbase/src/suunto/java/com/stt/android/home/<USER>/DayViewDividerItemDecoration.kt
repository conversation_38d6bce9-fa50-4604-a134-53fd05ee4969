package com.stt.android.home.dayview

import androidx.annotation.ColorInt
import com.stt.android.home.dayview.daypageitems.DayViewWorkoutItem
import com.stt.android.ui.utils.ConditionalDividerItemDecoration
import com.xwray.groupie.Item

class DayViewDividerItemDecoration(val height: Int, @ColorInt val color: Int) : ConditionalDividerItemDecoration() {
    override val dividerHeight = height
    override val dividerColor: Int? = color

    override fun shouldShowDividerBetween(item: Item<*>, nextItem: Item<*>?) =
        nextItem is DayViewWorkoutItem
}
