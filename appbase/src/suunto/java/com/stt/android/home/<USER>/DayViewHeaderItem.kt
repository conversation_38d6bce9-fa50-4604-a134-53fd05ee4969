package com.stt.android.home.dayview

import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.common.ui.BaseBindableItem
import com.stt.android.databinding.ItemDayViewHeaderItemBinding

class DayViewHeaderItem(
    @StringRes private val textRes: Int
) : BaseBindableItem<ItemDayViewHeaderItemBinding>() {

    override fun getLayout() = R.layout.item_day_view_header_item

    override fun getId(): Long = textRes.toLong()

    override fun bind(viewBinding: ItemDayViewHeaderItemBinding, position: Int) {
        super.bind(viewBinding, position)
        viewBinding.textRes = textRes
    }
}
