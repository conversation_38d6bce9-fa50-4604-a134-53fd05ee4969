package com.stt.android.home.settings.hrintensityzones

import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.hrintensityzones.CombinedHrIntensityZones
import com.suunto.connectivity.hrintensityzones.HrIntensityZonesConsumer
import kotlinx.coroutines.rx2.await
import timber.log.Timber
import javax.inject.Inject

class HrIntensityZonesWatchApi @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
) {

    private fun logWarning(throwable: Throwable, message: String) {
        if (throwable is MissingCurrentWatchException) {
            Timber.i("Missing current watch")
        } else {
            Timber.w(throwable, message)
        }
    }

    private suspend fun supportsHrIntensityZones(waitForConnect: Boolean = false): Boolean =
        runSuspendCatching {
            suuntoWatchModel.getWatchWithStateRx(waitForConnect) { watch, watchState ->
                val compatibility =
                    SuuntoDeviceCapabilityInfoProvider[watch.suuntoBtDevice.deviceType]
                compatibility.supportsHrIntensityZones(watchState.deviceInfo?.capabilities)
            }.await()
        }.getOrElse {
            logWarning(it, "Error in supportsHrIntensityZones($waitForConnect)")
            false
        }

    private suspend fun requireHrIntensityZonesCapability() {
        if (!supportsHrIntensityZones(waitForConnect = false)) {
            throw UnsupportedOperationException("Watch does not support hr & intensity zones.")
        }
    }

    private suspend fun hrIntensityZonesConsumerWithSerial(): Pair<HrIntensityZonesConsumer, String> {
        requireHrIntensityZonesCapability()
        val spartan = suuntoWatchModel.currentWatch.await()
        val serial = spartan.suuntoBtDevice.serial
        return spartan.suuntoRepositoryClient.hrIntensityZonesConsumer to serial
    }

    private suspend fun setWithConsumer(
        block: suspend (HrIntensityZonesConsumer, String) -> Boolean
    ): Boolean = runSuspendCatching {
        val (consumer, serial) = hrIntensityZonesConsumerWithSerial()
        block(consumer, serial)
    }.getOrElse {
        logWarning(it, "Operation[Set] failed.")
        false
    }
    suspend fun sendUserMaxHeart(maxHR: Int) {
        requireHrIntensityZonesCapability()
        suuntoWatchModel.sendUserMaxHeart(maxHR)
    }

    suspend fun sendUserRestHeart(restHR: Int) {
        requireHrIntensityZonesCapability()
        suuntoWatchModel.sendUserRestHeart(restHR)
    }

    suspend fun setHrIntensityZones(combinedHrIntensityZones: CombinedHrIntensityZones) =
        setWithConsumer { consumer, serial ->
            consumer.setHrIntensityZones(serial, combinedHrIntensityZones)
        }
}
