package com.stt.android.home.explore.routes.addtowatch

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.stt.android.domain.routes.RouteWatchSyncState

@Composable
fun AddToWatchView(
    watchEnabled: Boolean,
    watchSyncState: RouteWatchSyncState,
    watchSyncResponseCode: Int,
    watchRouteListFull: Boolean,
    onCheckedChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    AndroidView(
        modifier = modifier.fillMaxWidth(),
        factory = { context ->
            AddToWatchView(context)
        }, update = { addToWatchView ->
            addToWatchView.setup(
                watchEnabled, watchSyncState, watchSyncResponseCode, watchRouteListFull
            ) { _, isChecked -> onCheckedChanged(isChecked) }
        })
}
