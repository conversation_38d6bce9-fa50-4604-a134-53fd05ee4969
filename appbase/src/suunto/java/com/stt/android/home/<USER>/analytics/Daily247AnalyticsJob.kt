package com.stt.android.home.diary.analytics

import android.content.Context
import android.content.SharedPreferences
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.TimeUtils
import com.stt.android.data.toEpochMilli
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.utils.STTConstants
import timber.log.Timber
import java.time.Clock
import java.time.temporal.ChronoUnit
import javax.inject.Inject

class Daily247AnalyticsJob(
    private val suunto247Analytics: Suunto247Analytics,
    private val sharedPreferences: SharedPreferences,
    private val clock: Clock,
    appContext: Context,
    params: WorkerParameters
) : CoroutineWorker(appContext, params) {

    class Factory
    @Inject constructor(
        private val suunto247Analytics: Suunto247Analytics,
        @SuuntoSharedPrefs private val sharedPreferences: SharedPreferences,
        private val clock: Clock
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return Daily247AnalyticsJob(
                suunto247Analytics,
                sharedPreferences,
                clock,
                context,
                params,
            )
        }
    }

    override suspend fun doWork(): Result {
        var lastAnalyticsSentTime: Long = sharedPreferences.getLong(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_247_ANALYTICS_LAST_SENT_TIME,
            0
        )

        // Only send analytics once per day
        if (TimeUtils.isBeforeStartOfToday(clock, lastAnalyticsSentTime)) {
            val oneWeekAgo = TimeUtils.getNow(clock).minusDays(7).truncatedTo(ChronoUnit.DAYS)
            // On the first time and when changing phones etc., send analytics from the last week
            if (lastAnalyticsSentTime <= 0) {
                lastAnalyticsSentTime = oneWeekAgo.toEpochMilli()
            }

            runSuspendCatching {
                suunto247Analytics.formAndSend247AnalyticsData(lastAnalyticsSentTime, TimeUtils.getTodayStartTime(clock))
            }.onFailure { e ->
                // Just logging the error because if lastAnalyticsSentTime is not updated to today, then this job
                // will run again after next sync with watch.
                Timber.w(e, "Failed to send 247 analytics data")
            }
        }

        return Result.success()
    }

    companion object {
        const val TAG = "Daily247AnalyticsJob"

        fun schedule(workManager: WorkManager) {
            workManager.enqueueUniqueWork(
                TAG,
                ExistingWorkPolicy.REPLACE,
                OneTimeWorkRequest.Builder(Daily247AnalyticsJob::class.java)
                    .build()
            )
        }
    }
}
