package com.stt.android.home.settings.zones

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import com.stt.android.R
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.utils.CustomTabsUtils

class ZonesDescriptionActivity: ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentWithM3Theme {
            val descriptionType = intent.getStringExtra(DESCRIPTION_TYPE).orEmpty()
            ZonesDescriptionNavGraph(ZonesDescriptionType.entries.first { it.name == descriptionType }, onBackClick = { finish() }, onWechatClick = {
                CustomTabsUtils.launchCustomTab(this, getString(R.string.follow_wechat_link))
            })
        }
    }

    companion object {
        fun newIntent(context: Context, descriptionType: String): Intent {
            return Intent(context, ZonesDescriptionActivity::class.java)
                .apply { putExtra(DESCRIPTION_TYPE, descriptionType) }
        }

        private const val DESCRIPTION_TYPE = "DESCRIPTION_TYPE"
    }
}
