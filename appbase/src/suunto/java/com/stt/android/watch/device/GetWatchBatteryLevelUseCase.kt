package com.stt.android.watch.device

import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.battery.BatteryLevel
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetWatchBatteryLevelUseCase @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
    private val dispatcherProvider: CoroutinesDispatcherProvider
) {

    suspend fun run(): BatteryLevel = withContext(dispatcherProvider.io) {
        suuntoWatchModel.getBatteryLevel()
    }
}
