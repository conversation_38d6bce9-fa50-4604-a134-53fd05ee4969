package com.stt.android.watch.background

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.R
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.utils.STTConstants
import javax.inject.Inject

class GetNewWorkoutSyncedMessage @Inject constructor(
    @SuuntoSharedPrefs
    private val suuntoSharedPrefs: SharedPreferences,
) {
    fun getNewWorkoutSyncedMessage(context: Context, userName: String): String {
        val pushMessageOrder = suuntoSharedPrefs.getInt(
            STTConstants.SuuntoPreferences.KEY_NEW_WORKOUT_SYNCED_MESSAGE_ORDER,
            1
        )
        suuntoSharedPrefs.edit {
            putInt(
                STTConstants.SuuntoPreferences.KEY_NEW_WORKOUT_SYNCED_MESSAGE_ORDER,
                if (pushMessageOrder == 5) 1 else pushMessageOrder + 1
            )
        }
        return when (pushMessageOrder) {
            1 -> context.getString(
                R.string.new_workout_push_message_1,
                userName
            )

            2 -> context.getString(
                R.string.new_workout_push_message_2,
                userName
            )

            3 -> context.getString(R.string.new_workout_push_message_3)
            4 -> context.getString(R.string.new_workout_push_message_4)
            5 -> context.getString(R.string.new_workout_push_message_5)
            else -> context.getString(
                R.string.new_workout_push_message_1,
                userName
            )
        }
    }
}
