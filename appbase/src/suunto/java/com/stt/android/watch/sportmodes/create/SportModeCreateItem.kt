package com.stt.android.watch.sportmodes.create

import android.view.View
import com.stt.android.R
import com.stt.android.common.ui.ClickableItem
import com.stt.android.databinding.ItemSportmodeCreateBinding
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.getStIdForMcId

data class SportModeCreateItem(
    private val activityId: Int,
    private val onClick: (activityId: Int, activityName: String) -> Unit
) : ClickableItem<ItemSportmodeCreateBinding>() {
    val activityType: ActivityType = ActivityType.valueOf(getStIdForMcId(activityId))
    val iconResId
        get() = activityType.iconId
    val activityNameResId
        get() = activityType.localizedStringId

    override fun getLayout() = R.layout.item_sportmode_create
    override fun getId() = activityId.toLong()

    override fun onClick(view: View) {
        val activityName = view.context.getString(activityNameResId)
        onClick(activityId, activityName)
    }
}
