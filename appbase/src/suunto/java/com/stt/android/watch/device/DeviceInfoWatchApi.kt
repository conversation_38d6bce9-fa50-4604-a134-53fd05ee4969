package com.stt.android.watch.device

import com.stt.android.data.device.DeviceInfoApi
import com.stt.android.domain.device.ConnectedWatchConnectionQuality
import com.stt.android.domain.device.ConnectedWatchConnectionState
import com.stt.android.domain.device.ConnectedWatchState
import com.stt.android.domain.device.ConnectedWatchSyncState
import com.stt.android.domain.device.DeviceInfo
import com.stt.android.domain.device.DeviceInfoWear
import com.stt.android.domain.device.SyncState
import com.stt.android.domain.device.UploadProgressState
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.firmware.CheckForNewerFirmwareUseCase
import com.stt.android.utils.toV2
import com.stt.android.utils.toV2Flowable
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.sdsmanager.model.getMDSPVersion
import com.suunto.connectivity.sdsmanager.model.getSIMVersion
import com.suunto.connectivity.sdsmanager.model.getSKUVersion
import com.suunto.connectivity.sdsmanager.model.getSerialNumber
import com.suunto.connectivity.sdsmanager.model.getWASSVersion
import com.suunto.connectivity.sdsmanager.model.getWearAppVersion
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.util.SupportedDevices
import com.suunto.connectivity.watch.WatchState
import hu.akarnokd.rxjava.interop.RxJavaInterop
import io.reactivex.Flowable
import io.reactivex.Scheduler
import io.reactivex.Single
import io.reactivex.rxkotlin.Flowables
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.withTimeout
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.time.Duration.Companion.seconds

class DeviceInfoWatchApi
@Inject constructor(
    val suuntoWatchModel: SuuntoWatchModel,
    private val checkForNewerFirmwareUseCase: CheckForNewerFirmwareUseCase,
    private val supportedDevices: SupportedDevices,
    @IoThread private val ioThread: Scheduler
) : DeviceInfoApi {

    /**
     * Internal wrapper class containing MdsDeviceInfo. Needed for emitting null device info.
     */
    internal class MdsDeviceInfoContainer(
        val mdsDeviceInfo: MdsDeviceInfo?
    )

    /**
     * Flowable to provide information about the state of currently paired watch
     * Feel free to add more states that you observe on UI if needed
     */
    override fun connectedWatchState(): Flowable<ConnectedWatchState> {
        return Flowables.combineLatest(
            suuntoWatchModel.stateChangeObservable.toV2Flowable(),
            isFirmwareUpdateAvailable()
        )
            .map { (watchState, isFWUpdateAvailable) ->
                val mdsInfo = watchState.deviceInfo
                val deviceInfo = if (mdsInfo != null) {
                    DeviceInfo(
                        fwVersion = mdsInfo.swVersion,
                        model = mdsInfo.variant,
                        serial = mdsInfo.serial,
                        hwVersion = mdsInfo.hw,
                        manufacturer = mdsInfo.manufacturerName,
                        sku = mdsInfo.getSKUVersion(),
                        variantName = mdsInfo.variant,
                        capabilities = mdsInfo.capabilities ?: emptyList()
                    )
                } else {
                    null
                }
                ConnectedWatchState(
                    connectedWatchSyncState = ConnectedWatchSyncState(
                        SyncState.from(watchState.syncState.state),
                        watchState.syncState.step,
                        watchState.syncState.stepCount
                    ),
                    connectedWatchConnectionState = ConnectedWatchConnectionState.from(watchState.connectionState.ordinal),
                    connectedWatchConnectionQuality = ConnectedWatchConnectionQuality.from(
                        watchState.connectionQuality.ordinal
                    ),
                    deviceInfo = deviceInfo,
                    isBusy = watchState.isDeviceBusy,
                    isRegistered = watchState.isRegistered,
                    isFWUpdateAvailable = isFWUpdateAvailable,
                    isFWUpdateRequired = mdsInfo != null && !supportedDevices.isSupportedFirmwareVersion(
                        mdsInfo
                    ),
                    uploadProgressState = UploadProgressState(
                        uploadInProgress = watchState.firmwareUpdateStatus.uploadInProgress,
                        percentage = watchState.firmwareUpdateStatus.progressPercentage,
                        fileSizeInBytes = watchState.firmwareUpdateStatus.fileSizeInBytes
                    )
                )
            }
            .doOnError {
                if (it is MissingCurrentWatchException) {
                    Timber.i("Missing current watch")
                } else {
                    Timber.w(it, "ConnectedWatchState subscription failed.")
                }
            }
    }

    /**
     * Check if firmware update is available.
     */
    private fun isFirmwareUpdateAvailable(): Flowable<Boolean> {
        return suuntoWatchModel.stateChangeObservable.toV2Flowable()
            .filter { it.deviceInfo != null }
            .distinctUntilChanged { old, new -> old.connectionState == new.connectionState }
            .filter { it.isConnected || it.isDisconnected }
            .flatMap { watchState ->
                val deviceInfo = watchState.deviceInfo
                if (deviceInfo != null) {
                    val deviceType: SuuntoDeviceType =
                        SuuntoDeviceType.fromVariantName(deviceInfo.variant)
                    if (!deviceType.supportsOtaUpdate(deviceInfo.swVersion)) {
                        checkForNewerFirmwareUseCase.isUpdateAvailable(
                            deviceInfo.variant,
                            deviceInfo.hwCompatibilityId,
                            deviceInfo.swVersion,
                            deviceInfo.productVersion,
                            deviceType.isSuunto7,
                            deviceType.supportOtaUpdateCheck(deviceInfo)
                        )
                    } else {
                        suuntoWatchModel.checkForNewOtaUpdates().toFlowable()
                            .map { it.otaUpdateState.hasUpdate() }
                    }
                } else {
                    Flowable.just(false)
                }
            }.onErrorReturn {
                if (it is MissingCurrentWatchException) {
                    Timber.i("Missing current watch")
                } else {
                    Timber.e(it, "Error during isFirmwareUpdateAvailable")
                }
                false
            }
            // 1. Check for FW update sometimes requires fetching info from the network.
            // 2. Also getting the first WatchState with non-null MdsDeviceInfo can take some time.
            // If the response is slow, the response will cause the observer to wait which causes undesirable behaviour like a blank screen
            .startWith(false)
            .distinctUntilChanged()
    }

    /**
     * Provides device build variant
     */
    override suspend fun variant(): String = deviceInfo()
        .map { it.mdsDeviceInfo?.variant.orEmpty() }
        .await()

    /**
     * Provides device software version
     */
    override suspend fun version(): String = deviceInfo()
        .map { it.mdsDeviceInfo?.swVersion.orEmpty() }
        .await()

    /**
     * Provides device mac address
     */
    override suspend fun macAddress(): String = withTimeout(20.seconds) {
        suuntoWatchModel.currentWatch()
            .suuntoBtDevice
            .macAddress
    }

    override fun isConnected(): Flow<Boolean> {
        return RxJavaInterop.toV2Flowable(
            suuntoWatchModel.stateChangeObservable
                .map { watchState ->
                    watchState.connectionState == WatchState.ConnectionState.CONNECTED
                }
        )
            .onErrorReturn {
                if (it !is MissingCurrentWatchException) {
                    Timber.w(it, "isConnected: stateChangeObservable emitted error")
                }
                false
            }
            .subscribeOn(ioThread)
            .asFlow()
    }

    override fun isSyncing(): Flow<Boolean> =
        suuntoWatchModel.stateChangeObservable.toV2Flowable()
            .map { SyncState.from(it.syncState.state) != SyncState.NOT_SYNCING }
            .distinctUntilChanged()
            .asFlow()
            .catch { emit(false) }

    override fun isBusy(): Flow<Boolean> =
        suuntoWatchModel.stateChangeObservable.toV2Flowable()
            .map { it.isDeviceBusy }
            .startWith(false) // Emit false immediately as getting watch state may take time
            .distinctUntilChanged()
            .asFlow()
            .catch { emit(false) }

    override suspend fun versionHash(): String = deviceInfo()
        .map { it.mdsDeviceInfo.getSIMVersion() }
        .await()

    /**
     * Provides device serial
     */
    override suspend fun serial(): String = deviceInfo()
        .map { it.mdsDeviceInfo.getSerialNumber() }
        .await()

    /**
     * Provides device SKU version
     */
    override fun sku(): Single<String> {
        return deviceInfo()
            .map { it.mdsDeviceInfo.getSKUVersion() }
    }

    private fun watchState(): Single<WatchState> {
        return suuntoWatchModel.stateChangeObservable
            .toV2()
            .observeOn(Schedulers.io())
            .firstOrError()
    }

    private fun deviceInfo(): Single<MdsDeviceInfoContainer> {
        return watchState()
            .map { MdsDeviceInfoContainer(it.deviceInfo) }
            .timeout(20L, TimeUnit.SECONDS)
    }

    override suspend fun wearInfo(): DeviceInfoWear = deviceInfo()
        .map { mdsDeviceInfoContainer ->
            val deviceInfo = mdsDeviceInfoContainer.mdsDeviceInfo
            DeviceInfoWear(
                wearAppVersion = deviceInfo.getWearAppVersion().takeIf { it.isNotBlank() },
                wassVersion = deviceInfo.getWASSVersion().takeIf { it.isNotBlank() },
                mdspVersion = deviceInfo.getMDSPVersion().takeIf { it.isNotBlank() }
            )
        }.await()

    override suspend fun otaUpdateSupported(): Boolean = watchState()
        .map { it.firmwareUpdateStatus.otaUpdateSupported }
        .timeout(20L, TimeUnit.SECONDS)
        .await()
}
