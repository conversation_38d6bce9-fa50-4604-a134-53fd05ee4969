package com.stt.android.watch.syncfail

import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.utils.STTConstants
import com.stt.android.watch.DeviceStateUpdate
import com.stt.android.watch.DeviceViewModel
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import javax.inject.Inject

@HiltViewModel
class DeviceSyncFailViewModel
@Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : DeviceViewModel(ioThread, mainThread) {

    private var deviceType: SuuntoDeviceType? = null

    fun onNeedHelpClick() {
        val publishId = if (deviceType?.isEon == true) {
            STTConstants.HelpShiftPublishId.SUUNTO_WATCH_SYNCING_OR_PAIRING_ISSUE_DIVE
        } else {
            STTConstants.HelpShiftPublishId.SUUNTO_WATCH_SYNC_FAILED
        }
        sharedViewModel.onHelpArticleClick(
            publishId,
            AnalyticsPropertyValue.HelpshiftSource.SYNC_FAILED
        )
    }

    fun onRetrySync() {
        sharedViewModel.sendAnalyticsEventFromWatchState(AnalyticsEvent.SUUNTO_SYNC_WATCH_RETRY, true, false, false)
        sharedViewModel.onSyncNow()
    }

    override fun onDeviceStateUpdate(state: DeviceStateUpdate) {
        super.onDeviceStateUpdate(state)
        state.deviceType?.isDataLayerDevice?.let {
            deviceType = state.deviceType
        }
    }
}
