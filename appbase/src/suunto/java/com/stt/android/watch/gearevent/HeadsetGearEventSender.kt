package com.stt.android.watch.gearevent

/**
 * Interface for sending headset gear events to the backend.
 * This interface allows the headset module to trigger gear events
 * without directly depending on the GearEventSender implementation.
 */
interface HeadsetGearEventSender {
    
    /**
     * Prepare for a new paired headset device. Once the device information is available,
     * a pair event will be scheduled to be sent to the backend.
     */
    fun setupForNewPairedHeadset()
    
    /**
     * Schedule a pair event to be sent for this headset device.
     * It is ok to call this multiple times for the same device.
     * Only one event will be sent.
     */
    fun sendHeadsetPairedEventIfNotSent(
        serialNumber: String,
        manufacturer: String,
        model: String,
        firmwareVersion: String?,
        hardwareVersion: String?
    )
    
    /**
     * Schedule an unpair event to be sent for this headset device.
     * This should only be called once when unpairing.
     *
     * Note: There is no entry to unpair a headset device for now, so not used.
     */
    fun sendHeadsetUnpairedEvent(serial: String)
    
    /**
     * Cancel any scheduled headset events that have not yet been sent.
     */
    fun clearHeadsetEvents()
}
