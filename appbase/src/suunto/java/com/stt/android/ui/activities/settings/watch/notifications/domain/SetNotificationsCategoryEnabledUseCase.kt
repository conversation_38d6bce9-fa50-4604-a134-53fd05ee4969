package com.stt.android.ui.activities.settings.watch.notifications.domain

import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.ui.activities.settings.watch.notifications.datasource.NotificationsDataSource
import kotlinx.coroutines.withContext
import javax.inject.Inject

class SetNotificationsCategoryEnabledUseCase @Inject constructor(
    private val dataSource: NotificationsDataSource,
    private val dispatcherProvider: CoroutinesDispatcherProvider
) {

    suspend fun run(call: <PERSON>ole<PERSON>, sms: <PERSON>olean, application: Boolean) =
        withContext(dispatcherProvider.io) {
            dataSource.setNotificationCategoryEnabled(call, sms, application)
        }
}
