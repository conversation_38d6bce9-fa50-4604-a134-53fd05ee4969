package com.stt.android.ui.activities.settings.helper

import android.content.SharedPreferences
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.utils.STTConstants
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class PredefinedRepliesHelper
@Inject constructor(
    private val userSettingsController: UserSettingsController,
    @SuuntoSharedPrefs private val sharedPreferences: SharedPreferences,
    private val suuntoWatchModel: SuuntoWatchModel,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) {
    private val coroutineScope = CoroutineScope(SupervisorJob())

    var predefinedReplies: List<String>
        get() = userSettingsController.settings.predefinedReplies.toList()
        set(value) {
            userSettingsController.storeSettings(
                userSettingsController.settings.setPredefinedReplies(value.toTypedArray())
            )
            sendPredefinedRepliesToWatch(value)
            sendAnalyticsForPredefinedReplyEdit()
        }

    val supportsMediaAndNotificationControls: Boolean
        get() {
            val watchVariantName: String? = sharedPreferences.getString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
                "N/A"
            )
            val firmwareVersion: String = sharedPreferences.getString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_FW_VERSION,
                "N/A"
            ) ?: "N/A"
            return SuuntoDeviceCapabilityInfoProvider[watchVariantName]
                .supportsMediaAndNotificationControls(firmwareVersion)
        }

    private fun sendPredefinedRepliesToWatch(predefinedReplies: List<String>) {
        coroutineScope.launch {
            runSuspendCatching {
                suuntoWatchModel.sendPredefinedReplies(predefinedReplies)
            }.onFailure { e ->
                if (e is MissingCurrentWatchException) {
                    Timber.i("Missing current watch")
                } else {
                    Timber.w(e, "Failed to send predefined replies to watch")
                }
            }
        }
    }

    private fun sendAnalyticsForPredefinedReplyEdit() {
        val firmwareVersion: String? = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_FW_VERSION,
            "N/A"
        )
        val watchVariantName: String? = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
            "N/A"
        )
        val watchModel: String? = watchVariantName?.let {
            AnalyticsDevicePropertyHelper.getWatchModelNameForVariantName(it)
        }
        val serialNumber: String? = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_SERIAL_NUMBER,
            "N/A"
        )

        val properties: AnalyticsProperties = AnalyticsProperties()
            .put(AnalyticsEventProperty.SUUNTO_WATCH_FIRMWARE_VERSION, firmwareVersion)
            .put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModel)
            .put(AnalyticsEventProperty.SUUNTO_WATCH_SERIAL_NUMBER, serialNumber)

        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.PREDEFINED_REPLY_EDIT_SAVED, properties)
    }
}
