package com.stt.android.login.requestpermission

import android.os.Build
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.jakewharton.rxrelay2.BehaviorRelay
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.ui.RxViewModel
import com.stt.android.domain.android.DeviceFeatureStates
import com.stt.android.domain.android.FetchBluetoothEnabledUseCase
import com.stt.android.domain.android.FetchLocationEnabledUseCase
import com.stt.android.domain.android.IsLocationPermissionGrantedUseCase
import com.stt.android.domain.android.IsNearbyDevicesPermissionGrantedUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.watch.DeviceAnalyticsUtil
import com.stt.android.watch.permission.PermissionEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.BackpressureStrategy
import io.reactivex.Scheduler
import io.reactivex.rxkotlin.Flowables
import io.reactivex.rxkotlin.subscribeBy
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class RequestPermissionViewModel
@Inject constructor(
    bluetoothUseCase: FetchBluetoothEnabledUseCase,
    locationUseCase: FetchLocationEnabledUseCase,
    private val locationPermissionUseCase: IsLocationPermissionGrantedUseCase,
    private val nearbyDevicesPermissionUseCase: IsNearbyDevicesPermissionGrantedUseCase,
    private val featureStates: DeviceFeatureStates,
    private val analyticsUtil: DeviceAnalyticsUtil,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : RxViewModel(ioThread, mainThread) {

    // LiveData instances used to hold the permission states
    private val _locationAllowed = MutableLiveData<Boolean>()
    val locationAllowed: LiveData<Boolean>
        get() = _locationAllowed

    private val _bluetoothEnabled = MutableLiveData<Boolean>()
    val bluetoothEnabled: LiveData<Boolean>
        get() = _bluetoothEnabled

    private val _locationEnabled = MutableLiveData<Boolean>()
    val locationEnabled: LiveData<Boolean>
        get() = _locationEnabled

    private val _nearbyDevicesAllowed = MutableLiveData<Boolean>()
    val nearbyDevicesAllowed: LiveData<Boolean>
        get() = _nearbyDevicesAllowed

    private val _nearbyDevicesRequired = MutableLiveData<Boolean>()
    val nearbyDevicesRequired: LiveData<Boolean>
        get() = _nearbyDevicesRequired

    private val _permissionsGrantedOrSkipped = MutableLiveData<Boolean>().apply { value = false }
    val permissionsGrantedOrSkipped: LiveData<Boolean>
        get() = _permissionsGrantedOrSkipped

    private val _permissionEvent = SingleLiveEvent<PermissionEvent>()
    val permissionEvent: LiveData<PermissionEvent>
        get() = _permissionEvent

    /**
     * Is location permission granted by the user
     */
    private val locationAllowedRelay = BehaviorRelay.create<Boolean>()

    /**
     * Is devices nearby permission granted by the user
     */
    private val nearbyDevicesAllowedRelay = BehaviorRelay.create<Boolean>()

    val areNotificationsEnabled get() = featureStates.areNotificationsEnabled()

    init {
        // Initialize location state
        _nearbyDevicesRequired.value = Build.VERSION.SDK_INT > Build.VERSION_CODES.R || "S" == Build.VERSION.CODENAME
        checkLocationPermissionState()
        nearbyDevicesAllowedRelay.accept(nearbyDevicesPermissionUseCase.nearbyDevicesPermissionGranted())

        // Updates the individual LiveData instances when one of them changes.
        disposables.add(
            Flowables.combineLatest(
                locationUseCase.locationEnabled(),
                bluetoothUseCase.bluetoothEnabled(),
                locationAllowedRelay.toFlowable(BackpressureStrategy.LATEST),
                nearbyDevicesAllowedRelay.toFlowable(BackpressureStrategy.LATEST)
            ) { locEnabled, btEnabled, locAllowed, nearbyDevicesAllowed -> PermissionsInfo(locEnabled, btEnabled, locAllowed, nearbyDevicesAllowed) }
                .observeOn(mainThread)
                .subscribeBy(onNext = {
                    _locationEnabled.value = it.locationEnabled
                    _bluetoothEnabled.value = it.bluetoothEnabled
                    _locationAllowed.value = it.locationAllowed
                    _nearbyDevicesAllowed.value = it.nearbyDevicesAllowed

                    if (it.locationEnabled && it.bluetoothEnabled && it.locationAllowed && it.nearbyDevicesAllowed) {
                        _permissionsGrantedOrSkipped.value = true
                    }
                }, onError = Timber::d)
        )
    }

    fun checkLocationPermissionState() {
        locationAllowedRelay.accept(locationPermissionUseCase.foregroundLocationPermissionGranted())
    }

    fun checkNearbyDevicesPermissionState() {
        val nearbyDevicesGranted = nearbyDevicesPermissionUseCase.nearbyDevicesPermissionGranted()
        featureStates.recheckBluetoothEnabled(nearbyDevicesGranted)
        nearbyDevicesAllowedRelay.accept(nearbyDevicesGranted)
    }

    fun onEnableBluetoothClick() {
        _permissionEvent.value = PermissionEvent.ENABLE_BLUETOOTH
        analyticsUtil.onPermissionButtonClick(AnalyticsPropertyValue.PermissionButton.TURN_ON_BLUETOOTH)
    }

    fun onEnableLocationClick() {
        _permissionEvent.value = PermissionEvent.ENABLE_LOCATION
        analyticsUtil.onPermissionButtonClick(AnalyticsPropertyValue.PermissionButton.TURN_ON_LOCATION)
    }

    fun onAllowLocationClick() {
        _permissionEvent.value = PermissionEvent.ALLOW_LOCATION
        analyticsUtil.onPermissionButtonClick(AnalyticsPropertyValue.PermissionButton.ALLOW_LOCATION)
    }

    fun onAllowNearbyDevicesClick() {
        _permissionEvent.value = PermissionEvent.ALLOW_NEARBY_DEVICES
        analyticsUtil.onPermissionButtonClick(AnalyticsPropertyValue.PermissionButton.ALLOW_NEARBY_DEVICES)
    }

    fun onSkipClick() {
        analyticsUtil.onPermissionSkipped(AnalyticsPropertyValue.PermissionContext.ON_BOARDING)
        _permissionsGrantedOrSkipped.value = true
    }

    fun onLocationPermissionScreenShowed() {
        analyticsUtil.onLocationPermissionScreenShowed(AnalyticsPropertyValue.PermissionContext.ON_BOARDING)
    }

    fun onRequestLocationPermissionsResult(result: String) {
        analyticsUtil.onRequestLocationPermissionsResult(AnalyticsPropertyValue.PermissionContext.ON_BOARDING, result)
    }

    fun onRequestNearbyDevicesPermissionsResult(result: String) {
        analyticsUtil.onRequestNearbyDevicesPermissionsResult(AnalyticsPropertyValue.PermissionContext.ON_BOARDING, result)
    }

    private data class PermissionsInfo(
        val locationEnabled: Boolean,
        val bluetoothEnabled: Boolean,
        val locationAllowed: Boolean,
        val nearbyDevicesAllowed: Boolean
    )
}
