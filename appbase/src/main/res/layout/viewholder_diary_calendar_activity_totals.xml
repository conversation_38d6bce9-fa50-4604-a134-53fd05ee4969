<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="totalValues"
            type="com.stt.android.home.diary.diarycalendar.TotalValues" />

        <variable
            name="horizontalPadding"
            type="Float" />

        <variable
            name="verticalPadding"
            type="Float" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?suuntoItemBackgroundColor"
        android:paddingStart="@{horizontalPadding}"
        android:paddingTop="@{verticalPadding}"
        android:paddingEnd="@{horizontalPadding}"
        android:paddingBottom="@{verticalPadding}">

        <include
            android:id="@+id/firstTotalsValue"
            layout="@layout/workout_value_label_and_unit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:gravityDirection="@{totalValues.gravityAtIndex(0)}"
            app:labelText="@{totalValues.labelAtIndex(0, context)}"
            app:layout_constraintEnd_toStartOf="@+id/secondTotalsValue"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:unitText="@{totalValues.unitAtIndex(0, context)}"
            app:valueText="@{totalValues.valueAtIndex(0)}" />

        <include
            android:id="@+id/secondTotalsValue"
            layout="@layout/workout_value_label_and_unit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="@{totalValues.size &gt; 1 ? View.VISIBLE : View.GONE}"
            app:gravityDirection="@{totalValues.gravityAtIndex(1)}"
            app:labelText="@{totalValues.labelAtIndex(1, context)}"
            app:layout_constraintEnd_toStartOf="@+id/thirdTotalsValue"
            app:layout_constraintStart_toEndOf="@+id/firstTotalsValue"
            app:layout_constraintTop_toTopOf="parent"
            app:unitText="@{totalValues.unitAtIndex(1, context)}"
            app:valueText="@{totalValues.valueAtIndex(1)}" />

        <include
            android:id="@+id/thirdTotalsValue"
            layout="@layout/workout_value_label_and_unit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="@{totalValues.size &gt; 2 ? View.VISIBLE : View.GONE}"
            app:gravityDirection="@{totalValues.gravityAtIndex(2)}"
            app:labelText="@{totalValues.labelAtIndex(2, context)}"
            app:layout_constraintEnd_toStartOf="@+id/fourthTotalsValue"
            app:layout_constraintStart_toEndOf="@+id/secondTotalsValue"
            app:unitText="@{totalValues.unitAtIndex(2, context)}"
            app:valueText="@{totalValues.valueAtIndex(2)}" />

        <include
            android:id="@+id/fourthTotalsValue"
            layout="@layout/workout_value_label_and_unit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="@{totalValues.size &gt; 3 ? View.VISIBLE : View.GONE}"
            app:gravityDirection="@{totalValues.gravityAtIndex(3)}"
            app:labelText="@{totalValues.labelAtIndex(3, context)}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/thirdTotalsValue"
            app:layout_constraintTop_toTopOf="parent"
            app:unitText="@{totalValues.unitAtIndex(3, context)}"
            app:valueText="@{totalValues.valueAtIndex(3)}" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
