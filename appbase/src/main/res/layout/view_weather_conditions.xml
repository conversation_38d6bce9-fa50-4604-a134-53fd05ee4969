<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="weatherConditionsIconDrawable"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="windSpeedText"
            type="java.lang.String" />

        <variable
            name="temperatureText"
            type="java.lang.String" />

        <variable
            name="windDirection"
            type="float" />
    </data>

    <merge
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:minHeight="@dimen/weather_conditions_min_height"
        tools:background="@drawable/weather_conditions_background"
        tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

        <ImageView
            android:id="@+id/weather_conditions_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_small"
            android:tint="@color/white"
            android:src="@{weatherConditionsIconDrawable}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_weather_broken_clouds_fill" />

        <TextView
            android:id="@+id/weather_conditions_temperature_text"
            style="@style/Body.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_xxsmall"
            android:text="@{temperatureText}"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/weather_conditions_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="+8 °C" />

        <ImageView
            android:id="@+id/weather_conditions_wind_direction_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_xsmall"
            android:tint="@color/white"
            android:rotation="@{windDirection + 180.0f}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/weather_conditions_temperature_text"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_wind_direction_fill" />

        <TextView
            android:id="@+id/weather_conditions_wind_speed_text"
            style="@style/Body.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_xxsmall"
            android:layout_marginEnd="@dimen/size_spacing_small"
            android:text="@{windSpeedText}"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/weather_conditions_wind_direction_indicator"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="9 m/s" />

    </merge>
</layout>
