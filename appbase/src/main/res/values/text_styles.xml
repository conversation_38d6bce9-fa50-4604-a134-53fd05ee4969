<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Subhead" parent="TextAppearance.AppCompat.Subhead">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="Subhead.BoldCaps" parent="Subhead">
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="Body1" parent="TextAppearance.AppCompat.Body1">
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="Body1.Alt" parent="Body1">
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="Body2" parent="TextAppearance.AppCompat.Body2">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="Body2.Alt" parent="Body2">
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="Body2.Alt.Comment">
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>

    <style name="Body3" parent="TextAppearance.AppCompat.Body2">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>

    </style>

    <style name="Body3.Alt" parent="Body3">
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="Body3.Inverse" parent="Body3">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
    </style>

    <style name="Caption" parent="TextAppearance.AppCompat.Caption">
        <item name="android:textSize">10sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="Caption.Alt" parent="Caption">
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
</resources>
