package com.stt.android.domain.database;

import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.user.LegacyUser;
import com.stt.android.domain.user.LegacyWorkoutHeader;
import com.stt.android.domain.workouts.WorkoutHeader;
import java.sql.SQLException;

@SuppressWarnings("deprecation")
public class DatabaseUpgrade3To5Helper extends DatabaseUpgradeHelper {
    private static final Class<?>[] EXISTING_TABLE_CLASSES = new Class<?>[] {
        LegacyUser.class, WorkoutHeader.class, ImageInformation.class
    };
    private static final String[] ANNOTATED_TABLE_NAMES = new String[] {
        LegacyUser.TABLE_NAME, LegacyWorkoutHeader.TABLE_NAME, ImageInformation.TABLE_NAME
    };

    public DatabaseUpgrade3To5Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        // once upon a time, we didn't use annotations for table names, the library will use the
        // lower cases of the class' name as the table name, it's broken in certain locales once we
        // add the annotation
        Cursor cursor =
            db.query("sqlite_master", new String[] { "name" }, "type=?", new String[] { "table" },
                null, null, null);
        int count = cursor.getCount();
        String[] existingTableNames = new String[count];
        int i = 0;
        while (cursor.moveToNext()) {
            existingTableNames[i++] = cursor.getString(0);
        }
        cursor.close();
        for (i = 0; i < EXISTING_TABLE_CLASSES.length; ++i) {
            String oldTableName = EXISTING_TABLE_CLASSES[i].getSimpleName().toLowerCase();
            String annotatedTableName = ANNOTATED_TABLE_NAMES[i];
            if (!oldTableName.equals(annotatedTableName)) {
                for (String name : existingTableNames) {
                    if (name.equals(oldTableName)) {
                        db.execSQL("ALTER TABLE "
                            + oldTableName
                            + " RENAME TO "
                            + annotatedTableName
                            + ";");
                        break;
                    }
                }
            }
        }
    }
}
