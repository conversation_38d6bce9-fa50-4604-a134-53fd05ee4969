package com.stt.android.domain.user

import com.stt.android.domain.workout.ActivityType

data class HeatmapType constructor(
    val name: String,
    val titleResource: Int,
    val iconResource: Int,
    val colorResource: Int,
    val tileUrlTemplate: String,
    val activityTypes: List<ActivityType>,
    val startingPointTileUrlTemplate: String,
    val startingPointColor: Int,
    val startingPointStroke: Int,
    val requiresPremium: Boolean,
    val analyticsName: String? = name
) {

    override fun equals(other: Any?): Boolean {
        if (this === other) {
            return true
        }

        return (other as? HeatmapType)?.name == name
    }

    override fun hashCode(): Int {
        return name.hashCode()
    }
}
