package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.stt.android.domain.user.LegacyUser;
import com.stt.android.follow.UserFollowStatus;
import java.sql.SQLException;

class DatabaseUpgrade21To22<PERSON><PERSON>per extends DatabaseUpgradeHelper {
    DatabaseUpgrade21To22Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        removeLegacyRequestTableIfExists();
        //noinspection deprecation
        DatabaseHelper.addColumnIfNotExist(db, LegacyUser.TABLE_NAME, LegacyUser.DbFields.FOLLOW_MODEL);
        TableUtils.createTableIfNotExists(connectionSource, UserFollowStatus.class);
        // Migration for follow status based on User.friend to UFS has now been dropped.
        // This was done in 2017 so all users should be migrated already.
    }

    //We used to have table called request that is not needed anymore
    private void removeLegacyRequestTableIfExists() {
        try {
            db.execSQL("DROP TABLE request;");
        } catch (Exception e) {
            //Let's ignore if dropping is not working
        }
    }
}
