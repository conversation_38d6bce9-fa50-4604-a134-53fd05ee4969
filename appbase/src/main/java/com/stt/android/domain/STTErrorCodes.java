package com.stt.android.domain;

import android.annotation.SuppressLint;
import android.content.res.Resources;
import com.stt.android.R;
import timber.log.Timber;

public enum STTErrorCodes {
    // FIXME: Add a third parameter which lins the error with the right localized text instead of relying on the number
    // Backend errors
    UNKNOWN(0, "Unknown error"),
    USERNAME_ALREADY_EXISTS(100, "Username already exists"),
    EMAIL_ALREADY_EXISTS(101, "Email already exists"),
    UNDER_13(102, "Under 13"),
    NOTIFICATION_NOT_SEND(103, "Notification not send"),
    ERROR_UPDATING_USER_SETTINGS(104, "Error updating user settings"),
    NOT_FOUND(404, "Resource not found"),
    TOO_MANY_FOLLOW_REQUESTS(429, "Too many follow requests"),
    GENERAL_ERROR(500, "General error"),
    DATABASE_ERROR(520, "Database error"),
    VISIBILITY_NOT_CHANGED(521, "Data visibility not changed"),
    SETTINGS_NOT_CHANGED(522, "User settings not changed"),
    WORKOUT_NOT_SAVED(523, "Workout could not be saved"),
    COMMENT_NOT_SENT(524, "Comment not sent"),
    COMMENT_RETRIEVAL_ERROR(525, "Comment retrieval error"),
    NO_COMMENT_ID(526, "Add comment: couldn't get commentId. Comment probably not sent"),
    INVALID_OBJECTID(527, "invalid ObjectId"),
    WORKOUT_NOT_FOUND(528, "Workout not found"),
    NOT_OWN_WORKOUT(529, "Tried to delete workout belonging to someone else"),
    REACTION_TO_WORKOUT_FAILED(538, "Reaction to workout failed"),
    FB_USER_TOKEN_ALREADY_IN_USE(548, "Facebook token already in use"),
    FB_USER_ID_ALREADY_USED(549, "Facebook uid already used by another ST user"),
    TW_USER_TOKEN_ALREADY_IN_USE(550, "Twitter token already in use"),
    TW_USER_ID_ALREADY_USED(551, "Twitter uid already used by another ST user"),
    // Internal app errors
    FB_USER_ERROR(1399, "Facebook user error"),
    FB_ERROR(1400, "Facebook error"),
    INVALID_USER_PASS(1401, "Invalid user password"),
    IO_STREAM_INTERRUPTED(1402, "I/O stream interrupted"),
    UNDEFINED_ERROR(1403, "Undefined internal error"),
    UNABLE_TO_SEARCH(1404, "Unable to search users"),
    INVITE_FAILED(1405, "Unable to process invite"),
    FETCH_SUBSCRIPTION_FAILED(1406, "Unable to fetch subscriptions"),
    FB_NOT_LINKED(1407, "Facebook account not linked"),
    INVALID_EMAIL(1408, "Invalid email"),
    FB_TOKEN_ERROR(1409, "Facebook token could not be validated or invalid"),
    PWD_RESET_NEEDED(1410, "User password has been reset"),
    INVALID_PIN_CODE(1412, "Pin code is invalid"),
    PHONE_NUMBER_ALREADY_EXISTS(1413, "Phone number already exists"),
    PROFILE_IMAGE_AUDIT_FAILED(562,"profile image audit failed"),
    FILE_SIZE_EXCEEDS_LIMIT(561, "File size exceeds limit"),
    SUBSCRIPTION_USED_BY_OTHER_USER(563, "Subscription is used by other user")
    ;

    private final int code;
    private final String description;

    private STTErrorCodes(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getLocalizedMessage(Resources res, String packageName) {
        int id = getLocalizedMessageRes(res, packageName);
        if (id == 0) {
            return res.getString(R.string.unknown_error_id, code);
        }
        return res.getString(id);
    }

    @SuppressLint("DiscouragedApi")
    public int getLocalizedMessageRes(Resources res, String packageName) {
        return res.getIdentifier("error_" + code, "string", packageName);
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static STTErrorCodes valueOf(int code) {
        for (STTErrorCodes error: values()) {
            if (error.getCode() == code) {
                return error;
            }
        }
        Timber.w("Unknown code for STTErrorCodes: %d", code);
        return UNKNOWN;
    }
}
