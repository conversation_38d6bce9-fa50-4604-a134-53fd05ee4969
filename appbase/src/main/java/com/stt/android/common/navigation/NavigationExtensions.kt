package com.stt.android.common.navigation

import androidx.annotation.IdRes
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentContainerView
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment

/**
 * Extension function to get [NavController] from [FragmentActivity] that hosts the
 * [NavHostFragment] in a [FragmentContainerView].
 * See https://issuetracker.google.com/issues/142847973#comment4 for more details.
 */
fun FragmentActivity.findNavController(@IdRes id: Int): NavController {
    return this.supportFragmentManager.findNavController(id)
}

/**
 * Extension function to get [NavController] from [Fragment] that hosts the
 * [NavHostFragment] in a [FragmentContainerView].
 * See https://issuetracker.google.com/issues/142847973#comment4 for more details.
 */
fun Fragment.findNavController(@IdRes id: Int): NavController {
    return this.parentFragmentManager.findNavController(id)
}

private fun FragmentManager.findNavController(@IdRes id: Int): NavController {
    return (this.findFragmentById(id) as NavHostFragment).navController
}

fun Fragment.findNavControllerByFragmentIdUsingChildFragmentManager(@IdRes fragmentById: Int): NavController {
    return (this.childFragmentManager.findFragmentById(fragmentById) as NavHostFragment).navController
}
