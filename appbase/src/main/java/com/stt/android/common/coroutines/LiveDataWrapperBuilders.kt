package com.stt.android.common.coroutines

import androidx.annotation.MainThread
import kotlinx.coroutines.CoroutineScope
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

class LiveDataSuspend<Result : Any>(
    coroutineScope: Lazy<CoroutineScope>,
    coroutineContext: CoroutineContext = EmptyCoroutineContext,
    action: suspend CoroutineScope.(Unit) -> Result
) : LiveDataSuspendWithParam<Result, Unit>(coroutineScope, coroutineContext, action) {
    @MainThread
    fun run() = super.run(Unit)
}

/**
 * Build a LiveData wrapper in a context where view model is not directly available, but view model
 * scope can be accessed via ViewModelScopeProvider
 */
fun <Result : Any> ViewModelScopeProvider.liveDataSuspend(
    coroutineContext: CoroutineContext = EmptyCoroutineContext,
    action: suspend CoroutineScope.(Unit) -> Result
) = LiveDataSuspend(lazy { viewModelScope }, coroutineContext, action)

/**
 * Build a LiveData wrapper in a context where view model is not directly available, but view model
 * scope can be accessed via ViewModelScopeProvider
 */
fun <Result : Any, Params> ViewModelScopeProvider.paramLiveDataSuspend(
    coroutineContext: CoroutineContext = EmptyCoroutineContext,
    action: suspend CoroutineScope.(Params) -> Result
) = LiveDataSuspendWithParam(lazy { viewModelScope }, coroutineContext, action)

/**
 * Build LiveData wrapper for a coroutine based asynchronous operation in a view model.
 *
 * This runs the code in the view model's scope in contrast to the [androidx.lifecycle.liveData],
 * which creates a new scope for each live data.
 *
 * This function also does not implement timeouts or a retry-mechanism like
 * [androidx.lifecycle.liveData].
 */
fun <Result : Any> CoroutineViewModel.liveDataSuspend(
    coroutineContext: CoroutineContext = EmptyCoroutineContext,
    action: suspend CoroutineScope.(Unit) -> Result
) = LiveDataSuspend(lazy { this }, coroutineContext, action)

/**
 * Build LiveData wrapper for a coroutine based asynchronous operation in a view model.
 *
 * This runs the code in the view model's scope in contrast to the [androidx.lifecycle.liveData],
 * which creates a new scope for each live data.
 *
 * This function also does not implement timeouts or a retry-mechanism like
 * [androidx.lifecycle.liveData].
 */
fun <Result : Any, Params> CoroutineViewModel.paramLiveDataSuspend(
    coroutineContext: CoroutineContext = EmptyCoroutineContext,
    action: suspend CoroutineScope.(Params?) -> Result
) = LiveDataSuspendWithParam(lazy { this }, coroutineContext, action)
