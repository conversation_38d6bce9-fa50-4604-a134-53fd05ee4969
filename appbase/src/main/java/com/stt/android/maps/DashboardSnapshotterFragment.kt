package com.stt.android.maps

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.stt.android.home.dashboardv2.getDashboardMapSnapshotterWidth
import com.stt.android.maps.mapbox.MapboxMapsProvider
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class DashboardSnapshotterFragment : SuuntoSupportMapFragment() {

    @Inject
    lateinit var suuntoMaps: SuuntoMaps

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    init {
        setOptions(
            SuuntoMapOptions().apply {
                liteMode = true
                uiAttribution = false
                uiLogo = false
                uiCompass = false
                showMyLocationMarker = false
                mapType = MAP_TYPE_DEFAULT
            }
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // no SuuntoMapView instances are needed for mapbox map snapshots
        return if (suuntoMaps.defaultProvider?.name == MapboxMapsProvider.NAME) {
            View(context)
        } else {
            super.onCreateView(inflater, container, savedInstanceState)
                ?.apply { visibility = View.INVISIBLE }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val snapshotterWidth = getDashboardMapSnapshotterWidth(requireContext())
        view.layoutParams = view.layoutParams?.apply {
            width = snapshotterWidth
        } ?: ViewGroup.LayoutParams(snapshotterWidth, ViewGroup.LayoutParams.WRAP_CONTENT)
    }

    override fun onResume() {
        super.onResume()

        mapView?.let {
            mapSnapshotter.addMapView(it)
        }
    }

    override fun onPause() {
        super.onPause()

        mapView?.let {
            mapSnapshotter.removeMapView(it)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        mapView?.let {
            mapSnapshotter.removeMapView(it)
        }
    }
}
