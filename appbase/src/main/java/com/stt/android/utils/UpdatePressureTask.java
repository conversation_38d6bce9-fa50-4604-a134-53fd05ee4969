package com.stt.android.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.hardware.Sensor;
import android.hardware.SensorManager;
import android.location.Location;
import android.os.Handler;
import android.os.Looper;
import android.widget.Toast;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.tasks.Tasks;
import com.stt.android.STTApplication;
import com.stt.android.SimpleBackgroundTask;
import com.stt.android.domain.weather.GetWeatherConditionsUseCase;
import com.stt.android.domain.weather.WeatherConditions;
import com.stt.android.location.LastLocationRequest;
import com.stt.android.location.LocationModel;
import java.util.Locale;
import java.util.concurrent.ExecutionException;
import javax.inject.Inject;
import pub.devrel.easypermissions.EasyPermissions;
import timber.log.Timber;

public class UpdatePressureTask extends SimpleBackgroundTask<Void> {
    /**
     * How old the pressure value needs to be to get a new one (in milliseconds).
     * Currently half an hour.
     */
    private static final long MIN_PRESSURE_DELAY = STTConstants.DEBUG ? 0 : 30 * DateUtils.MINUTE_IN_MILLIS;

    private static final long MAX_TIME_UPDATE_SUN = DateUtils.HOUR_IN_MILLIS;

    /**
     * The lowest measurable sea-level pressure is found at the centers of tropical cyclones and
     * tornadoes, with a record low of 870 mbar. (http://en.wikipedia.org/wiki/Atmospheric_pressure)
     */
    private static final float MINIMUM_VALID_PRESSURE = 800.0F;
    private final Callbacks listener;

    @Inject
    SensorManager sensorManager;

    @Inject
    LocationModel locationModel;

    @Inject
    Context context;

    @Inject
    SharedPreferences sharedPreferences;

    @Inject
    GetWeatherConditionsUseCase getWeatherConditionsUseCase;

    private final FusedLocationProviderClient fusedLocationProviderClient;

    public UpdatePressureTask(Callbacks listener) {
        super();
        STTApplication.getComponent().inject(this);
        this.listener = listener;
        fusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(context);
    }

    public static boolean hasPressureSensor(SensorManager sensorManager) {
        return sensorManager.getDefaultSensor(Sensor.TYPE_PRESSURE) != null;
    }

    @Override
    public Void call() throws Exception {
        if (hasPressureSensor(sensorManager)) {
            updatePressureValueIfTooOld();
        }
        return null;
    }

    @SuppressLint("MissingPermission")
    private void updatePressureValueIfTooOld() {
        if (!EasyPermissions.hasPermissions(context, PermissionUtils.LOCATION_PERMISSIONS)) {
            // we do not have the location permissions granted
            Timber.w("Missing location permission");
            return;
        }

        long lastPressureTimestamp = sharedPreferences.getLong(STTConstants.DefaultPreferences
                .KEY_REFERENCE_PRESSURE_TIMESTAMP, 0);
        if (System.currentTimeMillis() - MIN_PRESSURE_DELAY > lastPressureTimestamp) {
            Location location = null;
            try {
                location = Tasks.await(fusedLocationProviderClient.getLastLocation());
            } catch (ExecutionException | InterruptedException ignored) {
            }
            // In case there's no Google Play Services or the location was null try with our
            // own location provider
            if (location == null) {
                location = locationModel.getLastLocation(LastLocationRequest.builder()
                    .skipPassiveProvider(false)
                    .timeInMilliSecondsSinceEpoch(System.currentTimeMillis() - MAX_TIME_UPDATE_SUN)
                    .build());
            }
            if (location == null) {
                return;
            }

            WeatherConditions result = getWeatherConditionsUseCase.runBlocking(
                new GetWeatherConditionsUseCase.Params(
                    location.getLatitude(),
                    location.getLongitude()
                )
            );

            if (result != null) {
                Float airPressure = result.getAirPressure();
                Float seaLevelAirPressure = result.getSeaLevelAirPressure();
                /*
                 Sometimes open weather map gives us sea level and some other times just pressure.
                 We contacted open weather map and "in common case 'pressure' is on the sea level"
                  */
                Float pressure = seaLevelAirPressure != null ? seaLevelAirPressure : airPressure;
                // Only store the pressure if it's above some minimum reasonable value.
                if (pressure != null && pressure > MINIMUM_VALID_PRESSURE) {
                    storePressure(pressure);
                }
            }
        }
    }

    private void storePressure(float pressure) {
        Timber.d("UpdatePressureTask.storePressure(pressure: %.4f)", pressure);
        if (STTConstants.DEBUG) {
            showValuesInUi(pressure);
        }
        sharedPreferences.edit()
                .putFloat(STTConstants.DefaultPreferences.KEY_REFERENCE_PRESSURE, pressure)
                .putLong(STTConstants.DefaultPreferences.KEY_REFERENCE_PRESSURE_TIMESTAMP, System.currentTimeMillis())
                .apply();
    }

    private void showValuesInUi(final float pressure) {
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            String msg = String.format(Locale.US, "Got open weather pressure: %.2f",
                    pressure);
            Toast.makeText(context.getApplicationContext(), msg, Toast.LENGTH_LONG).show();
        });
    }

    @Override
    protected void onSuccess(Void aVoid) {
        super.onSuccess(aVoid);
        listener.onSuccess();
    }

    @Override
    protected void onException(Exception e) throws RuntimeException {
        super.onException(e);
        listener.onFailure(e);
    }

    public interface Callbacks {
        void onSuccess();

        void onFailure(Exception e);
    }
}
