package com.stt.android.utils.permissions.backgroundLocation

import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.view.ContextThemeWrapper
import com.stt.android.R
import com.stt.android.extensions.openAppSettings
import com.stt.android.utils.isBackgroundLocationPermissionGranted
import io.reactivex.Completable
import io.reactivex.subjects.PublishSubject
import timber.log.Timber
import java.util.concurrent.atomic.AtomicLong

class BackgroundLocationRequestHelper {
    private enum class Events {
        RequestFailed,
        FineLocationPermissionGranted,
        BackgroundLocationPermissionGranted
    }

    class UserRejectedRequest : RuntimeException("User rejected request")
    class DialogNotOpenedSettingsShown : RuntimeException("Dialog not opened. Settings shown instead")

    private val events = PublishSubject.create<Events>()

    fun checkAndRequestBackgroundLocationPermission(
        context: Context,
        requestFineLocationPermission: (() -> Unit)?,
        requestBackgroundLocationPermission: (() -> Unit)?
    ): Completable {
        return if (isPermissionGranted(context)) {
            Completable.complete()
        } else {
            val startTime = AtomicLong(0)
            showBackgroundLocationRequestDialog(context)
                .doOnComplete {
                    startTime.set(System.currentTimeMillis())
                }
                .andThen(requestFineLocationPermission(requestFineLocationPermission))
                .andThen(
                    requestBackgroundLocationPermission(requestBackgroundLocationPermission)
                        .onErrorResumeNext {
                            Timber.d(it, "Error in requestBackgroundLocationPermission")
                            val time = System.currentTimeMillis()
                            if (time - startTime.get() < BACKGROUND_REQUEST_DIALOG_TOO_QUICKLY_CLOSED_MS) {
                                Timber.d("Permission dialog disappeared too quick. Opening settings.")
                                openSettings(context)
                                    .andThen(Completable.error(DialogNotOpenedSettingsShown()))
                            } else {
                                Completable.error(it)
                            }
                        }
                )
        }
    }

    private fun showBackgroundLocationRequestDialog(
        context: Context
    ): Completable {
        return Completable.fromPublisher<Void> { publisher ->
            val message = injectBackgroundOptionLabelIntoText(
                context,
                R.string.request_background_location_permission_purpose
            )
            val title = context.getString(R.string.request_permission)
            val contextThemeWrapper = ContextThemeWrapper(context, R.style.WhiteTheme)
            val builder = AlertDialog.Builder(contextThemeWrapper)
                .setTitle(title)
                .setMessage(message)
                .setOnCancelListener { publisher.onError(UserRejectedRequest()) }
                .setPositiveButton(R.string.allow) { _, _ ->
                    publisher.onComplete()
                }
            builder.create().show()
        }
    }

    private fun requestFineLocationPermission(requestFineLocationPermission: (() -> Unit)?): Completable {
        return Completable.fromAction {
            requestFineLocationPermission?.invoke()
        }
            .mergeWith(expectNextEvent(Events.FineLocationPermissionGranted))
    }

    private fun requestBackgroundLocationPermission(requestBackgroundLocationPermission: (() -> Unit)?): Completable {
        return Completable.fromAction {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                requestBackgroundLocationPermission?.invoke()
            } else {
                throw Exception("Wrong API level: ${Build.VERSION.SDK_INT}")
            }
        }
            .mergeWith(expectNextEvent(Events.BackgroundLocationPermissionGranted))
    }

    private fun expectNextEvent(event: Events): Completable {
        return events
            .firstOrError()
            .flatMapCompletable {
                if (it == event) {
                    Completable.complete()
                } else {
                    Completable.error(Exception("Wrong event. Expecting: $event , actual: $it"))
                }
            }
    }

    private fun openSettings(context: Context): Completable {
        return Completable.fromAction {
            context.openAppSettings()
        }
    }

    fun onRequestPermissionsResult(
        activity: Activity,
        requestCode: Int,
        grantResults: IntArray
    ) {
        when (requestCode) {
            REQUEST_FINE_LOCATION -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    events.onNext(Events.FineLocationPermissionGranted)
                } else {
                    events.onNext(Events.RequestFailed)
                }
            }
            REQUEST_BACKGROUND_LOCATION -> {
                if (isPermissionGranted(activity)) {
                    events.onNext(Events.BackgroundLocationPermissionGranted)
                } else {
                    events.onNext(Events.RequestFailed)
                }
            }
        }
    }

    companion object {
        const val REQUEST_FINE_LOCATION = 5154
        const val REQUEST_BACKGROUND_LOCATION = 5155
        const val BACKGROUND_REQUEST_DIALOG_TOO_QUICKLY_CLOSED_MS = 700

        @JvmStatic
        fun injectBackgroundOptionLabelIntoText(context: Context, sourceResource: Int): String {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                context.getString(
                    sourceResource,
                    context.packageManager.backgroundPermissionOptionLabel
                )
            } else {
                context.getString(
                    sourceResource,
                    context.getString(R.string.background_location_request_allow_all_the_time)
                )
            }
        }

        @JvmStatic
        fun isPermissionGranted(context: Context): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                context.isBackgroundLocationPermissionGranted()
            } else {
                return true
            }
        }
    }
}
