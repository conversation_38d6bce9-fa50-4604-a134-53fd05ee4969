package com.stt.android.utils

object SupportUrlData {

    var supportUrl = mapOf(
        STTConstants.HelpShiftPublishId.GPS_ISSUE_SUPPORT_PUBLISH_ID to STTConstants.HelpShiftPublishId.GPS_ISSUE_SUPPORT_PUBLISH_ID_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_7_FAILED_TO_PAIR to STTConstants.HelpShiftPublishId.SUUNTO_7_FAILED_TO_PAIR_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_7_NOT_CONNECTED to STTConstants.HelpShiftPublishId.SUUNTO_7_NOT_CONNECTED_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_WATCH_SYNC_FAILED to STTConstants.HelpShiftPublishId.SUUNTO_WATCH_SYNC_FAILED_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_WATCH_SYNCING_OR_PAIRING_ISSUE to STTConstants.HelpShiftPublishId.SUUNTO_WATCH_SYNCING_OR_PAIRING_ISSUE_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_WATCH_DISCONNECTED_HELP_LEGAGY to STTConstants.HelpShiftPublishId.SUUNTO_WATCH_DISCONNECTED_HELP_LEGAGY_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_AMBIT1_AND_AMBIT2 to STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_AMBIT1_AND_AMBIT2_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_TRAVERSE_AND_AMBIT3 to STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_TRAVERSE_AND_AMBIT3_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_7 to STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_7_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_EON to STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_EON_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_CABLE_CONNECTED_DIVE to STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_CABLE_CONNECTED_DIVE_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_GENERIC to STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_GENERIC_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_SPORT_MODE_HELP to STTConstants.HelpShiftPublishId.SUUNTO_SPORT_MODE_HELP_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_MAIN_SUPPORT to STTConstants.HelpShiftPublishId.SUUNTO_MAIN_SUPPORT_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_D5 to STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_D5_URL,
        STTConstants.HelpShiftPublishId.SUUNTO_WATCH_SYNCING_OR_PAIRING_ISSUE_DIVE to STTConstants.HelpShiftPublishId.SUUNTO_WATCH_SYNCING_OR_PAIRING_ISSUE_DIVE_URL,
        STTConstants.HelpShiftPublishId.MC_CONNECTION_FAQ_ID to STTConstants.HelpShiftPublishId.MC_CONNECTION_FAQ_ID_URL,
        STTConstants.HelpShiftPublishId.DELETE_ACCOUNT_ID to STTConstants.HelpShiftPublishId.DELETE_ACCOUNT_ID_URL,
        STTConstants.HelpShiftPublishId.RESET_PASSWORD to STTConstants.HelpShiftPublishId.RESET_PASSWORD_URL,
        STTConstants.HelpShiftPublishId.NOTIFICATIONS_ID to STTConstants.HelpShiftPublishId.NOTIFICATIONS_ID_URL,
    )
}
