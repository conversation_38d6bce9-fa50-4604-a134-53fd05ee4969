package com.stt.android.home.dashboardv2.ui.widgets.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyXLarge
import com.stt.android.compose.theme.spacing

@Composable
fun PremiumForeground(
    editMode: <PERSON><PERSON><PERSON>,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface.copy(alpha = 0.5f)),
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp)
                .graphicsLayer {
                    scaleX = 2f
                    rotationZ = -45f
                }
                .background(color = MaterialTheme.colorScheme.primaryContainer)
        ) {
            Text(
                text = stringResource(R.string.premium),
                style = MaterialTheme.typography.bodyXLarge.merge(color = MaterialTheme.colorScheme.onSecondary),
                modifier = Modifier.graphicsLayer {
                    scaleX = 0.5f
                }
            )
        }
        if (editMode && onRemoveClick != null) {
            Box(
                contentAlignment = Alignment.TopEnd,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(MaterialTheme.spacing.medium),
            ) {
                RemoveWidgetButton(
                    onClick = onRemoveClick,
                )
            }
        }
    }
}

@Preview(widthDp = 140, heightDp = 170)
@Preview(widthDp = 170, heightDp = 170)
@Preview(widthDp = 210, heightDp = 170)
@Composable
private fun RequirePremiumPreview() {
    M3AppTheme {
        PremiumForeground(
            editMode = false,
            onRemoveClick = {}
        )
    }
}
