package com.stt.android.home.diary.diarycalendar.planner

import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlan
import com.stt.android.home.diary.diarycalendar.planner.models.CatalogueUiState
import com.stt.android.home.diary.diarycalendar.planner.models.PlanUiState
import com.stt.android.home.diary.diarycalendar.planner.models.ProgramDetailsUiState
import com.stt.android.home.diary.diarycalendar.planner.models.Question
import com.stt.android.home.diary.diarycalendar.planner.models.QuestionId
import com.stt.android.home.diary.diarycalendar.planner.models.WeeklyPlanUiState

sealed class Catalogue {
    data object Loading : Catalogue()
    data class Data(val catalogueUiState: CatalogueUiState) : Catalogue()
}

sealed class ActiveTrainingPlan {
    data object Loading : ActiveTrainingPlan()
    data object UnknownError : ActiveTrainingPlan()
    data object NetworkError : ActiveTrainingPlan()
    data class PlanIsBeingGenerated(val plan: TrainingPlan) : ActiveTrainingPlan()
    data class PlanGenerationFailed(val planId: String): ActiveTrainingPlan()
    data class NoActivePlan(val lastPlan: TrainingPlan?) : ActiveTrainingPlan()
    data class Data(val planUiState: PlanUiState) : ActiveTrainingPlan()
}

sealed class ProgramDetails {
    data object Loading : ProgramDetails()
    data object UnknownError : ProgramDetails()
    data object NetworkError : ProgramDetails()
    data object InvalidProgramId : ProgramDetails()
    sealed class Details : ProgramDetails() {
        data class Info(val programDetailsUiState: ProgramDetailsUiState) : Details()
        data object NoSurveyAvailable : Details()
        sealed class Survey(
            open val questions: List<Question>,
            open val version: String,
        ) : Details() {
            data class AnswerQuestions(
                private val internalCurrentQuestionId: QuestionId,
                override val questions: List<Question>,
                override val version: String,
                val onContinueClick: () -> Unit,
                val onBackClick: () -> Unit,
            ) : Survey(questions, version) {
                val currentQuestion: Question =
                    questions.first { it.id == internalCurrentQuestionId }
                val internalCurrentQuestionIndex: Int =
                    questions.indexOfFirst { it.id == internalCurrentQuestionId }
                val externalQuestionIndex: Int = internalCurrentQuestionIndex + 1
                val totalNumberOfQuestions: Int = questions.size
                val isFirstQuestion: Boolean = internalCurrentQuestionIndex > 0
            }

            data class Summary(
                override val questions: List<Question>,
                override val version: String
            ) :
                Survey(questions, version)

            data class EditQuestion(
                override val questions: List<Question>,
                override val version: String,
                val questionIdToEdit: QuestionId,
                val onDoneClick: () -> Unit,
            ) :
                Survey(questions, version) {
                val currentQuestionToEdit: Question = questions.first { it.id == questionIdToEdit }
            }
        }
    }
}

data class GeneratedPlanDetails(
    val planUiState: PlanUiState,
    val weeklyPlanUiState: WeeklyPlanUiState,
)
