package com.stt.android.home.diary.diarycalendar.planner.composables

import androidx.annotation.ColorInt
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.graphics.toColorInt
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.home.diary.diarycalendar.planner.models.Legend
import com.stt.android.home.diary.diarycalendar.planner.models.WeeklyLegend

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun WeeklyChartLegends(
    legends: List<WeeklyLegend>,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
    ) {
        FlowRow(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.smaller),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        ) {
            legends.forEach { legend ->
                Column(
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
                ) {
                    WeeklyChartLegend(
                        text = legend.completedLegend.text,
                        color = legend.completedLegend.color,
                    )
                    if (legend.plannedLegend != null) {
                        WeeklyChartLegend(
                            text = legend.plannedLegend.text,
                            color = legend.plannedLegend.color,
                            isPlanned = true,
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun WeeklyChartLegend(
    text: String,
    @ColorInt color: Int,
    modifier: Modifier = Modifier,
    isPlanned: Boolean = false,
) {
    val background = remember(color, isPlanned) {
        with(color) {
            if (isPlanned) {
                applyAlphaToSolidColor(this, 0.25f)
            } else {
                this
            }
        }
    }

    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(6.dp),
    ) {
        Box(
            modifier = Modifier
                .height(9.dp)
                .width(12.dp)
                .background(color = Color(background), shape = RoundedCornerShape(2.dp))
                .then(
                    if (isPlanned) {
                        Modifier
                            .padding(0.5.dp)
                            .drawBehind {
                                drawRoundRect(
                                    color = Color(color),
                                    cornerRadius = CornerRadius(2.dp.toPx(), 2.dp.toPx()),
                                    style = Stroke(
                                        width = 1.dp.toPx(),
                                        pathEffect = PathEffect.dashPathEffect(
                                            floatArrayOf(5f, 5f),
                                            2.5f
                                        )
                                    )
                                )
                            }
                    } else {
                        Modifier
                    }
                )
        )

        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall,
            color = if (isPlanned) MaterialTheme.colorScheme.secondary else MaterialTheme.colorScheme.onSurface
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun WeeklyChartLegendPreview() {
    M3AppTheme {
        WeeklyChartLegend(
            text = "Running 140",
            color = "#FDD300".toColorInt(),
        )
    }
}

@Preview(showBackground = true, widthDp = 320)
@Composable
private fun WeeklyChartLegendsPreview() {
    M3AppTheme {
        Box(
        ) {
            WeeklyChartLegends(
                legends = listOf(
                    WeeklyLegend(
                        plannedLegend = Legend(
                            text = "Planned 500",
                            color = "#FDD300".toColorInt(),
                        ),
                        completedLegend = Legend(
                            text = "Running 790",
                            color = "#FDD300".toColorInt(),
                        ),
                    ),
                    WeeklyLegend(
                        plannedLegend = null,
                        completedLegend = Legend(
                            text = "Walking 180",
                            color = "#55D781".toColorInt(),
                        ),
                    ),
                    WeeklyLegend(
                        plannedLegend = Legend(
                            text = "Planned 440",
                            color = "#FF7C3B".toColorInt(),
                        ),
                        completedLegend = Legend(
                            text = "Cycling 440",
                            color = "#FF7C3B".toColorInt(),
                        ),
                    ),
                    WeeklyLegend(
                        plannedLegend = null,
                        completedLegend = Legend(
                            text = "Gym 100",
                            color = "#FF467E".toColorInt(),
                        ),
                    ),
                    WeeklyLegend(
                        plannedLegend =  Legend(
                            text = "Planned 420",
                            color = "#59C7FE".toColorInt(),
                        ),
                        completedLegend = Legend(
                            text = "Swimming -",
                            color = "#59C7FE".toColorInt(),
                        ),
                    ),
                )
            )
        }
    }
}
