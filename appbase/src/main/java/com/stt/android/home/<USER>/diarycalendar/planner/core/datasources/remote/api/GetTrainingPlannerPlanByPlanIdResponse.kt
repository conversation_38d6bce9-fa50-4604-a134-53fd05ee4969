package com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class GetTrainingPlannerPlanByPlanIdResponse(
    val id: String,
    val metaPlanId: String?,
    val name: String,
    val durationWeeks: Int?,
    val startDate: String?,
    val targetDate: String?,
    val status: String,
    val weeklyPrograms: List<RemoteWeeklyProgram>,
    val answers: List<TrainingPlanAnswer>?,
    val questions: List<TrainingPlannerProgramDetailsRemoteQuestion>?,
    val metaPlanHeader: TrainingPlannerProgramRemotePlan?,
    val richInfo: String?,
    val description: String?,
    val eventInfo: TrainingPlannerProgramDetailsRemoteEventInfo?,
)

@JsonClass(generateAdapter = true)
data class RemoteWeeklyProgram(
    val weekNumber: Int,
    val goal: String?,
    val note: String?,
    val weeklyTargets: RemoteWeeklyTargets,
    val plannedWorkouts: List<RemotePlannedWorkouts>
)

@JsonClass(generateAdapter = true)
data class RemoteWeeklyTargets(
    @Json(name = "distance") val distanceInMeters: Int?,
    val duration: Int?,
    val trainingLoad: Int?,
)

@JsonClass(generateAdapter = true)
data class RemotePlannedWorkouts(
    val activityId: Int,
    val duration: Int,
    val estimatedDistanceInMeters: Int,
    val intensityZone: Int?,
    val impacts: List<String>?,
    val name: String,
    val notes: String,
    val trainingDate: String,
    val trainingStressScore: Int,
    val targetPace: TargetMinMax?,
    val targetHeartRate: TargetMinMax?,
    val targetPower: TargetMinMax?,
    val avgSpeed: Double?,
)

@JsonClass(generateAdapter = true)
data class TargetMinMax (
    val min: Double? = null,
    val max: Double? = null,
)

@JsonClass(generateAdapter = true)
data class PlanId(
    val id: String,
)
