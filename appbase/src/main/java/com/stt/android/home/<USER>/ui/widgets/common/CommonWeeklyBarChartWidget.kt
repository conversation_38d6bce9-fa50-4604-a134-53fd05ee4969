package com.stt.android.home.dashboardv2.ui.widgets.common

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import java.time.LocalDate

@Composable
internal fun CommonWeeklyBarChartWidget(
    editMode: Boolean,
    @StringRes headerRes: Int,
    @DrawableRes iconRes: Int,
    @ColorRes colorRes: Int,
    titleText: AnnotatedString,
    subheaderText: String,
    subtitleText: String,
    progresses: List<Float>,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
    iconCircleBg: Boolean = false,
    endDate: LocalDate = LocalDate.now().atStartOfDay().toLocalDate(),
    backgroundColor: Color = Color.Transparent,
    extraColor: Color? = null,
    extraProgresses: List<Float>? = null,
    @DrawableRes subtitleIconRes: Int? = null
) {
    CommonChartWidget(
        editMode = editMode,
        headerRes = headerRes,
        iconRes = iconRes,
        colorRes = colorRes,
        titleText = titleText,
        subheaderText = subheaderText,
        subtitleText = subtitleText,
        subtitleIconRes = subtitleIconRes,
        onClick = onClick,
        onLongClick = onLongClick,
        onRemoveClick = onRemoveClick,
        modifier = modifier,
        iconCircleBg = iconCircleBg,
    ) {
        WidgetWeeklyBarChart(
            color = colorResource(colorRes),
            progresses = progresses,
            backgroundBarColor = backgroundColor,
            extraColor = extraColor,
            extraProgresses = extraProgresses,
            endDate = endDate,
        )
    }
}

@Preview
@Composable
private fun CommonWeeklyBarChartPreview() {
    M3AppTheme {
        CommonWeeklyBarChartWidget(
            editMode = false,
            headerRes = R.string.dashboard_widget_calories_name,
            subheaderText = stringResource(R.string.today),
            colorRes = R.color.dashboard_widget_calories,
            iconRes = R.drawable.ic_dashboard_widget_calories,
            titleText = generateWidgetTitle("123", "kcal"),
            subtitleText = "-177 kcal of target",
            progresses = listOf(0.1f, 0.3f, 0.0f, -1f, 1.0f, 0.3f, 0.66f),
            onClick = null,
            onLongClick = null,
            onRemoveClick = null,
            modifier = Modifier.size(170.dp),
        )
    }
}
