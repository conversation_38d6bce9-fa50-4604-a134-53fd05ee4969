package com.stt.android.home.diary.diarycalendar.bubbles

sealed class DiaryB<PERSON>bleType {
    data class TrainingDayBubbleType(
        val activityGroupsBubbleParameters: List<DiaryBubbleParameters>
    ) : DiaryBubbleType()

    object RestDayBubbleType : DiaryBubbleType()
    object TodayBubbleType : DiaryBubbleType()
    data class FutureDateBubbleType(
        val activityGroupsBubbleParameters: List<DiaryBubbleParameters>
    ) : DiaryBubbleType()
    object NoBubbleType : DiaryBubbleType() // Placeholder bubble for previous month

    @Suppress("RedundantOverride")
    override fun hashCode(): Int {
        // Avoid Epoxy Processor Exception: Attribute does not implement hashCode.
        // Epoxy requires model properties to implement hashCode() and equals(), but in this case
        // using the Kotlin-generated data class for TrainingDayBubble and java.lang.Object.equals
        // for the rest of the bubble types works correctly.
        return super.hashCode()
    }

    @Suppress("RedundantOverride")
    override fun equals(other: Any?): Boolean {
        // Avoid Epoxy Processor Exception: Attribute does not implement hashCode
        return super.equals(other)
    }
}
