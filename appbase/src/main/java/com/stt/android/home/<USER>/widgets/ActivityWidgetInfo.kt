package com.stt.android.home.dashboardv2.widgets

import com.stt.android.home.dashboard.DashboardChartContainer
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.map.selection.MyTracksGranularity

internal data class ActivityWidgetInfo(
    val infoModelFormatter: InfoModelFormatter,
    val granularityType: MyTracksGranularity.Type,
    val chartContainer: DashboardChartContainer,
) : WidgetInfo
