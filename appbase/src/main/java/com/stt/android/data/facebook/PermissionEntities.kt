package com.stt.android.data.facebook

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class PermissionStatus(
    @<PERSON>son(name = "permission") val permission: String,
    @<PERSON><PERSON>(name = "status") val status: String
) {
    companion object {
        const val STATUS_GRANTED = "granted"
    }
}

@JsonClass(generateAdapter = true)
data class PermissionResponse(
    @Json(name = "data") val permissions: List<PermissionStatus>
) {
    companion object {
        const val PERMISSION_FRIENDS = "user_friends"
    }
}
