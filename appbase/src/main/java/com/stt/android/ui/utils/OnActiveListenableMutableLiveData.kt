package com.stt.android.ui.utils

import androidx.lifecycle.MutableLiveData
import com.stt.android.TestOpen

@TestOpen
class OnActiveListenableMutableLiveData<T> : MutableLiveData<T> {

    constructor() : super()

    constructor(value: T) : super(value)

    private var onActiveListener: OnActiveListener? = null

    fun setOnActiveListener(listener: OnActiveListener?) {
        onActiveListener = listener
    }

    override fun onActive() {
        super.onActive()
        onActiveListener?.onActive()
    }

    override fun onInactive() {
        super.onInactive()
        onActiveListener?.onInactive()
    }
}

interface OnActiveListener {
    fun onActive()
    fun onInactive()
}
