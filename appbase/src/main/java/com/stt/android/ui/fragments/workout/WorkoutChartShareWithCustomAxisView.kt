package com.stt.android.ui.fragments.workout

import android.content.Context
import android.graphics.Typeface.BOLD
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.StyleSpan
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import com.stt.android.R
import com.stt.android.databinding.WorkoutChartShareWithCustomAxisViewBinding
import com.stt.android.mapping.InfoModelFormatter
import java.util.Locale
import com.stt.android.core.R as CR

/**
 * A view group containing a chart showing some workout data and custom y-axis with minimum and maximum
 * values. Scales graph lines and text font sizes based on the smaller dimension of the photo where it's used.
 */
class WorkoutChartShareWithCustomAxisView
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), YAxisView {

    private val binding = WorkoutChartShareWithCustomAxisViewBinding.inflate(
        LayoutInflater.from(context),
        this
    )

    var infoModelFormatter: InfoModelFormatter? = null
        set(value) {
            field = value
            if (value != null) {
                binding.graphView.setInfoModelFormatter(value)
            }
        }

    /**
     * Title of the chart
     */
    var title: String = ""
        set(value) {
            field = value
            updateUpperValueText(
                if (binding.graphView.axisRight.isInverted) {
                    minValueText
                } else {
                    maxValueText
                }
            )
        }

    /**
     * String presenting unit of the values shown on chart
     */
    var unit: String = ""
        set(value) {
            field = value
            updateUpperValueText(
                if (binding.graphView.axisRight.isInverted) {
                    minValueText
                } else {
                    maxValueText
                }
            )
            updateLowerValueText(
                if (binding.graphView.axisRight.isInverted) {
                    maxValueText
                } else {
                    minValueText
                }
            )
        }
    private var minValueText: String = ""
        set(value) {
            field = value
            updateMinText()
        }
    private var maxValueText: String = ""
        set(value) {
            field = value
            updateMaxText()
        }

    private val axisColor = ContextCompat.getColor(context, CR.color.sharing_chart_line_color)

    init {
        with(binding) {
            graphView.parentChartView = this@WorkoutChartShareWithCustomAxisView
            graphView.apply {
                minValueText = axisMinValueText
                maxValueText = axisMaxValueText
                bottomOffsetChanged(extraBottomOffset)
                topOffsetChanged(extraTopOffset)
            }

            lowerValueView.setTextColor(axisColor)
            upperValueView.setTextColor(axisColor)
            graphYAxis.setColor(axisColor)
        }
    }

    /**
     * Sets font size for minimum and maximum value in the axis
     * @param unit The desired dimension unit
     * @param value The desired size in the given units.
     */
    fun setFontSize(unit: Int, value: Float) = with(binding) {
        upperValueView.setTextSize(unit, value)
        lowerValueView.setTextSize(unit, value)
    }

    /**
     * Sets line width used when drawing graph line and y-axis line
     * @param lineWidth The desired width in raw pixels
     */
    fun setLineWidth(lineWidth: Float) = with(binding) {
        graphView.lineWidth = getPixelsToDpFactor() * lineWidth
        graphYAxis.setLineWidth(lineWidth)
    }

    /**
     * Sets margin between y-axis minimum/maximum value text and axis line
     * @param margin The desired margin in raw pixels
     */
    fun setTextMargin(margin: Int) {
        ConstraintSet().apply {
            clone(this@WorkoutChartShareWithCustomAxisView)
            setMargin(R.id.graphView, ConstraintSet.BOTTOM, margin)
            setMargin(R.id.graphView, ConstraintSet.TOP, margin)
        }.applyTo(this)
    }

    override fun minValueTextChanged(valueText: String) {
        minValueText = valueText
    }

    override fun maxValueTextChanged(valueText: String) {
        maxValueText = valueText
    }

    override fun bottomOffsetChanged(offset: Float) = with(binding) {
        graphYAxis.bottomOffset = offset
    }

    override fun topOffsetChanged(offset: Float) = with(binding) {
        graphYAxis.topOffset = offset
    }

    private fun getPixelsToDpFactor(): Float =
        (DisplayMetrics.DENSITY_DEFAULT.toFloat() / context.resources.displayMetrics.densityDpi)

    private fun updateMinText() = with(binding) {
        if (graphView.axisRight.isInverted) {
            updateUpperValueText(minValueText)
        } else {
            updateLowerValueText(minValueText)
        }
    }

    private fun updateMaxText() = with(binding) {
        if (graphView.axisRight.isInverted) {
            updateLowerValueText(maxValueText)
        } else {
            updateUpperValueText(maxValueText)
        }
    }

    private fun updateUpperValueText(text: String) = with(binding) {
        val strBuilder = SpannableStringBuilder(
            String.format(
                Locale.getDefault(),
                "%s %s %s",
                title,
                text,
                unit
            )
        )
        strBuilder.setSpan(StyleSpan(BOLD), 0, title.length, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        upperValueView.text = strBuilder
    }

    private fun updateLowerValueText(text: String) = with(binding) {
        lowerValueView.text = String.format(Locale.getDefault(), "%s %s", text, unit)
    }
}
