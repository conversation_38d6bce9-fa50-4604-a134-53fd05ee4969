package com.stt.android.ui.workout.widgets;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.stt.android.R;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.domain.user.LapSettingHelper;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.laps.Laps;
import com.stt.android.utils.STTConstants;
import com.stt.android.workouts.RecordWorkoutService;

import javax.inject.Inject;

public class LapAvgSpeedPaceWidget extends SpeedRelatedWidget {
    public static class SmallLapAvgSpeedPaceWidget extends LapAvgSpeedPaceWidget {
        @Inject
        public SmallLapAvgSpeedPaceWidget(LocalBroadcastManager localBM, UserSettingsController
            userSettingsController) {
            super(localBM, userSettingsController);
        }

        @Override
        protected int getLayoutId() {
            return R.layout.small_tracking_widget_with_unit;
        }
    }

    private Laps.Type selectedLapsType;

    private final BroadcastReceiver lapTypeChangedListener = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            selectedLapsType = (Laps.Type) intent.getSerializableExtra(STTConstants.ExtraKeys.LAP_TYPE);
            onUpdate();
        }
    };

    @Inject
    public LapAvgSpeedPaceWidget(LocalBroadcastManager localBM, UserSettingsController
        userSettingsController) {
        super(localBM, userSettingsController);
    }

    @Override
    protected void onViewInflated() {
        label.setText(R.string.avg_speed_avg_pace_capital);
        super.onViewInflated();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.tracking_widget_with_unit;
    }

    @Override
    protected double getValue() {
        RecordWorkoutService rws = serviceConnection.getRecordWorkoutService();
        return rws != null && selectedLapsType != null ? rws.getOngoingLapAverageSpeed
            (selectedLapsType, userSettingsController.getSettings().getMeasurementUnit()) : 0.0;
    }

    @Override
    protected int getWidgetLabelId() {
        return R.id.label;
    }

    @Override
    public void onStart() {
        super.onStart();
        localBM.registerReceiver(lapTypeChangedListener,
            new IntentFilter(STTConstants.BroadcastActions.LAP_TYPE_CHANGED));
    }

    @Override
    public void onStop() {
        localBM.unregisterReceiver(lapTypeChangedListener);
        super.onStop();
    }

    @Override
    public void onRecordWorkoutServiceBound() {
        super.onRecordWorkoutServiceBound();

        ActivityType activityType = serviceConnection.getRecordWorkoutService().getActivityType();
        selectedLapsType = activityType != null
            ? LapSettingHelper.readLapType(context, activityType.getId()) : Laps.Type.DEFAULT;
    }
}
