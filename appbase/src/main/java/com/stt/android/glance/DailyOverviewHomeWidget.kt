package com.stt.android.glance

import android.annotation.SuppressLint
import android.content.Context
import androidx.annotation.DrawableRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.glance.ColorFilter
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.Image
import androidx.glance.ImageProvider
import androidx.glance.LocalContext
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.SizeMode
import androidx.glance.appwidget.provideContent
import androidx.glance.layout.Alignment
import androidx.glance.layout.Box
import androidx.glance.layout.Column
import androidx.glance.layout.ContentScale
import androidx.glance.layout.Row
import androidx.glance.layout.Spacer
import androidx.glance.layout.fillMaxSize
import androidx.glance.layout.height
import androidx.glance.layout.size
import androidx.glance.unit.ColorProvider
import com.stt.android.FontRefs
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.glance.action.actionStartActivityWithAnalytics
import com.stt.android.glance.data.DailyOverviewHomeWidgetInfo
import com.stt.android.glance.dataloader.DailyOverviewHomeWidgetDataLoader
import com.stt.android.home.HomeActivityNavigator
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.withContext

class DailyOverviewHomeWidget : GlanceAppWidget() {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface WidgetEntryPoint {
        fun dailyOverviewHomeWidgetDataLoader(): DailyOverviewHomeWidgetDataLoader
        fun dispatchers(): CoroutinesDispatchers
        fun homeActivityNavigator(): HomeActivityNavigator
    }

    override val sizeMode = SizeMode.Exact

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        val entryPoint = EntryPointAccessors.fromApplication(
            context.applicationContext,
            WidgetEntryPoint::class.java,
        )
        val info = withContext(entryPoint.dispatchers().io) {
            entryPoint.dailyOverviewHomeWidgetDataLoader().load()
        }
        val intent = entryPoint.homeActivityNavigator().newStartIntentToHome(context)
        provideContent {
            HomeWidgetTheme {
                HomeWidgetSurface(
                    targetWidth = 338f,
                    targetHeight = 158f,
                    paddingHorizontal = 44f,
                    paddingVertical = 26f,
                    onClick = actionStartActivityWithAnalytics(intent, NAME),
                ) { _, _ ->
                    Content(info = info)
                }
            }
        }
    }

    @Composable
    fun Content(
        info: DailyOverviewHomeWidgetInfo,
        modifier: GlanceModifier = GlanceModifier,
    ) {
        Row(
            modifier = modifier.fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            DailyView(
                progress = info.sleepProgress,
                activeTrackColor = Color(0xFF9E6CEC),
                iconRes = R.drawable.ic_dashboard_widget_sleep,
                titleRes = R.string.dashboard_widget_sleep_name,
                summary = info.sleepValue,
            )
            Spacer(modifier = GlanceModifier.defaultWeight())
            DailyView(
                progress = info.caloriesProgress,
                activeTrackColor = Color(0xFFEFB749),
                iconRes = R.drawable.ic_dashboard_widget_calories,
                titleRes = R.string.dashboard_widget_calories_name,
                summary = info.caloriesValue,
            )
            Spacer(modifier = GlanceModifier.defaultWeight())
            DailyView(
                progress = info.stepsProgress,
                activeTrackColor = Color(0xFF66B6FF),
                iconRes = R.drawable.ic_dashboard_widget_steps,
                titleRes = R.string.dashboard_widget_steps_name,
                summary = info.stepsValue,
            )
        }
    }

    @SuppressLint("RestrictedApi")
    @Composable
    fun DailyView(
        progress: Float,
        activeTrackColor: Color,
        @DrawableRes iconRes: Int,
        titleRes: Int,
        summary: String,
        modifier: GlanceModifier = GlanceModifier
    ) {
        val context = LocalContext.current
        Column(
            modifier = modifier,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Box(
                modifier = GlanceModifier.defaultWeight(),
                contentAlignment = Alignment.Center,
            ) {
                CircularProgressBar(
                    progress = progress,
                    activeTrackColor = activeTrackColor,
                    inactiveTrackColor = GlanceTheme.colors.surfaceVariant.getColor(context),
                )
                Image(
                    modifier = GlanceModifier.size(24.scaledDp),
                    provider = ImageProvider(iconRes),
                    contentDescription = null,
                    contentScale = ContentScale.Fit,
                    colorFilter = ColorFilter.tint(ColorProvider(color = activeTrackColor))
                )
            }
            Spacer(modifier = GlanceModifier.height(8.scaledDp))
            WidgetText(
                text = context.getString(titleRes),
                fontRes = FontRefs.DEFAULT_FONT_REF,
                fontSize = 14.scaledDp,
                color = GlanceTheme.colors.onSurface,
            )
            Spacer(modifier = GlanceModifier.height(4.scaledDp))
            WidgetText(
                text = summary,
                fontRes = FontRefs.DEFAULT_FONT_BOLD_REF,
                fontSize = 14.scaledDp,
                color = GlanceTheme.colors.onSurface,
            )
        }
    }

    companion object {
        const val NAME = "DailyOverview"
    }
}
