package com.stt.android.di.job

import com.stt.android.analytics.SyncFirstPairedDeviceEventJob
import com.stt.android.analytics.notificationAnalytics.NotificationsAnalyticsJob
import com.stt.android.analytics.userDetailsAnalytics.UserDetailsAnalyticsJob
import com.stt.android.analytics.usercustomproperty.SetCustomUserPropertyJob
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.backgroundwork.WorkerKey
import com.stt.android.services.FetchStaticConfigFilesWorker
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

/**
 * Module for binding cross-flavor jobs.
 * For flavor specific job bindings, see FlavorJobModule.
 */
@Module
abstract class BaseJobModule {
    @Binds
    @IntoMap
    @WorkerKey(UserDetailsAnalyticsJob::class)
    abstract fun bindUserDetailsAnalyticsJob(userDetailsAnalyticsJobFactory: UserDetailsAnalyticsJob.Factory): CoroutineWorkerAssistedFactory

    @Binds
    @IntoMap
    @WorkerKey(NotificationsAnalyticsJob::class)
    abstract fun bindNotificationsAnalyticsJob(notificationsAnalyticsJobFactory: NotificationsAnalyticsJob.Factory): CoroutineWorkerAssistedFactory

    @Binds
    @IntoMap
    @WorkerKey(FetchStaticConfigFilesWorker::class)
    abstract fun bindFetchStaticConfigFilesFactory(fetchStaticConfigFilesWorker: FetchStaticConfigFilesWorker.Factory): CoroutineWorkerAssistedFactory

    @Binds
    @IntoMap
    @WorkerKey(SetCustomUserPropertyJob::class)
    abstract fun bindSetCustomUserPropertyJob(setCustomUserPropertyJobFactory: SetCustomUserPropertyJob.Factory): CoroutineWorkerAssistedFactory

    @Binds
    @IntoMap
    @WorkerKey(SyncFirstPairedDeviceEventJob::class)
    abstract fun bindSyncFirstPairedDeviceEventJob(syncFirstPairedDeviceEventJob: SyncFirstPairedDeviceEventJob.Factory): CoroutineWorkerAssistedFactory
}
