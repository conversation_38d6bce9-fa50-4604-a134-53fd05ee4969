package com.stt.android.watch

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.R
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

object WatchHelper {
    private const val SUUNTO_7_BLACK_LIME_SKU = "SS050379000"
    private const val SUUNTO_7_BLACK_SKU = "SS050378000"
    private const val SUUNTO_7_GRAPHITE_COPPER_SKU = "SS050382000"
    private const val SUUNTO_7_WHITE_BURGUNDY_SKU = "SS050380000"
    private const val SUUNTO_7_SANDSTONE_ROSEGOLD_SKU = "SS050381000"

    // pike variants
    private const val SUUNTO_7_MATTE_BLACK_TITANIUM_SKU = "SS050568000"
    private const val SUUNTO_7_STONE_GRAY_TITANIUM_SKU = "SS050567000"

    private const val SUUNTO_7_CHINA_BLACK_LIME_SKU = "SS050394000"
    private const val SUUNTO_7_CHINA_BLACK_SKU = "SS050393000"
    private const val SUUNTO_7_CHINA_GRAPHITE_COPPER_SKU = "SS050397000"
    private const val SUUNTO_7_CHINA_WHITE_BURGUNDY_SKU = "SS050395000"
    private const val SUUNTO_7_CHINA_SANDSTONE_ROSEGOLD_SKU = "SS050396000"

    @DrawableRes
    @JvmStatic
    fun getDrawableResIdForSuuntoDeviceType(suuntoDeviceType: SuuntoDeviceType?, sku: String): Int =
        when (suuntoDeviceType) {
            SuuntoDeviceType.SpartanUltra -> R.drawable.watch_activity_spartan_ultra
            SuuntoDeviceType.SpartanSport -> R.drawable.watch_activity_spartan_sport
            SuuntoDeviceType.SpartanSportWristHR -> R.drawable.watch_activity_spartan_sport_whr
            SuuntoDeviceType.SpartanTrainer -> R.drawable.watch_activity_spartan_trainer
            SuuntoDeviceType.SpartanSportWristHRBaro -> R.drawable.watch_activity_baro_whr_watch
            SuuntoDeviceType.Suunto3 -> R.drawable.watch_activity_3
            SuuntoDeviceType.Suunto3Fitness -> R.drawable.watch_activity_3_fitness
            SuuntoDeviceType.Suunto9 -> R.drawable.watch_activity_suunto_9
            SuuntoDeviceType.Suunto9Lima -> R.drawable.watch_activity_suunto_9_lima
            SuuntoDeviceType.Suunto9Peak -> R.drawable.watch_activity_suunto_9_peak
            SuuntoDeviceType.Suunto5 -> R.drawable.watch_activity_suunto_5
            SuuntoDeviceType.Suunto5Peak -> R.drawable.watch_activity_suunto_5_peak
            SuuntoDeviceType.EonCore -> R.drawable.watch_activity_eon_core
            SuuntoDeviceType.EonSteel -> R.drawable.watch_activity_eon_steel
            SuuntoDeviceType.EonSteelBlack -> R.drawable.watch_activity_eon_steel_black
            SuuntoDeviceType.Suunto7 -> provideImageBasedOnSku(sku)
            SuuntoDeviceType.Suunto9PeakPro -> R.drawable.watch_activity_suunto_9_peak_pro
            SuuntoDeviceType.SuuntoVertical -> R.drawable.watch_activity_suunto_vertical
            SuuntoDeviceType.SuuntoOcean -> R.drawable.watch_activity_suunto_ocean
            SuuntoDeviceType.SuuntoRaceS -> R.drawable.watch_activity_suunto_race_s
            SuuntoDeviceType.SuuntoRace -> R.drawable.watch_activity_suunto_phoenix
            SuuntoDeviceType.Ambit3Peak -> R.drawable.watch_activity_ambit3_peak
            SuuntoDeviceType.Ambit3Run -> R.drawable.watch_activity_ambit3_run
            SuuntoDeviceType.Ambit3Vertical -> R.drawable.watch_activity_ambit3_vertical
            SuuntoDeviceType.Ambit3Sport -> R.drawable.watch_activity_ambit3_sport
            SuuntoDeviceType.Traverse -> R.drawable.watch_activity_traverse
            SuuntoDeviceType.TraverseAlpha -> R.drawable.watch_activity_traverse_alpha
            SuuntoDeviceType.SuuntoD5 -> R.drawable.watch_activity_suunto_d5
            SuuntoDeviceType.SuuntoRun -> R.drawable.watch_activity_suunto_run
            SuuntoDeviceType.SuuntoRace2 -> R.drawable.watch_activity_suunto_race2
            SuuntoDeviceType.SuuntoVertical2 -> R.drawable.watch_activity_suunto_vertical2
            SuuntoDeviceType.SuuntoGT -> R.drawable.watch_activity_suunto_ocean // todo: temp use suunto GT resource
            SuuntoDeviceType.Unrecognized,
            null -> R.drawable.watch_activity_ghost_watch
        }

    /**
     * Returns suunto 7 image with right color based on sku or black as default.
     *
     * @param sku String device sku information
     * @return drawable resource int
     */
    private fun provideImageBasedOnSku(sku: String): Int {
        return when (sku) {
            SUUNTO_7_GRAPHITE_COPPER_SKU,
            SUUNTO_7_CHINA_GRAPHITE_COPPER_SKU -> R.drawable.watch_activity_suunto_7_graphite
            SUUNTO_7_SANDSTONE_ROSEGOLD_SKU,
            SUUNTO_7_CHINA_SANDSTONE_ROSEGOLD_SKU -> R.drawable.watch_activity_suunto_7_sandstone
            SUUNTO_7_WHITE_BURGUNDY_SKU,
            SUUNTO_7_CHINA_WHITE_BURGUNDY_SKU -> R.drawable.watch_activity_suunto_7_white
            SUUNTO_7_BLACK_LIME_SKU,
            SUUNTO_7_CHINA_BLACK_LIME_SKU,
            SUUNTO_7_BLACK_SKU,
            SUUNTO_7_CHINA_BLACK_SKU -> R.drawable.watch_activity_suunto_7_black
            SUUNTO_7_MATTE_BLACK_TITANIUM_SKU -> R.drawable.watch_activity_suunto_7_all_black_titanium
            SUUNTO_7_STONE_GRAY_TITANIUM_SKU -> R.drawable.watch_activity_suunto_7_stone_gray_titanium
            else -> R.drawable.watch_activity_suunto_7_black
        }
    }

    @StringRes
    @JvmStatic
    fun getStringResIdForSuuntoDeviceType(suuntoDeviceType: SuuntoDeviceType?): Int =
        when (suuntoDeviceType) {
            SuuntoDeviceType.SpartanUltra -> R.string.watch_activity_title_spartan_ultra
            SuuntoDeviceType.SpartanSport -> R.string.watch_activity_title_spartan_sport
            SuuntoDeviceType.SpartanSportWristHR -> R.string.watch_activity_title_spartan_sport_whr
            SuuntoDeviceType.SpartanTrainer -> R.string.watch_activity_title_spartan_trainer
            SuuntoDeviceType.SpartanSportWristHRBaro -> R.string.watch_activity_title_spartan_sport_whr_baro
            SuuntoDeviceType.Suunto3 -> R.string.watch_activity_title_suunto_3 // fixme use new suunto 3 watch name when available
            SuuntoDeviceType.Suunto3Fitness -> R.string.watch_activity_title_3_fitness
            SuuntoDeviceType.Suunto9 -> R.string.watch_activity_title_suunto_9
            SuuntoDeviceType.Suunto9Lima -> R.string.watch_activity_title_suunto_9_lima
            SuuntoDeviceType.Suunto9Peak -> R.string.watch_activity_title_suunto_9_peak
            SuuntoDeviceType.Suunto5 -> R.string.watch_activity_title_suunto_5
            SuuntoDeviceType.Suunto5Peak -> R.string.watch_activity_title_suunto_5_peak
            SuuntoDeviceType.Suunto7 -> R.string.watch_activity_title_suunto_7
            SuuntoDeviceType.Suunto9PeakPro -> R.string.watch_activity_title_suunto_9_peak_pro
            SuuntoDeviceType.SuuntoVertical -> R.string.watch_activity_title_suunto_vertical
            SuuntoDeviceType.SuuntoOcean -> R.string.watch_activity_title_suunto_seal
            SuuntoDeviceType.SuuntoRaceS -> R.string.watch_activity_title_suunto_race_s
            SuuntoDeviceType.SuuntoRace -> R.string.watch_activity_title_suunto_race
            SuuntoDeviceType.Ambit3Peak -> R.string.watch_activity_title_suunto_ambit3_peak
            SuuntoDeviceType.Ambit3Run -> R.string.watch_activity_title_suunto_ambit3_run
            SuuntoDeviceType.Ambit3Vertical -> R.string.watch_activity_title_suunto_ambit3_vertical
            SuuntoDeviceType.Ambit3Sport -> R.string.watch_activity_title_suunto_ambit3_sport
            SuuntoDeviceType.Traverse -> R.string.watch_activity_title_suunto_traverse
            SuuntoDeviceType.TraverseAlpha -> R.string.watch_activity_title_suunto_traverse_alpha
            SuuntoDeviceType.EonCore -> R.string.watch_activity_title_suunto_eon_core
            SuuntoDeviceType.EonSteel -> R.string.watch_activity_title_suunto_eon_steel
            SuuntoDeviceType.EonSteelBlack -> R.string.watch_activity_title_suunto_eon_steel_black
            SuuntoDeviceType.SuuntoD5 -> R.string.watch_activity_title_suunto_d5
            SuuntoDeviceType.SuuntoRun -> R.string.watch_activity_title_suunto_run
            SuuntoDeviceType.SuuntoRace2 -> R.string.watch_activity_title_suunto_race_2
            SuuntoDeviceType.SuuntoVertical2 -> R.string.watch_activity_title_suunto_vertical_2
            SuuntoDeviceType.SuuntoGT -> R.string.watch_activity_title_suunto_gt
            SuuntoDeviceType.Unrecognized,
            null -> R.string.watch_activity_title
        }

    @JvmStatic
    fun hasOnboarding(deviceType: SuuntoDeviceType): Boolean =
        when (deviceType) {
            SuuntoDeviceType.EonCore,
            SuuntoDeviceType.EonSteel,
            SuuntoDeviceType.EonSteelBlack,
            SuuntoDeviceType.Suunto3,
            SuuntoDeviceType.Suunto3Fitness,
            SuuntoDeviceType.Suunto5,
            SuuntoDeviceType.Suunto5Peak,
            SuuntoDeviceType.Suunto7,
            SuuntoDeviceType.Suunto9,
            SuuntoDeviceType.Suunto9Lima,
            SuuntoDeviceType.Suunto9Peak,
            SuuntoDeviceType.SuuntoD5,
            SuuntoDeviceType.SpartanSport,
            SuuntoDeviceType.SpartanSportWristHR,
            SuuntoDeviceType.SpartanSportWristHRBaro,
            SuuntoDeviceType.SpartanTrainer,
            SuuntoDeviceType.SpartanUltra,
            SuuntoDeviceType.SuuntoVertical,
            SuuntoDeviceType.Suunto9PeakPro,
            SuuntoDeviceType.SuuntoRace,
            SuuntoDeviceType.SuuntoOcean,
            SuuntoDeviceType.SuuntoRaceS,
            SuuntoDeviceType.SuuntoRun,
            SuuntoDeviceType.SuuntoRace2,
            SuuntoDeviceType.SuuntoGT,
            SuuntoDeviceType.SuuntoVertical2 -> true
            else -> false
        }

    @JvmStatic
    fun hasUserGuide(deviceType: SuuntoDeviceType): Boolean =
        getUserGuideLinkForSuuntoDeviceType(deviceType) != null

    @StringRes
    @JvmStatic
    fun getUserGuideLinkForSuuntoDeviceType(suuntoDeviceType: SuuntoDeviceType): Int? =
        when (suuntoDeviceType) {
            SuuntoDeviceType.Suunto9,
            SuuntoDeviceType.Suunto9Lima -> R.string.user_guide_suunto_9
            SuuntoDeviceType.Suunto9Peak -> R.string.user_guide_suunto_9_peak
            SuuntoDeviceType.Suunto5 -> R.string.user_guide_suunto_5
            SuuntoDeviceType.Suunto5Peak -> R.string.user_guide_suunto_5_peak
            SuuntoDeviceType.SpartanUltra -> R.string.user_guide_spartan_ultra
            SuuntoDeviceType.SpartanSport -> R.string.user_guide_spartan_sport
            SuuntoDeviceType.SpartanSportWristHR -> R.string.user_guide_spartan_sport_wrist_hr
            SuuntoDeviceType.SpartanTrainer -> R.string.user_guide_spartan_trainer
            SuuntoDeviceType.Suunto3 -> R.string.user_guide_suunto_3
            SuuntoDeviceType.Suunto3Fitness -> R.string.user_guide_suunto_3_fitness
            SuuntoDeviceType.SpartanSportWristHRBaro -> R.string.user_guide_spartan_sport_wrist_hr_baro
            SuuntoDeviceType.Suunto7 -> R.string.user_guide_suunto_7
            SuuntoDeviceType.Suunto9PeakPro -> R.string.user_guide_suunto_9_peak_pro
            SuuntoDeviceType.SuuntoVertical -> R.string.user_guide_suunto_vertical
            SuuntoDeviceType.SuuntoOcean -> R.string.user_guide_suunto_ocean
            SuuntoDeviceType.SuuntoRaceS -> R.string.user_guide_suunto_race_s
            SuuntoDeviceType.SuuntoRace -> R.string.user_guide_suunto_race
            SuuntoDeviceType.Ambit3Peak -> R.string.user_guide_ambit_3_peak
            SuuntoDeviceType.Ambit3Sport -> R.string.user_guide_ambit_3_sport
            SuuntoDeviceType.Ambit3Run -> R.string.user_guide_ambit_3_run
            SuuntoDeviceType.Ambit3Vertical -> R.string.user_guide_ambit_3_vertical
            SuuntoDeviceType.Traverse -> R.string.user_guide_traverse
            SuuntoDeviceType.TraverseAlpha -> R.string.user_guide_traverse_alpha
            SuuntoDeviceType.EonSteel -> R.string.user_guide_eon_steel
            SuuntoDeviceType.EonSteelBlack -> R.string.user_guide_eon_steel_black
            SuuntoDeviceType.SuuntoGT -> R.string.user_guide_eon_steel_black // todo: use correct link for GT
            SuuntoDeviceType.EonCore -> R.string.user_guide_eon_core
            SuuntoDeviceType.SuuntoD5 -> R.string.user_guide_suunto_d5
            SuuntoDeviceType.SuuntoRun -> R.string.user_guide_suunto_run
            SuuntoDeviceType.SuuntoRace2 -> R.string.user_guide_suunto_race // todo: temp use race resource
            SuuntoDeviceType.SuuntoVertical2 -> R.string.user_guide_suunto_vertical // todo: temp use vertical resource
            SuuntoDeviceType.Unrecognized -> null
        }
}
