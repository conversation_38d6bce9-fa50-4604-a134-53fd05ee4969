package com.stt.android.diveplanner.ui.diveprofile.gases

import androidx.compose.runtime.Immutable
import com.stt.android.diveplanner.ui.common.Configuration
import com.stt.android.utils.DiveGasNameHelper

@Immutable
data class GasUiState(
    val id: String,
    val mixtureUiState: MixtureUiState,
    val cylinderUiState: CylinderUiState,
    val preferencesUiState: PreferencesUiState,
    val isSelected: Boolean,
    val isEnabled: Boolean
) {
    val isBottomGas: Boolean =
        !preferencesUiState.useAsDecompressionGasEnabled &&
            !preferencesUiState.useAsBailoutGasEnabled

    val isDecompressionGas: Boolean =
        preferencesUiState.useAsDecompressionGasEnabled &&
            !preferencesUiState.useAsBailoutGasEnabled

    val isBailoutGas: Boolean = preferencesUiState.useAsBailoutGasEnabled

    val gasName: String
        get() = DiveGasNameHelper.extractNameFrom(mixtureUiState.o2.div(100f), mixtureUiState.he.div(100f))
}

@Immutable
data class MixtureUiState(
    val o2: Int,
    val he: Int
)

@Immutable
data class CylinderUiState(
    val size: Int,
    val pressure: Int
)

@Immutable
data class PreferencesUiState(
    val configuration: Configuration,
    val useAsDecompressionGasEnabled: Boolean,
    val partialPressureLimit: Float,
    val useAsBailoutGasEnabled: Boolean,
    val endLimit: Int
) {
    val canBeUsedAsDecompressionGas: Boolean
        get() = configuration == Configuration.OpenCircuit

    val hasPartialPressureLimit: Boolean
        get() = configuration == Configuration.OpenCircuit

    val canBeUsedAsBailoutGas: Boolean
        get() = configuration == Configuration.ClosedCircuit

    val mod: Int
        get() = 56 // TODO: How to calculate this?
}
