package com.stt.android.diveplanner.ui

import app.cash.turbine.test
import com.google.common.truth.Truth.assertThat
import com.stt.android.controllers.UserSettingsController
import com.stt.android.diveplanner.data.DiveProfileGasesRepository
import com.stt.android.diveplanner.data.DiveProfileLevelsRepository
import com.stt.android.diveplanner.data.DiveProfileSettingsRepository
import com.stt.android.diveplanner.ui.common.Algorithm
import com.stt.android.diveplanner.ui.common.Configuration
import com.stt.android.diveplanner.ui.diveprofile.gases.DiveProfileGasesUiState
import com.stt.android.diveplanner.ui.diveprofile.settings.DiveProfileSettingsUiState
import com.stt.android.diveplanner.ui.diveprofile.settings.NonSuuntoAlgorithmUiState
import com.stt.android.diveplanner.ui.diveprofile.settings.SuuntoAlgorithmUiState
import com.stt.android.diveplanner.ui.utils.DataFaker
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.UserSettings
import com.stt.android.testutils.NewCoroutinesTestRule
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class DivePlannerViewModelTest {

    @Rule
    @JvmField
    val coroutinesTestRule = NewCoroutinesTestRule()

    @Mock
    private lateinit var userSettings: UserSettings

    @Mock
    private lateinit var userSettingsController: UserSettingsController

    private lateinit var viewModel: DivePlannerViewModel

    @Before
    fun setup() {
        whenever(userSettingsController.settings).thenReturn(userSettings)
        whenever(userSettingsController.settings.measurementUnit).thenReturn(MeasurementUnit.METRIC)
        viewModel = DivePlannerViewModel(
            userSettingsController = userSettingsController,
            diveProfileLevelsDataSource = DiveProfileLevelsRepository(), // TODO: Replace with fake once the repository is no longer the data holder
            diveProfileGasesDataSource = DiveProfileGasesRepository(),
            diveProfileSettingsDataSource = DiveProfileSettingsRepository(userSettingsController),
        )
    }

    @Test
    fun `compare algorithm is always null by default`() {
        assertThat(viewModel.diveProfileSettingsUiState.value.algorithm.compareAlgorithm).isNull()
        assertThat(viewModel.diveProfileSettingsUiState.value.algorithm.isCompareAlgorithmsEnabled).isFalse()
    }

    @Test
    fun `enable deep stop must be enabled if default algorithm is a Suunto algo`() {
        val primaryAlgorithm =
            viewModel.diveProfileSettingsUiState.value.algorithm.primaryAlgorithm as SuuntoAlgorithmUiState
        assertThat(primaryAlgorithm.isEnableDeepStopsEnabled).isTrue()
    }

    @Test
    fun `set algorithm must change the UI state used`() = runTest {
        viewModel.diveProfileSettingsUiState.test {
            viewModel.setAlgorithm(Algorithm.NonSuuntoAlgorithm.Buhlmann16GF, isPrimary = true)
            (expectMostRecentItem().algorithm.primaryAlgorithm as NonSuuntoAlgorithmUiState).let {
                assertThat(it.gradientFactorHigh).isEqualTo(70)
                assertThat(it.gradientFactorLow).isEqualTo(30)
            }

            viewModel.setAlgorithm(Algorithm.SuuntoAlgorithm.SuuntoFusedRGBM2, isPrimary = true)
            (expectMostRecentItem().algorithm.primaryAlgorithm as SuuntoAlgorithmUiState).let {
                assertThat(it.isEnableDeepStopsEnabled).isTrue()
            }
        }
    }

    @Test
    fun `changing Suunto algorithm to another suunto must keep same old common values like enable deep stops`() =
        runTest {
            viewModel.diveProfileSettingsUiState.test {
                viewModel.setAlgorithm(Algorithm.SuuntoAlgorithm.SuuntoFusedRGBM, isPrimary = true)
                viewModel.setPersonalSettings(
                    Algorithm.SuuntoAlgorithm.PersonalSetting.IdealConditionsExcellentFitness,
                    isPrimary = true
                )
                (expectMostRecentItem().algorithm.primaryAlgorithm as SuuntoAlgorithmUiState).let {
                    assertThat(it.isEnableDeepStopsEnabled).isTrue()
                    assertThat(it.personalSetting).isInstanceOf(Algorithm.SuuntoAlgorithm.PersonalSetting.IdealConditionsExcellentFitness::class.java)
                }
                viewModel.setEnableDeepStops(false, isPrimary = true)
                viewModel.setPersonalSettings(
                    Algorithm.SuuntoAlgorithm.PersonalSetting.SomeRisk,
                    isPrimary = true
                )
                (expectMostRecentItem().algorithm.primaryAlgorithm as SuuntoAlgorithmUiState).let {
                    assertThat(it.isEnableDeepStopsEnabled).isFalse()
                }
                viewModel.setAlgorithm(Algorithm.SuuntoAlgorithm.SuuntoRGBM, isPrimary = true)
                (expectMostRecentItem().algorithm.primaryAlgorithm as SuuntoAlgorithmUiState).let {
                    assertThat(it.isEnableDeepStopsEnabled).isFalse()
                    assertThat(it.personalSetting).isInstanceOf(Algorithm.SuuntoAlgorithm.PersonalSetting.SomeRisk::class.java)
                }
            }
        }

    @Test
    fun `update gradient should work correctly`() = runTest {
        viewModel.diveProfileSettingsUiState.test {
            viewModel.setAlgorithm(Algorithm.NonSuuntoAlgorithm.Buhlmann16GF, isPrimary = true)
            viewModel.setGradientFactorLow(100, isPrimary = true)
            viewModel.setGradientFactorHigh(150, isPrimary = true)
            val finalItem =
                expectMostRecentItem().algorithm.primaryAlgorithm as NonSuuntoAlgorithmUiState
            assertThat(finalItem.gradientFactorLow).isEqualTo(100)
            assertThat(finalItem.gradientFactorHigh).isEqualTo(150)
        }
    }

    @Test
    fun `the user can not compare the original algorithm itself`() = runTest {
        viewModel.diveProfileSettingsUiState.test {
            val algorithm = expectMostRecentItem().algorithm
            assertThat(algorithm.possibleValuesPrimaryAlgorithm).containsExactlyElementsIn(Algorithm.values())
            assertThat(algorithm.possibleValuesCompareAlgorithm).containsExactlyElementsIn(
                Algorithm.values()
                    .filter { it != algorithm.primaryAlgorithm.selectedAlgorithm }
            )
        }
    }

    @Test
    fun `selected algorithm to compare with should be removed from primary algorithms list`() =
        runTest {
            viewModel.diveProfileSettingsUiState.test {
                viewModel.setAlgorithm(Algorithm.SuuntoAlgorithm.SuuntoFusedRGBM2, isPrimary = true)
                assertThat(expectMostRecentItem().algorithm.possibleValuesPrimaryAlgorithm).containsExactlyElementsIn(
                    Algorithm.values()
                )

                viewModel.setAlgorithm(Algorithm.SuuntoAlgorithm.SuuntoTechRGBM, isPrimary = false)
                val settingsUiState = expectMostRecentItem()
                assertThat(settingsUiState.algorithm.possibleValuesPrimaryAlgorithm).containsExactlyElementsIn(
                    Algorithm.values().minus(Algorithm.SuuntoAlgorithm.SuuntoTechRGBM)
                )
                assertThat(settingsUiState.algorithm.possibleValuesCompareAlgorithm).containsExactlyElementsIn(
                    Algorithm.values().minus(Algorithm.SuuntoAlgorithm.SuuntoFusedRGBM2)
                )
            }
        }

    @Test
    fun `set points are only supported in CCR mode`() = runTest {
        viewModel.diveProfileSettingsUiState.test {
            viewModel.setConfiguration(Configuration.OpenCircuit)
            var uiState: DiveProfileSettingsUiState = expectMostRecentItem()
            assertThat(uiState.configuration == Configuration.OpenCircuit).isTrue()
            assertThat(uiState.supportsSetPoints).isFalse()

            viewModel.setConfiguration(Configuration.ClosedCircuit)
            uiState = expectMostRecentItem()
            assertThat(uiState.configuration == Configuration.ClosedCircuit).isTrue()
            assertThat(uiState.supportsSetPoints).isTrue()
        }
    }

    @Test
    fun `the added gas must exported to ui in the right spot`() = runTest {
        val toBeAddedAsBottomGas = DataFaker.generateGasUiState(
            useAsDecompressionGasEnabled = false,
            useAsBailoutGasEnabled = false
        )
        val toBeAddedAsDecompressionGas = DataFaker.generateGasUiState(
            useAsDecompressionGasEnabled = true,
            useAsBailoutGasEnabled = false
        )
        val toBeAddedAsBailoutGas = DataFaker.generateGasUiState(
            useAsDecompressionGasEnabled = false,
            useAsBailoutGasEnabled = true
        )

        viewModel.diveProfileGasesUiState.test {
            viewModel.addGas(toBeAddedAsBottomGas)
            viewModel.addGas(toBeAddedAsDecompressionGas)
            viewModel.addGas(toBeAddedAsBailoutGas)
            val uiState: DiveProfileGasesUiState = expectMostRecentItem()
            assertThat(uiState.gases).containsExactly(
                toBeAddedAsBottomGas,
                toBeAddedAsDecompressionGas,
                toBeAddedAsBailoutGas
            )
            assertThat(uiState.bottomGases).containsExactly(toBeAddedAsBottomGas)
            assertThat(uiState.decompressionGases).containsExactly(toBeAddedAsDecompressionGas)
            assertThat(uiState.bailoutGases).containsExactly(toBeAddedAsBailoutGas)
        }
    }
}
