package com.stt.android.data.trenddata

import com.stt.android.data.TimeUtils
import com.stt.android.data.source.local.trenddata.LocalTrendData
import com.stt.android.domain.trenddata.TrendData
import com.suunto.algorithms.data.Energy.Companion.joules

val trendDataMock = TrendData(
    serial = "",
    timestamp = 1321231,
    energy = 34.0.joules,
    steps = 23,
    hr = 1.01f,
    hrMin = 1.00f,
    hrMax = 1.02f,
    spo2 = 0.98f,
    altitude = 101f,
    hrv = 12,
    timeISO8601 = TimeUtils.epochToZonedDateTime(1321231)
)

val localTrendDataMock = LocalTrendData(
    serial = "",
    timestampSeconds = 1321231,
    energy = 23f,
    steps = 23,
    heartrate = 1.01f,
    hrMin = 1.00f,
    hrMax = 1.02f,
    spo2 = 0.98f,
    altitude = 101f,
    hrv = 12
)
