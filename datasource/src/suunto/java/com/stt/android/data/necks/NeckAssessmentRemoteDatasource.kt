package com.stt.android.data.necks

import com.squareup.moshi.Moshi
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory.getStOkHttpConfig
import com.stt.android.remote.di.RestApiFactory.buildRestApi
import com.stt.android.remote.headsetneck.NeckAssessmentApi
import com.stt.android.remote.otp.GenerateOTPUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import javax.inject.Inject

@Module
@InstallIn(SingletonComponent::class)
abstract class NeckAssessmentRemoteModule {
    companion object {
        @Provides
        fun provideNeckAssessmentApi(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: <PERSON><PERSON>?
        ): NeckAssessmentApi {
            return buildRestApi(
                sharedClient,
                baseUrl,
                NeckAssessmentApi::class.java,
                getStOkHttpConfig(authProvider, userAgent),
                moshi!!
            )
        }
    }
}

class NeckAssessmentRemoteDatasource @Inject constructor(
    private val neckAssessmentApi: NeckAssessmentApi,
    private val generateOTPUseCase: GenerateOTPUseCase
) {

    suspend fun saveNeckAssessment(neckLocalAssessmentValues: NeckLocalAssessmentValues): NeckLocalAssessmentValues {
        val totp = generateOTPUseCase.generateTOTP()
        return neckAssessmentApi.saveAssessmentValues(totp, neckLocalAssessmentValues.toRemote())
            .payloadOrThrow().toLocal()
    }

    suspend fun getNecksAssessmentValues(): List<NeckLocalAssessmentValues> {
        return neckAssessmentApi.getNeckAssessmentValues().payloadOrThrow().map { it.toLocal() }
    }

    suspend fun deleteNeckAssessment(id: String): Boolean {
        return neckAssessmentApi.deleteNeckAssessment(id).payloadOrThrow()
    }
}
