package com.stt.android.data.usersettings

import android.app.Application
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.ListenableWorker
import androidx.work.testing.TestListenableWorkerBuilder
import com.google.common.truth.Truth.assertThat
import com.stt.android.data.TestWorkerFactory
import com.stt.android.data.otp.FakeOTPGenerator
import com.stt.android.data.session.CurrentUser
import com.stt.android.remote.usersettings.FcmTokenRegistrationRemoteApi
import com.stt.android.remote.usersettings.UserSettingsRemoteApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.spy
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@RunWith(AndroidJUnit4::class)
class UserSettingsRemoteSyncJobTest {

    private val currentUser = mock<CurrentUser>()
    private val userSettingsDataSource = spy<FakeUserSettingsDataSource>()
    private val fcmTokenRegistrationRestApi = spy<FakeFcmTokenRegistrationRestApi>()
    private val userSettingsRestApi = spy<FakeUserSettingsRestApi>()
    private val userNotificationsSettingsRestApi = spy<FakeUserNotificationsSettingsRestApi>()
    private val fcmTokenDataSource = spy<FakeFcmTokenDataSource>()

    private lateinit var worker: CoroutineWorker

    private lateinit var userSettingsRemoteApi: UserSettingsRemoteApi

    @Before
    fun setup() {
        val context = ApplicationProvider.getApplicationContext<Application>()
        userSettingsRemoteApi = spy(UserSettingsRemoteApi(userSettingsRestApi, FakeOTPGenerator()))
        worker = TestListenableWorkerBuilder<UserSettingsRemoteSyncJob>(context)
            .setInputData(
                Data.Builder()
                    .putBoolean(UserSettingsRemoteSyncJob.KEY_FORCE_SETTINGS_FETCH, true)
                    .build()
            )
            .setWorkerFactory(
                TestWorkerFactory(
                    UserSettingsRemoteSyncJob.Factory(
                        userSettingsDataSource,
                        currentUser,
                        FcmTokenSynchronizer(
                            FcmTokenRegistrationRemoteApi(fcmTokenRegistrationRestApi),
                            fcmTokenDataSource,
                            userSettingsDataSource
                        ),
                        UserSettingsSynchronizer(
                            userSettingsDataSource,
                            userSettingsRemoteApi,
                            fcmTokenDataSource,
                            userNotificationsSettingsRestApi
                        ),
                        UserSettingsSyncMonitor(),
                    )
                )
            ).build()
    }

    @Test
    fun userNotLoggedInJobShouldAbortWithSuccessResult() = runTest {
        whenever(currentUser.isLoggedIn()).thenReturn(false)

        assertThat(worker.doWork()).isEqualTo(ListenableWorker.Result.success())
    }

    @Test
    fun userIsLoggedInShouldRegisterFcmTokenAndMarkJobSuccess() = runTest {
        whenever(currentUser.isLoggedIn()).thenReturn(true)

        assertThat(worker.doWork()).isEqualTo(ListenableWorker.Result.success())
        verify(fcmTokenRegistrationRestApi).register(any())
    }

    @Test
    fun userSettingsChangedLocallySavesRemotelyAndMarkJobSuccess() = runTest {
        whenever(currentUser.isLoggedIn()).thenReturn(true)
        whenever(userSettingsDataSource.isLocallyChanged()).thenReturn(true)

        assertThat(worker.doWork()).isEqualTo(ListenableWorker.Result.success())
        verify(
            userSettingsRemoteApi,
            times(2)
        ).saveUserSettings(any()) // Should be times(2), since FakeUserSettingsRestApi contains UserSettings with tagAutomation as null and it should set it to default value and sync it to backend
        verify(userNotificationsSettingsRestApi).saveUserNotificationsSettings(any())
    }

    @Test
    fun userIsLoggedInShouldFetchSettingsFromRemoteAndMarkJobSuccess() = runTest {
        whenever(currentUser.isLoggedIn()).thenReturn(true)

        assertThat(worker.doWork()).isEqualTo(ListenableWorker.Result.success())
        verify(userNotificationsSettingsRestApi).fetchUserNotificationsSettings()
        verify(userSettingsRemoteApi).fetchUserSettings()
    }
}
