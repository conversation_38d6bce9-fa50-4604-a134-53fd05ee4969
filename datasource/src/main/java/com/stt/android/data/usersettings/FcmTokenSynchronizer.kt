package com.stt.android.data.usersettings

import com.stt.android.data.notifications.FcmTokenDataSource
import com.stt.android.domain.user.UserSettingsDataSource
import com.stt.android.remote.usersettings.FcmTokenRegistrationRemoteApi
import timber.log.Timber
import javax.inject.Inject

class FcmTokenSynchronizer
@Inject constructor(
    private val fcmTokenRegistrationRemoteApi: FcmTokenRegistrationRemoteApi,
    private val fcmTokenDataSource: FcmTokenDataSource,
    private val userSettingsDataSource: UserSettingsDataSource
) {
    suspend fun saveFcmToken() {
        val token = fcmTokenDataSource.fetch()
        if (token != null) {
            val synced = fcmTokenDataSource.getFcmTokenSyncedStatus(token)
            if (!synced) {
                fcmTokenRegistrationRemoteApi.register(token, userSettingsDataSource.getPushApp())
                Timber.d("Fcm token already synced to backend")
                // mark sync successful
                fcmTokenDataSource.setFcmTokenAsSynced(token)
            } else {
                Timber.d("Fcm token already synced to backend - skipping save")
            }
        }
    }

    suspend fun removeFcmToken() {
        val token = fcmTokenDataSource.fetch()
        if (token != null) {
            fcmTokenRegistrationRemoteApi.unregister(token, userSettingsDataSource.getPushApp())
            fcmTokenDataSource.cleanCached()
        }
    }
}
