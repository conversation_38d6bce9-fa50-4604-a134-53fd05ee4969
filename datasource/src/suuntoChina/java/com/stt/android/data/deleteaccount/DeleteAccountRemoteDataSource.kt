package com.stt.android.data.deleteaccount

import com.stt.android.remote.deleteaccount.DeleteAccountRemoteApi
import javax.inject.Inject

class DeleteAccountRemoteDataSource @Inject constructor(
    private val deleteAccountRemoteApi: DeleteAccountRemoteApi,
) {
    suspend fun sendVerificationCode(phoneNumber: String) =
        deleteAccountRemoteApi.sendVerificationCode(phoneNumber)

    suspend fun verifyVerificationCode(phoneNumber: String, verificationCode: String): String {
        return deleteAccountRemoteApi.verifyVerificationCode(phoneNumber, verificationCode)
    }

    suspend fun deleteAccount(token: String): Boolean {
        return deleteAccountRemoteApi.deleteAccount(token)
    }
}
