package com.suunto.soa.ble.operation

import com.suunto.soa.ble.control.InternalController
import com.suunto.soa.ble.response.GetFileInfoResponse
import com.suunto.soa.ble.service.BleManager
import com.suunto.soa.command.Request
import com.suunto.soa.state.DeviceRunning

internal class GetFileInfoOperation(
    internalController: InternalController,
    bleManager: BleManager,
    request: Request,
    deviceRunning: DeviceRunning
) : RequestOperation<GetFileInfoResponse>(internalController, bleManager, request, deviceRunning) {

    override fun buildResponse(bytes: ByteArray): GetFileInfoResponse {
        return GetFileInfoResponse(bytes)
    }
}
