package com.suunto.soa.ble.response

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

fun getButtonFunction(functionCode: Int): ButtonFunction {
    return ButtonFunction.entries
        .firstOrNull { it.code != BUTTON_INVALID_CODE && it.code == functionCode }
        ?: ButtonFunction.EMPTY
}

fun getButtonShortKey(shortcutKeyCode: Int): ButtonShortcutKey {
    return ButtonShortcutKey.entries
        .firstOrNull { it.code != BUTTON_INVALID_CODE && it.code == shortcutKeyCode }
        ?: ButtonShortcutKey.EMPTY
}

data class CustomizationButton(
    val shortcutKey: ButtonShortcutKey,
    val buttonFunction: ButtonFunction
) {
    fun getButtonFunctionCode(): Int? {
        return if (buttonFunction.code == BUTTON_INVALID_CODE) {
            null
        } else {
            buttonFunction.code
        }
    }

    fun getButtonShortKeyCode(): Int {
        return if (shortcutKey.code == BUTTON_INVALID_CODE) {
            ButtonShortcutKey.EMPTY.code
        } else {
            shortcutKey.code
        }
    }
}

enum class ButtonFunction(val code: Int) {
    NEXT_SONG(0x00),
    LAST_SONG(0x01),
    SWITCH_SOUND_MODE(0x02),
    HEAD_MOVE_CONTROL(0x03),
    ENABLE_SPORT_TRACKING(0x04),
    SWITCH_MUSIC_MODE(0x05),
    ACTIVE_VOICE_ASSISTANT(0x06),
    CHANGE_INTERNAL_PLAYLIST(0x07),
    SWITCH_PLAYBACK_ORDER(0x08),
    NECK_FATIGUE_ALERT_ON_OFF(0x09),
    LED_MODE_ON_OFF(0x0A),
    POWER_ON_OFF(BUTTON_INVALID_CODE),
    VOLUME_ADJUSTMENT(BUTTON_INVALID_CODE),
    DUAL_DEVICE_CONNECTION(BUTTON_INVALID_CODE),
    MUSIC_PLAY_PAUSE(BUTTON_INVALID_CODE),
    PHONE_CALL_CONTROL(BUTTON_INVALID_CODE),
    REJECT_CALL(BUTTON_INVALID_CODE),
    REST_SETTING(BUTTON_INVALID_CODE),
    START_BLE_PAIR(BUTTON_INVALID_CODE),
    EMPTY(0xFFFF),
}

@Parcelize
enum class ButtonShortcutKey(val code: Int) : Parcelable {
    EMPTY(0x00),
    DOUBLE_PRESS_MFB(0x01),
    TRIPE_PRESS_MFB(0x02),
    HOLD_DOWN_MFB_3S(0x03),
    HOLD_DOWN_REDUCE_3S(0x04),
    HOLD_DOWN_PLUS_AND_REDUCE_3S(0x05),
    HOLD_DOWN_MFB_AND_PLUS_3S(0x06),
    HOLD_DOWN_MFB_AND_REDUCE_3S(0x07),
    HOLD_DOWN_PLUS_3S(BUTTON_INVALID_CODE),
    PRESS_PLUS_OR_REDUCE(BUTTON_INVALID_CODE),
    HOLD_DOWN_MFB_5S(BUTTON_INVALID_CODE),
    PRESS_MFB_ONCE(BUTTON_INVALID_CODE),
    PRESS_AND_HOLD_MFB(BUTTON_INVALID_CODE),
}

const val BUTTON_INVALID_CODE = 0xF0F0
