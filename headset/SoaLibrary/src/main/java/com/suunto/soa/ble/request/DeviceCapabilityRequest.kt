package com.suunto.soa.ble.request

import com.suunto.soa.DataConstants.CMD_REQUEST_RESPONSE
import com.suunto.soa.command.OpCodeSn
import com.suunto.soa.command.Request

internal class DeviceCapabilityRequest : Request(
    CMD_REQUEST_RESPONSE,
    GET_DEVICE_CAPABILITY,
    OpCodeSn.nextOpCodeSn(),
    byteArrayOf()
) {
    companion object {
        const val GET_DEVICE_CAPABILITY = 0x11.toByte()
    }
}
