package com.suunto.soa.ble.client

import com.suunto.soa.ble.control.attr.SwimStatus
import com.suunto.soa.data.FileInfo
import com.suunto.soa.data.SoaPoolDistance
import com.suunto.soa.data.SwimData

interface SwimSettingFeatures {

    suspend fun isDeviceSwimming(): Boolean

    suspend fun setPoolDistance(poolDistance: SoaPoolDistance): Boolean

    suspend fun getPoolDistance(): SoaPoolDistance?

    suspend fun getDeviceSwimStatus(): SwimStatus?

    suspend fun syncSwimData(isPoolSwimSupports: Boolean): List<SwimData>?

    suspend fun clearSwimData(isPoolSwimSupports: Boolean): Boolean

    suspend fun getFileInfo(): FileInfo
}
