package com.suunto.headset.repository

import com.stt.android.data.jumps.JumpAssessmentLocalValue
import com.stt.android.data.jumps.JumpAssessmentRemoteDatasource
import javax.inject.Inject

class JumpAssessmentRepository @Inject constructor(
    private val jumpAssessmentRemoteDatasource: JumpAssessmentRemoteDatasource
) {

    suspend fun saveJumpAssessment(assessmentValue: JumpAssessmentLocalValue): JumpAssessmentLocalValue {
        return jumpAssessmentRemoteDatasource.saveJumpAssessment(assessmentValue)
    }

    suspend fun getStandardJumpAssessment(): JumpAssessmentLocalValue? {
        return jumpAssessmentRemoteDatasource.getStandardJumpAssessment()
    }

    suspend fun getHistoryJumpAssessments(): List<JumpAssessmentLocalValue> {
        return jumpAssessmentRemoteDatasource.getHistoryJumpAssessments()
    }

    suspend fun deleteJumpData(jumpId: String) {
        jumpAssessmentRemoteDatasource.deleteJumpData(jumpId)
    }
}
