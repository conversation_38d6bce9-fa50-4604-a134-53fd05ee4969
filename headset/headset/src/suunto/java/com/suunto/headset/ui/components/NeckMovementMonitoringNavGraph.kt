package com.suunto.headset.ui.components

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.core.net.toUri
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.stt.android.analytics.AnalyticsPropertyValue.HeadphoneFeaturePageName
import com.suunto.headset.HeadsetConstant.SU07_VIDEO_ANIMATION_PATH
import com.suunto.headset.HeadsetConstant.VIDEO_ANIMATION_NECK_CALIBRATION
import com.suunto.headset.model.NeckAssessmentAdvice
import com.suunto.headset.ui.components.TransitionDuration.TRANSITION_DURATION_MS
import com.suunto.headset.ui.components.neck.AssessmentHistoryReportScreen
import com.suunto.headset.ui.components.neck.AssessmentProcessScreen
import com.suunto.headset.ui.components.neck.AssessmentReportDetailScreen
import com.suunto.headset.ui.components.neck.AssessmentResultScreen
import com.suunto.headset.ui.components.neck.CalibrationScreen
import com.suunto.headset.ui.components.neck.MobilityAssessmentScreen
import com.suunto.headset.ui.components.neck.NeckHealthGuideScreen
import com.suunto.headset.viewmodel.NeckMovementMonitoringViewModel
import kotlinx.collections.immutable.toImmutableList

private object NavDestinations {
    const val ASSESSMENT = "assessment"
    const val CALIBRATION = "calibration"
    const val ASSESSMENT_PROCESS = "assessment_process"
    const val ASSESSMENT_RESULT = "assessment_result"
    const val HISTORY_REPORT = "history_report"
    const val ASSESSMENT_REPORT_DETAIL = "assessment_report_detail"
    const val ASSESSMENT_HEALTH_GUIDE = "assessment_health_guide"
}

@Composable
fun NeckMovementMonitoringNavGraph(
    neckMovementMonitoringVM: NeckMovementMonitoringViewModel,
    exit: () -> Unit,
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController()
) {
    NavHost(
        modifier = modifier,
        navController = navController,
        startDestination = NavDestinations.ASSESSMENT,
        enterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                tween(TRANSITION_DURATION_MS)
            )
        },
        exitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                tween(TRANSITION_DURATION_MS)
            )
        },
        popEnterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                tween(TRANSITION_DURATION_MS)
            )
        },
        popExitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                tween(TRANSITION_DURATION_MS)
            )
        }
    ) {
        composable(NavDestinations.ASSESSMENT) {
            MobilityAssessmentScreen(
                onBackClick = exit,
                onStartAssessment = {
                    if (!neckMovementMonitoringVM.getShowGuide()) {
                        // assessing self
                        neckMovementMonitoringVM.setAssessmentForOther(false)
                        neckMovementMonitoringVM.resetCalibrationResult()
                        navController.navigate(NavDestinations.CALIBRATION)
                    }
                },
                onStartAssessmentForOtherAssessor = {
                    neckMovementMonitoringVM.setAssessmentForOther(true)
                    neckMovementMonitoringVM.resetCalibrationResult()
                    navController.navigate(NavDestinations.CALIBRATION)
                },
                onHistoryReport = {
                    neckMovementMonitoringVM.getHistoryReport()
                    neckMovementMonitoringVM.sendFeatureExposureEvent(HeadphoneFeaturePageName.NECK_ASSESSMENT_LOGS)
                    navController.navigate(NavDestinations.HISTORY_REPORT)
                },
                onIntroduction = {
                    neckMovementMonitoringVM.sendFeatureExposureEvent(HeadphoneFeaturePageName.NECK_ASSESSMENT_INSTRUCTIONS)
                    neckMovementMonitoringVM.showGuideSheet()
                },
                showGuide = neckMovementMonitoringVM.showGuide.collectAsState().value,
                onHideGuide = { neckMovementMonitoringVM.hideGuide() },
                assetsBaseUrl = neckMovementMonitoringVM.assetsBaseUrl,
                swimming = neckMovementMonitoringVM.swimming.collectAsState().value
            )
        }
        composable(NavDestinations.CALIBRATION) {
            CalibrationScreen(
                onStartAssessment = {
                    neckMovementMonitoringVM.resetAssessmentData()
                    navController.navigate(NavDestinations.ASSESSMENT_PROCESS) {
                        popUpTo(NavDestinations.ASSESSMENT)
                    }
                },
                onRecalibrate = {
                    neckMovementMonitoringVM.resetCalibrationResult()
                    neckMovementMonitoringVM.startCalibration()
                },
                showNoteDialog = true,
                onQuit = {
                    navController.popBackStack()
                    neckMovementMonitoringVM.stopCalibration()
                },
                onContinue = { neckMovementMonitoringVM.startCalibration() },
                calibrationResult = neckMovementMonitoringVM.calibrationResult.collectAsState().value,
                calibrationAnimationUri = (neckMovementMonitoringVM.assetsBaseUrl + SU07_VIDEO_ANIMATION_PATH + VIDEO_ANIMATION_NECK_CALIBRATION).toUri(),
                deviceDisConnected = neckMovementMonitoringVM.showDeviceDisconnectedDialog.collectAsState().value,
            )
        }
        composable(NavDestinations.ASSESSMENT_PROCESS) {
            AssessmentProcessScreen(
                steps = neckMovementMonitoringVM.assessmentStepInfo.collectAsState().value,
                onDone = {
                    neckMovementMonitoringVM.resetAssessmentResultValue()
                    neckMovementMonitoringVM.sendFeatureExposureEvent(HeadphoneFeaturePageName.NECK_ASSESSMENT_RESULT)
                    navController.navigate(NavDestinations.ASSESSMENT_RESULT)
                },
                onQuit = {
                    neckMovementMonitoringVM.exitAssessment()
                    navController.popBackStack()
                },
                onStartAssessment = {
                    neckMovementMonitoringVM.startAssessment(it)
                },
                allAssessmentFinished = neckMovementMonitoringVM.allAssessmentComplete.collectAsState().value,
                assessing = neckMovementMonitoringVM.assessing.collectAsState().value,
                deviceDisConnected = neckMovementMonitoringVM.showDeviceDisconnectedDialog.collectAsState().value,
            )
        }
        composable(NavDestinations.ASSESSMENT_RESULT) {
            AssessmentResultScreen(
                assessmentResultType = neckMovementMonitoringVM.getAssessmentResultType(),
                onGenerateReport = { neckMovementMonitoringVM.getAssessmentResult() },
                onViewReport = {
                    neckMovementMonitoringVM.generateAssessmentResultDetail {
                        navController.navigate(NavDestinations.ASSESSMENT_REPORT_DETAIL) {
                            popUpTo(NavDestinations.ASSESSMENT)
                        }
                    }
                },
                onReassess = {
                    navController.popBackStack()
                },
                onExit = {
                    navController.navigate(NavDestinations.ASSESSMENT) {
                        popUpTo(NavDestinations.ASSESSMENT)
                        launchSingleTop = true
                    }
                },
                onSaveData = {
                    neckMovementMonitoringVM.saveCurrentAssessment {
                        navController.navigate(NavDestinations.ASSESSMENT) {
                            popUpTo(NavDestinations.ASSESSMENT)
                            launchSingleTop = true
                        }
                    }
                },
                generateReportResult = neckMovementMonitoringVM.generateReportResult.collectAsState().value,
                generatingReport = neckMovementMonitoringVM.generatingReport.collectAsState().value,
                loading = neckMovementMonitoringVM.operationLoading.collectAsState().value,
                assessmentForOtherUser = neckMovementMonitoringVM.otherUserAssessment()
            )
        }
        composable(NavDestinations.HISTORY_REPORT) {
            AssessmentHistoryReportScreen(
                onBackClick = { navController.popBackStack() },
                historyReports = neckMovementMonitoringVM.summaryHistoryAssessments.collectAsState().value,
                onItemClick = {
                    neckMovementMonitoringVM.getHistoryReportDetail(it)
                    navController.navigate(NavDestinations.ASSESSMENT_REPORT_DETAIL)
                },
                onDeleteReport = {
                    neckMovementMonitoringVM.deleteHistoryReport(it)
                },
                loading = neckMovementMonitoringVM.historyNeckAssessmentsLoading.collectAsState().value,
            )
        }
        composable(NavDestinations.ASSESSMENT_REPORT_DETAIL) {
            AssessmentReportDetailScreen(
                neckMovementMonitoringVM.assessmentReportDetail.collectAsState().value,
                abandonAndReassess = {
                    navController.popBackStack()
                    neckMovementMonitoringVM.resetAssessmentData()
                    navController.navigate(NavDestinations.ASSESSMENT_PROCESS)
                },
                saveAndReassess = {
                    neckMovementMonitoringVM.saveCurrentAssessment {
                        navController.popBackStack()
                        neckMovementMonitoringVM.resetAssessmentData()
                        navController.navigate(NavDestinations.ASSESSMENT_PROCESS)
                    }
                },
                onClose = {
                    // assessment for other user, don't need to save
                    if (neckMovementMonitoringVM.otherUserAssessment() || neckMovementMonitoringVM.historyAssessmentReport()) {
                        navController.navigateUp()
                    } else {
                        neckMovementMonitoringVM.saveCurrentAssessment {
                            navController.navigateUp()
                        }
                    }
                },
                onHealthGuideClick = {
                    navController.navigate(NavDestinations.ASSESSMENT_HEALTH_GUIDE)
                },
                assessmentForOtherUser = neckMovementMonitoringVM.otherUserAssessment(),
                viewHistoryData = neckMovementMonitoringVM.historyAssessmentReport()
            )
        }
        composable(NavDestinations.ASSESSMENT_HEALTH_GUIDE) {
            NeckHealthGuideScreen(
                neckMovementMonitoringVM.assetsBaseUrl,
                NeckAssessmentAdvice.entries.map { it.item }.toImmutableList(),
                onBackClick = {
                    navController.navigateUp()
                }
            )
        }
    }
}
