package com.suunto.headset.model

import androidx.annotation.StringRes
import kotlinx.collections.immutable.ImmutableList

enum class HeadphoneType {
    LEFT, RIGHT,
}

data class CustomizationKeyValue(
    val enabled: <PERSON><PERSON>an,
    val selected: Boolean,
    val type: HeadphoneType,
    @StringRes val titleRes: Int,
    val keyInfos: ImmutableList<CustomizationKeyInfo>,
    val nonCustomizationKeyInfo: ImmutableList<CustomizationKeyInfo>,
)
