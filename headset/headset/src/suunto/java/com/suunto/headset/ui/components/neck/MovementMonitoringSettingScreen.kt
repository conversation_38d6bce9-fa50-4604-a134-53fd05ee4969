package com.suunto.headset.ui.components.neck

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.SnackbarDuration
import androidx.compose.material.SnackbarHost
import androidx.compose.material.SnackbarHostState
import androidx.compose.material.Switch
import androidx.compose.material.SwitchDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.input.pointer.PointerEventPass
import androidx.compose.ui.input.pointer.PointerEventType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.suunto.headset.R
import com.suunto.headset.ui.components.CommonTopAppBar
import com.suunto.headset.ui.components.FunctionConflictDialog
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.launch
import timber.log.Timber

@SuppressLint("ReturnFromAwaitPointerEventScope")
@Composable
fun NeckMovementMonitoringSettingScreen(
    onBackClick: () -> Unit,
    neckMoveMonitoringEnabled: Boolean,
    intervalReminderTime: Int,
    onNeckMoveMonitoringChecked: (Boolean) -> Unit,
    onIntervalReminderTimeSet: (Int) -> Unit,
    onNeckMobilityAssessmentClick: () -> Unit,
    modifier: Modifier = Modifier,
    intervalOptions: ImmutableList<Int> = persistentListOf(15, 30, 60, 90),
    deviceDisconnected: Boolean = false,
) {
    val snackbarHostState = remember {
        SnackbarHostState()
    }
    Scaffold(
        modifier = modifier,
        topBar = {
            CommonTopAppBar(
                title = stringResource(id = R.string.neck_movement_monitoring),
                onBackClick = onBackClick
            )
        },
        snackbarHost = {
            SnackbarHost(snackbarHostState)
        }
    ) { paddingValues ->
        var showSetIntervalTimeDialog by remember {
            mutableStateOf(false)
        }
        val headphoneDisconnectedMessage = stringResource(R.string.headphone_not_connected)
        LaunchedEffect(deviceDisconnected) {
            if (deviceDisconnected)
                snackbarHostState.showSnackbar(
                    message = headphoneDisconnectedMessage,
                    duration = SnackbarDuration.Indefinite
                )
        }
        val updatedDeviceDisconnectedState = rememberUpdatedState(deviceDisconnected)

        ContentCenteringColumn(
            modifier = Modifier
                .background(MaterialTheme.colors.surface)
                .alpha(if (deviceDisconnected) 0.5f else 1f)
                .pointerInput(Unit) {
                    awaitPointerEventScope {
                        while (true) {
                            val event = awaitPointerEvent(PointerEventPass.Initial)
                            if (updatedDeviceDisconnectedState.value)
                                event.changes.forEach { it.consume() }
                        }
                    }
                }
                .padding(paddingValues)
        ) {
            var showFunctionConflictDialog by remember {
                mutableStateOf(false)
            }
            Row(
                modifier = Modifier
                    .padding(MaterialTheme.spacing.medium)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = MaterialTheme.spacing.xsmall)
                ) {
                    Text(
                        text = stringResource(id = R.string.neck_movement_monitoring),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colors.nearBlack
                    )
                    Text(
                        modifier = Modifier
                            .padding(top = MaterialTheme.spacing.xsmall),
                        text = stringResource(id = R.string.neck_movement_monitoring_function),
                        style = MaterialTheme.typography.body,
                        color = MaterialTheme.colors.darkGrey,
                    )
                }
                Switch(
                    modifier = Modifier.padding(start = MaterialTheme.spacing.medium),
                    checked = neckMoveMonitoringEnabled,
                    onCheckedChange = onNeckMoveMonitoringChecked,
                    colors = SwitchDefaults.colors(checkedThumbColor = MaterialTheme.colors.primary)
                )
            }
            Divider(color = MaterialTheme.colors.lightGrey)
            if (neckMoveMonitoringEnabled) {
                Row(
                    modifier = Modifier
                        .clickable {
                            showSetIntervalTimeDialog = true
                        }
                        .padding(horizontal = MaterialTheme.spacing.medium, vertical = 18.dp)
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        modifier = Modifier.weight(1f),
                        text = stringResource(id = R.string.interval_reminder_time),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colors.nearBlack
                    )
                    Text(
                        text = stringResource(
                            id = R.string.interval_reminder_time_value,
                            intervalReminderTime
                        ),
                        style = MaterialTheme.typography.body,
                        color = MaterialTheme.colors.primary
                    )
                }
            }
            Divider(color = MaterialTheme.colors.lightGrey)
            if (showSetIntervalTimeDialog) {
                Dialog(
                    onDismissRequest = { showSetIntervalTimeDialog = false }) {
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colors.nearWhite,
                            contentColor = MaterialTheme.colors.nearBlack
                        )
                    ) {
                        Text(
                            modifier = Modifier.padding(MaterialTheme.spacing.medium),
                            text = stringResource(id = R.string.interval_reminder_time),
                            color = MaterialTheme.colors.nearBlack,
                            style = MaterialTheme.typography.bodyXLargeBold
                        )
                        LazyColumn {
                            items(intervalOptions, key = { it }) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(
                                            horizontal = MaterialTheme.spacing.small,
                                            vertical = MaterialTheme.spacing.xsmall
                                        )
                                        .clickable {
                                            onIntervalReminderTimeSet.invoke(it)
                                            showSetIntervalTimeDialog = false
                                        },
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    RadioButton(
                                        selected = it == intervalReminderTime, onClick = {
                                            onIntervalReminderTimeSet.invoke(it)
                                            showSetIntervalTimeDialog = false
                                        },
                                        colors = RadioButtonDefaults.colors(
                                            selectedColor = MaterialTheme.colors.primary,
                                            unselectedColor = MaterialTheme.colors.darkGrey
                                        )
                                    )
                                    Text(
                                        modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                                        text = stringResource(
                                            id = R.string.interval_reminder_time_value,
                                            it
                                        ),
                                        style = MaterialTheme.typography.bodyLarge,
                                        color = MaterialTheme.colors.nearBlack,
                                    )
                                }
                            }
                        }
                    }
                }
            }
            if (showFunctionConflictDialog) {
                FunctionConflictDialog(onDismissRequest = {
                    showFunctionConflictDialog = false
                }, onConfirm = {
                    showFunctionConflictDialog = false
                    onNeckMobilityAssessmentClick.invoke()
                })
            }
        }

    }
}

@Preview
@Composable
private fun NeckHealthManagementScreenPreview() {
    NeckMovementMonitoringSettingScreen(
        onBackClick = {},
        neckMoveMonitoringEnabled = true,
        intervalReminderTime = 15,
        onNeckMoveMonitoringChecked = {},
        onIntervalReminderTimeSet = {},
        onNeckMobilityAssessmentClick = {})
}
