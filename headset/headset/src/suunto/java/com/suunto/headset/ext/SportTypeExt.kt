package com.suunto.headset.ext

import com.suunto.headset.model.SportType
import com.stt.android.core.R
import com.suunto.soa.ble.control.attr.SoaSportType

fun SportType.getNameResId(): Int {
    return when (this) {
        SportType.OPEN_WATER_SWIMMING -> R.string.open_water_swimming
        SportType.JUMP_ROPE -> R.string.jump_rope
        SportType.RUNNING -> R.string.running
        SportType.POOL_SWIMMING -> R.string.swimming
    }
}

fun SportType.toSoaSportType(): SoaSportType {
    return when (this) {
        SportType.OPEN_WATER_SWIMMING -> SoaSportType.OPEN_WATER_SWIMMING
        SportType.JUMP_ROPE -> SoaSportType.JUMP_ROPE
        SportType.RUNNING -> SoaSportType.RUNNING
        SportType.POOL_SWIMMING -> SoaSportType.POOL_SWIMMING
    }
}

fun SoaSportType.toSportType(): SportType {
    return when (this) {
        SoaSportType.OPEN_WATER_SWIMMING -> SportType.OPEN_WATER_SWIMMING
        SoaSportType.JUMP_ROPE -> SportType.JUMP_ROPE
        SoaSportType.RUNNING -> SportType.RUNNING
        SoaSportType.POOL_SWIMMING -> SportType.POOL_SWIMMING
    }
}
