package com.stt.android.remote.appversion

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

interface CheckAppVersionRemoteApi {
    suspend fun checkAppVersion(
        appVersionCode: String,
        innerAppVersionCode: Int,
        systemVersionCode: String,
        mobilePhoneBrand: String,
        packageName: String?,
        appBrand: String
    ): AppUpgradeInfo?

    suspend fun getVersionList(
        pageNum: Int,
        pageSize: Int,
        brandType: String,
    ): List<AppVersionInfo>?

    suspend fun getVersionNotes(
        innerAppVersionCode: Int,
        brandType: String,
    ): String?
}

@JsonClass(generateAdapter = true)
@Parcelize
data class AppUpgradeInfo(
    @Json(name = "appStoreLink")
    val appStoreLink: String = "",
    @<PERSON><PERSON>(name = "innerAppVersionCode")
    val innerAppVersionCode: Int = 0,
    @<PERSON><PERSON>(name = "appVersionCode")
    val appVersionCode: String = "",
    @<PERSON><PERSON>(name = "appVersionType")
    val appVersionType: String = ""
) : Parcelable {

    /**
     * Forced update: If the user opens the APP and cannot continue to use the app without updating,
     * it can only be used after updating. After entering any page of the APP,
     * a pop-up window will prompt the version update information and provide the "Update" button. Currently,
     * the user can only select "Update" or exit the app
     */
    fun isForceUpgrade(): Boolean {
        return appVersionType == "FORCE"
    }

    /**
     * The user does not perceive when opening the app
     */
    fun isSelectUpgrade(): Boolean {
        return appVersionType == "SELECT"
    }

    /**
     * Prompt update: Every time the user opens the APP, no matter on any page, the version update information will pop up.
     * The user can choose to update or not to update, which will not affect the user's use
     */
    fun isRecommendUpgrade(): Boolean {
        return appVersionType == "RECOMMEND"
    }
}

val AppUpgradeInfo?.isShowUpdateToUser: Boolean
    get() = this != null && !isSelectUpgrade()

val AppUpgradeInfo?.shouldShowRedDot: Boolean
    get() = this != null && (isSelectUpgrade() || isRecommendUpgrade())
