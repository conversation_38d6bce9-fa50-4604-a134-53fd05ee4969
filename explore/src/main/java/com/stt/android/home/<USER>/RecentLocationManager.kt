package com.stt.android.home.explore

import android.content.Context
import androidx.collection.LruCache
import com.google.android.gms.maps.model.LatLng
import com.stt.android.maps.MarkerZPriority
import com.stt.android.maps.SuuntoBitmapDescriptorFactory
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions

internal class RecentLocationManager(
    private val context: Context,
    private val map: SuuntoMap
) {
    private val recentLocationMarkers = object : LruCache<Int, SuuntoMarker>(5) {
        override fun entryRemoved(
            evicted: <PERSON><PERSON><PERSON>,
            key: Int,
            oldMarker: SuuntoMarker,
            newMarker: SuuntoMarker?
        ) {
            super.entryRemoved(evicted, key, oldMarker, newMarker)
            oldMarker.remove()
        }
    }

    fun addRecentLocation(location: LatLng) {
        map.addMarker(
            buildRecentLocationMarkerOptions(location)
        )?.let {
            recentLocationMarkers.put(it.hashCode(), it)
        }
    }

    fun showRecentLocations() {
        val options = mutableListOf<SuuntoMarkerOptions>()
        recentLocationMarkers.snapshot().values.mapNotNull { it.getPosition() }.forEach {
            options.add(
                buildRecentLocationMarkerOptions(it)
            )
        }
        map.addMarkers(options)
    }

    private fun buildRecentLocationMarkerOptions(location: LatLng) =
        SuuntoMarkerOptions()
            .position(location)
            .icon(
                SuuntoBitmapDescriptorFactory(
                    context
                ).fromVectorDrawableResource(R.drawable.recent_location_marker)
            )
            .anchor(0.5f, 0.5f)
            .zPriority(MarkerZPriority.SELECTED_GEOPOINT)
            .isRecentLocation(true)
}
