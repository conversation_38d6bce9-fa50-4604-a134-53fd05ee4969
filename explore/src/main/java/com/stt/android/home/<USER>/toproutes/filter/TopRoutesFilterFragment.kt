package com.stt.android.home.explore.toproutes.filter

import android.app.Dialog
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.unit.dp
import androidx.core.os.BundleCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.google.android.gms.maps.model.LatLng
import com.stt.android.R
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.compose.widgets.DraggableBottomSheetHandle
import com.stt.android.controllers.UserSettingsController
import com.stt.android.home.explore.routes.popular.BasePopularRouteDetailsActivity
import com.stt.android.home.explore.toproutes.TopRoutesSharedViewModel
import com.stt.android.home.explore.toproutes.carousel.RouteFeature
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.window.setFlagsAndColors
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@OptIn(ExperimentalMaterial3Api::class)
@AndroidEntryPoint
class TopRoutesFilterFragment : DialogFragment() {

    private val topRouteFilterViewModel: TopRoutesFilterViewModel by viewModels()
    private val topRoutesSharedViewModel: TopRoutesSharedViewModel by activityViewModels()

    @Inject
    lateinit var userSettingController: UserSettingsController

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = Dialog(requireContext(), R.style.FullScreenTransparentDialogStyle)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.apply {
            setFlagsAndColors()

            // Setup for making the contents scrollable when the soft keyboard hide parts of it,
            // below API 30 just use SOFT_INPUT_ADJUST_RESIZE flag, on newer versions combination
            // of setDecorFitsSystemWindows(false) and setOnApplyWindowInsetsListener in onCreateView
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE)
                setDecorFitsSystemWindows(false)
            } else {
                setSoftInputMode(
                    WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE or
                        WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
                )
            }
        }

        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(inflater.context).apply {
        setContentWithM3Theme {
            val visibleFeatures by topRoutesSharedViewModel.visibleFeatures.collectAsState()
            var selectedActivityTypes = rememberSaveable {
                topRoutesSharedViewModel.selectedActivityTypes.value
            }

            ModalBottomSheet(
                onDismissRequest = {
                    topRoutesSharedViewModel.updateSelectedTopRouteActivityTypes(
                        selectedActivityTypes
                    )
                    dismiss()
                },
                dragHandle = null,
                shape = RoundedCornerShape(16.dp),
                containerColor = MaterialTheme.colorScheme.surface,
                scrimColor = Color.Transparent,
                modifier = Modifier.fillMaxWidth()
            ) {
                DraggableBottomSheetHandle(bottomPadding = MaterialTheme.spacing.small)

                Column(
                    modifier = Modifier
                        .fillMaxHeight(0.88f)
                        .padding(top = MaterialTheme.spacing.xlarge)
                ) {
                    arguments?.let {
                        BundleCompat.getParcelable(
                            it,
                            EXTRA_KEY_CURRENT_CAMERA_LOCATION,
                            LatLng::class.java
                        )
                    }?.let { latLng ->
                        TopRouteFilterLocationScreen(latLng = latLng)
                    }

                    TopRoutesFilterBottomScreen(
                        viewModel = topRouteFilterViewModel,
                        selectedActivityTypes = selectedActivityTypes,
                        visibleFeatures = visibleFeatures,
                        measurementUnit = userSettingController.settings.measurementUnit,
                        infoModelFormatter = infoModelFormatter,
                        onItemClick = ::handleItemClickEvent,
                        selectedActivityChanged = {
                            selectedActivityTypes = it ?: emptyList()
                        }
                    )
                }
            }
        }
    }

    private fun handleItemClickEvent(routeFeature: RouteFeature) {
        startActivity(
            BasePopularRouteDetailsActivity.newStartIntentFromMap(
                requireContext(),
                routeFeature.routeId,
                routeFeature.activityType.id,
                routeFeature.awayYouDistance
            )
        )
    }

    companion object {
        private const val EXTRA_KEY_CURRENT_CAMERA_LOCATION =
            "com.stt.android.home.explore.toproutes.filter.CAMERA_LOCATION"

        fun newInstance(
            cameraPosition: LatLng?,
        ): TopRoutesFilterFragment =
            TopRoutesFilterFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(EXTRA_KEY_CURRENT_CAMERA_LOCATION, cameraPosition)
                }
            }
    }
}
