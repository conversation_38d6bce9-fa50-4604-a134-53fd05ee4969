package com.stt.android.home.explore.routes.planner

import com.google.firebase.analytics.FirebaseAnalytics
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.domain.routes.Route
import com.stt.android.domain.routes.RouteRepository
import com.stt.android.domain.routes.RouteTool
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.routes.RouteAnalytics
import com.stt.android.home.explore.routes.RouteUtils
import io.reactivex.Completable
import kotlinx.coroutines.rx2.rxCompletable
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class RouteAnalyticsTracker
@Inject constructor(
    private val emarsysAnalytics: EmarsysAnalytics,
    private val routeRepository: RouteRepository,
    private val routeTool: RouteTool,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) : RouteAnalytics {
    override fun trackRouteDetailsScreenAnalytics(route: Route, ascent: Double?) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.ROUTE_DETAILS_SCREEN,
            AnalyticsProperties().apply {
                put(AnalyticsEventProperty.WAYPOINTS, route.getWaypointsCount())
                put(
                    AnalyticsEventProperty.ACTIVITY_TYPES,
                    RouteUtils.getActivitiesSimpleNames(route)
                )
                put(AnalyticsEventProperty.DISTANCE_IN_METERS, route.totalDistance)
                put(
                    AnalyticsEventProperty.DURATION_IN_SECONDS,
                    route.getDurationEstimation()
                )
                if (ascent != null) {
                    put(AnalyticsEventProperty.TOTAL_ASCENT, ascent)
                }
            }
        )
    }

    override fun trackRoutePlanningInstructionsButtonTapped(): Completable = rxCompletable {
        val routeCount = routeRepository.getRouteCount()
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.ROUTE_PLANNING_INSTRUCTIONS_BUTTON_TAPPED,
            AnalyticsProperties().put(AnalyticsEventProperty.EXISTING_ROUTES_AMOUNT, routeCount)
        )
    }

    override fun trackRouteDelete(
        route: Route,
        confirm: Boolean,
        currentUser: String
    ): Completable {
        return Completable.fromAction {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.ROUTE_DELETE,
                AnalyticsProperties().apply {
                    put(AnalyticsEventProperty.WAYPOINTS, route.getWaypointsCount())
                    putConfirmCancel(AnalyticsEventProperty.RESULT, confirm)
                    put(
                        AnalyticsEventProperty.DAYS_SINCE_CREATED,
                        route.getDaysSinceCreated()
                    )
                    put(
                        AnalyticsEventProperty.DISTANCE_IN_METERS,
                        route.totalDistance
                    )
                    put(AnalyticsEventProperty.DURATION_IN_SECONDS, route.getDurationEstimation())
                }
            )
        }.andThen(trackRoutesPlannedOrComplete(confirm, currentUser))
    }

    private fun trackRoutesPlannedOrComplete(confirm: Boolean, currentUser: String): Completable {
        return if (confirm) {
            trackRoutesPlanned(currentUser)
        } else {
            Completable.complete()
        }
    }

    override fun trackRoutesPlanned(currentUser: String): Completable = rxCompletable {
        val routeCount = routeRepository.getRouteCount()
        amplitudeAnalyticsTracker.trackUserProperty(AnalyticsUserProperty.ROUTES_PLANNED, routeCount)
        firebaseAnalyticsTracker.trackEvent(
            AnalyticsEvent.ROUTES_PLANNED,
            FirebaseAnalytics.Param.VALUE,
            routeCount,
        )
    }.onErrorComplete {
        Timber.w(it, "Failed to track routes ${AnalyticsUserProperty.ROUTES_PLANNED}")
        true
    }

    override fun trackRouteSaved(
        isEdited: Boolean,
        isWorkoutToRoute: Boolean,
        analyticsProperties: AnalyticsProperties,
        currentUser: String,
        scrubbingCount: Int
    ): Completable {
        return trackRoutesPlanned(currentUser)
            .doOnComplete {
                analyticsProperties.put(
                    AnalyticsEventProperty.CONTEXT,
                    if (isEdited) {
                        AnalyticsPropertyValue.RoutePlanningContext.EDIT_EXISTING_ROUTE
                    } else {
                        AnalyticsPropertyValue.RoutePlanningContext.CREATE_NEW_ROUTE
                    }
                )
                    .put(
                        AnalyticsEventProperty.ROUTE_FROM_ACTIVITY,
                        if (isWorkoutToRoute) {
                            AnalyticsPropertyValue.YES
                        } else {
                            AnalyticsPropertyValue.NO
                        }
                    )
                    .put(
                        AnalyticsEventProperty.ELEVATION_GRAPH_SCRUBBING,
                        scrubbingCount
                    )
                amplitudeAnalyticsTracker.trackEvent(
                    AnalyticsEvent.ROUTE_PLANNING_SAVE_ROUTE,
                    analyticsProperties
                )
                emarsysAnalytics.trackEventWithProperties(
                    AnalyticsEvent.ROUTE_PLANNING_SAVE_ROUTE,
                    analyticsProperties.map
                )
            }
    }

    override fun trackRouteImport(isButtonImport: Boolean) {
        val properties = AnalyticsProperties()
        val importSource = if (isButtonImport) {
            AnalyticsPropertyValue.RouteImportProperty.IMPORT_BUTTON
        } else {
            AnalyticsPropertyValue.RouteImportProperty.FILE_SYSTEM
        }
        properties.put(AnalyticsEventProperty.ROUTE_IMPORT_SOURCE, importSource)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.ROUTE_IMPORT, properties)
        emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.ROUTE_IMPORT, properties.map)
    }

    override fun trackRoutePlanningEditRoute(route: Route) {
        val properties = AnalyticsProperties().apply {
            put(AnalyticsEventProperty.WAYPOINTS, route.getWaypointsCount())
            put(
                AnalyticsEventProperty.DISTANCE_IN_METERS,
                route.totalDistance
            )
            put(
                AnalyticsEventProperty.DURATION_IN_MINUTES,
                TimeUnit.SECONDS.toMinutes(route.getDurationEstimation())
            )
            put(
                AnalyticsEventProperty.ROUTES_POINTS,
                routeTool.getRoutePointsForAnalytics(route.segments)
            )
            put(AnalyticsEventProperty.TOTAL_ASCENT, route.ascent)
        }
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.ROUTE_PLANNING_EDIT_ROUTE, properties)
        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.ROUTE_PLANNING_EDIT_ROUTE,
            properties.map
        )
    }

    override fun trackRoutePlanningActivitiesChanged(activities: List<ActivityType>) {
        val properties = AnalyticsProperties().apply {
            put(AnalyticsEventProperty.ACTIVITY_TYPES, activities.map { it.simpleName })
            put(AnalyticsEventProperty.ACTIVITY_TYPE_COUNT, activities.size)
        }
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.ROUTE_PLANNING_ACTIVITIES_CHANGED,
            properties
        )
        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.ROUTE_PLANNING_ACTIVITIES_CHANGED,
            properties.map
        )
    }
}
