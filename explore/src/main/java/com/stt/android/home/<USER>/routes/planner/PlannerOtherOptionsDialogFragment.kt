package com.stt.android.home.explore.routes.planner

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.ContentAlpha
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.ListItem
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.fragment.app.DialogFragment
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.home.explore.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class PlannerOtherOptionsDialogFragment : DialogFragment() {

    private var isRouteClosed = false

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return Dialog(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                AppTheme {
                    Body(
                        isRouteClosed = isRouteClosed,
                        onReverseRouteClicked = {
                            listener?.onReverseRouteClicked()
                            dismiss()
                        },
                        onCloseRouteClicked = {
                            listener?.onCloseRouteClicked()
                            dismiss()
                        },
                        onBackTraceClicked = {
                            listener?.onBackTraceClicked()
                            dismiss()
                        }
                    )
                }
            }
        }
    }

    private val listener: PlannerOtherOptionsListener?
        get() = if (parentFragment is PlannerOtherOptionsListener) {
            parentFragment as PlannerOtherOptionsListener
        } else if (activity is PlannerOtherOptionsListener) {
            activity as PlannerOtherOptionsListener
        } else {
            null
        }

    fun setIsRouteClosed(routeClosed: Boolean) {
        isRouteClosed = routeClosed
    }

    interface PlannerOtherOptionsListener {
        fun onReverseRouteClicked()
        fun onCloseRouteClicked()
        fun onBackTraceClicked()
    }

    companion object {
        const val FRAGMENT_TAG = "PlannerOtherOptionsDialogFragment.FRAGMENT_TAG"

        @JvmStatic
        fun newInstance(): PlannerOtherOptionsDialogFragment {
            return PlannerOtherOptionsDialogFragment()
        }
    }
}

@Composable
private fun Body(
    isRouteClosed: Boolean,
    onReverseRouteClicked: () -> Unit,
    onCloseRouteClicked: () -> Unit,
    onBackTraceClicked: () -> Unit,
    modifier: Modifier = Modifier
) {

    val isRouteClosedSaveable by rememberSaveable {
        mutableStateOf(isRouteClosed)
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(bottom = 10.dp)
    ) {
        Box(
            modifier = Modifier
                .wrapContentHeight()
                .padding(MaterialTheme.spacing.medium),
            contentAlignment = Alignment.CenterStart
        ) {
            Text(
                text = stringResource(com.stt.android.R.string.route_fab_options_title),
                style = MaterialTheme.typography.bodyXLargeBold
            )
        }

        LazyColumn(modifier = Modifier) {
            item {
                OptionItem(
                    iconResId = R.drawable.ic_reverse_route,
                    textResId = com.stt.android.R.string.reverse_route_title,
                    secondaryTextResId = com.stt.android.R.string.reverse_route_subtitle,
                    isClickable = true,
                    onItemClicked = onReverseRouteClicked
                )
                OptionItem(
                    iconResId = R.drawable.ic_back_to_start,
                    textResId = com.stt.android.R.string.back_to_start_title,
                    secondaryTextResId = com.stt.android.R.string.back_to_start_subtitle,
                    isClickable = !isRouteClosedSaveable,
                    onItemClicked = onCloseRouteClicked
                )
                OptionItem(
                    iconResId = R.drawable.ic_back_trace,
                    textResId = com.stt.android.R.string.back_trace_title,
                    secondaryTextResId = com.stt.android.R.string.back_trace_subtitle,
                    isClickable = !isRouteClosedSaveable,
                    onItemClicked = onBackTraceClicked
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun OptionItem(
    iconResId: Int,
    textResId: Int,
    secondaryTextResId: Int,
    isClickable: Boolean,
    onItemClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val interactionSource = remember { MutableInteractionSource() }
    val rippleIndication = ripple(bounded = true, color = Color.Gray)
    val alpha = if (!isClickable) ContentAlpha.disabled else 1f

    ListItem(
        icon = {
            Icon(
                painter = painterResource(id = iconResId),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier.size(MaterialTheme.iconSizes.medium)
            )
        },
        text = {
            Text(
                text = stringResource(textResId),
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.bodyLarge
            )
        },
        secondaryText = {
            Text(
                text = stringResource(secondaryTextResId),
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.bodySmall
            )
        },
        modifier = modifier
            .alpha(alpha)
            .clickable(
                interactionSource = interactionSource,
                indication = rippleIndication,
                enabled = isClickable
            ) {
                CoroutineScope(Dispatchers.Main).launch {
                    delay(100) // delay to allow ripple effect
                    onItemClicked()
                }
            }
    )
}

@Preview(showBackground = true)
@Composable
private fun BodyPreview() {
    AppTheme {
        Body(
            isRouteClosed = false,
            onReverseRouteClicked = {},
            onCloseRouteClicked = {},
            onBackTraceClicked = {}
        )
    }
}
