package com.stt.android.home.explore.pois

import android.os.Bundle
import android.view.View
import com.stt.android.ui.utils.setOnClickListenerThrottled

class SuuntoPOIDetailsFragment: POIDetailsFragment() {
    interface SuuntoPOIDetailsListener: POIDetailsListener {
        fun onPOINavigateOnWatchNowClicked()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.poiDetailsNavigateNow.setOnClickListenerThrottled {
            (parentFragment as? SuuntoPOIDetailsListener)?.onPOINavigateOnWatchNowClicked()
        }
    }

    fun showNavigateOnWatchNow(show: Boolean) {
        binding.poiDetailsNavigateNow.visibility = if (show) View.VISIBLE else View.GONE
    }

    fun setNavigateOnWatchNowEnabled(isEnabled: <PERSON>olean) {
        binding.poiDetailsNavigateNow.isEnabled = isEnabled
        binding.poiDetailsNavigateNow.alpha = if (isEnabled) 1f else 0.5f
    }

    companion object {
        const val TAG = "SuuntoPOIDetailsFragment"
    }
}
