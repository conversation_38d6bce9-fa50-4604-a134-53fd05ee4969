import com.soy.algorithms.climbanalysis.entities.ClimbSegment
import com.soy.algorithms.climbanalysis.entities.ClimbSegmentType
import com.soy.algorithms.climbanalysis.entities.StravaClimbCategory

const val INITIAL_ROUTE_SIMPLIFICATION_MAX_DISTANCE = 2.0

val ClimbSegment.wayPointType: Int get() = when (climbSegmentType) {
    ClimbSegmentType.FLAT -> 100
    ClimbSegmentType.UPHILL -> 101
    ClimbSegmentType.DOWNHILL -> 102
    ClimbSegmentType.CLIMB -> when (stravaClimbCategory) {
        StravaClimbCategory.HORS_CATEGORIE -> 103
        StravaClimbCategory.CATEGORY_1 -> 104
        StravaClimbCategory.CATEGORY_2 -> 105
        StravaClimbCategory.CATEGORY_3 -> 106
        StravaClimbCategory.CATEGORY_4 -> 107
        null -> 104
    }
    ClimbSegmentType.DESCENT -> when (stravaClimbCategory) {
        StravaClimbCategory.HORS_CATEGORIE -> 108
        StravaClimbCategory.CATEGORY_1 -> 109
        StravaClimbCategory.CATEGORY_2 -> 110
        StravaClimbCategory.CATEGORY_3 -> 111
        StravaClimbCategory.CATEGORY_4 -> 112
        null -> 109
    }
}
