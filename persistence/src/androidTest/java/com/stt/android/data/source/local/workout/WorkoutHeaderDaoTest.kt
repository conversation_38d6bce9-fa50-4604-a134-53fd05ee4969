package com.stt.android.data.source.local.workout

import androidx.test.ext.junit.runners.AndroidJUnit4
import app.cash.turbine.test
import com.google.common.truth.Truth.assertThat
import com.stt.android.data.source.local.RoomAppDatabaseTest
import com.stt.android.data.source.local.tags.LocalSuuntoTag
import com.stt.android.data.source.local.tags.TagsTestUtils
import com.stt.android.data.source.local.tags.UserTagsDao
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
internal class WorkoutHeaderDaoTest : RoomAppDatabaseTest() {

    private lateinit var workoutHeaderDao: WorkoutHeaderDao
    private lateinit var userTagsDao: UserTagsDao

    @Before
    fun setup() {
        workoutHeaderDao = db.workoutHeaderDao()
        userTagsDao = db.userTagsDao()
    }

    @Test
    fun findAllByKeys() = runTest {
        fakeWorkoutHeader.run {
            workoutHeaderDao.insert(this)
            val result = workoutHeaderDao.findAllByKeysSync(listOf(key!!))
            assertThat(result).containsExactly(this)
        }
    }

    @Test
    fun findLatestNotDeletedWorkout() = runTest {
        fakeWorkoutHeader.run {
            workoutHeaderDao.insert(this)
            val deletedWorkout = copy(id = id + 1, startTime = startTime + 20, deleted = true)
            workoutHeaderDao.insert(deletedWorkout)
            val result = workoutHeaderDao.findLatestNotDeletedWorkout(username)
            assertThat(result).isEqualTo(this)
        }
    }

    @Test
    fun findLatestNotDeletedShareableWorkout() = runTest {
        fakeWorkoutHeader.run {
            workoutHeaderDao.insert(this)
            val diveWorkout = copy(
                id = id + 1,
                startTime = startTime + 10,
                activityId = 78,
                pictureCount = 0,
                polyline = null
            )
            workoutHeaderDao.insert(diveWorkout)
            val deletedWorkout = copy(id = id + 2, startTime = startTime + 20, deleted = true)
            workoutHeaderDao.insert(deletedWorkout)
            val result = workoutHeaderDao.findLatestNotDeletedShareableWorkout(username)
            assertThat(result).isEqualTo(diveWorkout)
        }
    }

    @Test
    fun shouldReturnNull_whenNoShareableCommuteIsFound() = runTest {
        fakeWorkoutHeader.copy(suuntoTags = null).run {
            workoutHeaderDao.insert(this)
            val deletedWorkout =
                copy(id = id + 2, startTime = startTime + 20, suuntoTags = listOf(LocalSuuntoTag.COMMUTE), deleted = true)
            workoutHeaderDao.insert(deletedWorkout)
            val result = workoutHeaderDao.findLatestNotDeletedShareableCommuteWorkout(username)
            assertThat(result).isNull()
        }
    }

    @Test
    fun shouldReturnWorkoutHeader_whenAWorkoutIsNotDeletedAndIsCommuteAndShareable() = runTest {
        fakeWorkoutHeader.copy(
            suuntoTags = listOf(LocalSuuntoTag.COMMUTE),
            deleted = false
        ).run {
            workoutHeaderDao.insert(this)
            val deletedWorkout = copy(id = id + 2, startTime = startTime + 20, deleted = true)
            workoutHeaderDao.insert(deletedWorkout)
            val result = workoutHeaderDao.findLatestNotDeletedShareableCommuteWorkout(username)
            assertThat(result).isEqualTo(this)
        }
    }

    @Test
    fun findOldestNotDeletedWorkout() = runTest {
        fakeWorkoutHeader.run {
            workoutHeaderDao.insert(this)
            val deletedWorkout = copy(id = id + 1, startTime = startTime - 20, deleted = true)
            workoutHeaderDao.insert(deletedWorkout)
            val result = workoutHeaderDao.findOldestNotDeletedWorkout(username, startTime)
            assertThat(result).isEqualTo(this)
        }
    }

    @Test
    fun findAllPagedOfType_nonNullType() = runTest {
        fakeWorkoutHeader.run {
            // Add workouts of the type we're querying, results below should behave like these are
            // the only inserted workouts
            repeat(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE * 2) { i ->
                workoutHeaderDao.insert(copy(id = id + i, startTime = startTime + i))
            }
            // Results should not have these
            val idOffset = WorkoutHeaderDao.WORKOUTS_PAGE_SIZE * 2
            repeat(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE * 2) { i ->
                workoutHeaderDao.insert(
                    copy(
                        id = id + idOffset + i,
                        startTime = startTime + i,
                        activityId = activityId + 1
                    )
                )
            }

            val result1 = workoutHeaderDao.findAllPagedOfType(
                username = username,
                includeDeleted = true,
                activityTypeId = activityId,
                page = 1
            )
            assertThat(result1).hasSize(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE)
            assertThat(result1.first().startTime).isEqualTo(startTime + 59)
            assertThat(result1.last().startTime).isEqualTo(startTime + 30)

            val result2 = workoutHeaderDao.findAllPagedOfType(
                username = username,
                includeDeleted = true,
                activityTypeId = activityId,
                page = 2
            )
            assertThat(result2).hasSize(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE)
            assertThat(result2.first().startTime).isEqualTo(startTime + 29)
            assertThat(result2.last().startTime).isEqualTo(startTime)

            val result3 = workoutHeaderDao.findAllPagedOfType(
                username = username,
                includeDeleted = true,
                activityTypeId = activityId,
                page = 3
            )
            assertThat(result3).isEmpty()

            // Not sending a page (aka: value <=0) should return all the data
            val result4 = workoutHeaderDao.findAllPagedOfType(
                username = username,
                includeDeleted = true,
                activityTypeId = activityId,
                page = 0
            )
            assertThat(result4).hasSize(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE * 2)
        }
    }

    @Test
    fun findAllPagedOfType_nullType() = runTest {
        fakeWorkoutHeader.run {
            repeat(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE * 2) { i ->
                workoutHeaderDao.insert(
                    copy(
                        id = id + i,
                        startTime = startTime + i,
                        activityId = activityId + (i % 3)
                    )
                )
            }

            val result1 = workoutHeaderDao.findAllPagedOfType(
                username = username,
                includeDeleted = true,
                activityTypeId = null,
                page = 1
            )
            assertThat(result1).hasSize(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE)
            assertThat(result1.first().startTime).isEqualTo(startTime + 59)
            assertThat(result1.last().startTime).isEqualTo(startTime + 30)

            val result2 = workoutHeaderDao.findAllPagedOfType(
                username = username,
                includeDeleted = true,
                activityTypeId = null,
                page = 2
            )
            assertThat(result2).hasSize(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE)
            assertThat(result2.first().startTime).isEqualTo(startTime + 29)
            assertThat(result2.last().startTime).isEqualTo(startTime)

            val result3 = workoutHeaderDao.findAllPagedOfType(
                username = username,
                includeDeleted = true,
                activityTypeId = null,
                page = 3
            )
            assertThat(result3).isEmpty()

            // Not sending a page (aka: value <=0) should return all the data
            val result4 = workoutHeaderDao.findAllPagedOfType(
                username = username,
                includeDeleted = true,
                activityTypeId = null,
                page = 0
            )
            assertThat(result4).hasSize(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE * 2)
        }
    }

    @Test
    fun findAllPagedExcludingTypes_withEmptyExclusionSet() = runTest {
        fakeWorkoutHeader.run {
            repeat(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE * 2) { i ->
                workoutHeaderDao.insert(
                    copy(
                        id = id + i,
                        startTime = startTime + i,
                        activityId = activityId + (i % 2)
                    )
                )
            }

            val result1 = workoutHeaderDao.findAllPagedExcludingTypes(
                username = username,
                includeDeleted = true,
                excludedTypes = emptySet(),
                page = 1
            )
            assertThat(result1).hasSize(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE)
            assertThat(result1.first().startTime).isEqualTo(startTime + 59)
            assertThat(result1.last().startTime).isEqualTo(startTime + 30)

            val result2 = workoutHeaderDao.findAllPagedExcludingTypes(
                username = username,
                includeDeleted = true,
                excludedTypes = emptySet(),
                page = 2
            )
            assertThat(result2).hasSize(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE)
            assertThat(result2.first().startTime).isEqualTo(startTime + 29)
            assertThat(result2.last().startTime).isEqualTo(startTime)

            val result3 = workoutHeaderDao.findAllPagedExcludingTypes(
                username = username,
                includeDeleted = true,
                excludedTypes = emptySet(),
                page = 3
            )
            assertThat(result3).isEmpty()

            // Not sending a page (aka: value <=0) should return all the data
            val result4 = workoutHeaderDao.findAllPagedExcludingTypes(
                username = username,
                includeDeleted = true,
                excludedTypes = emptySet(),
                page = 0
            )
            assertThat(result4).hasSize(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE * 2)
        }
    }

    @Test
    fun findAllPagedExcludingTypes_withNonEmptyExclusionSet() = runTest {
        fakeWorkoutHeader.run {
            // Add workouts of the types we're querying, results below should behave like these are
            // the only inserted workouts
            repeat(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE * 2) { i ->
                workoutHeaderDao.insert(
                    copy(
                        id = id + i,
                        startTime = startTime + i,
                        activityId = activityId + (i % 2)
                    )
                )
            }
            // Results should not have these
            val idOffset = WorkoutHeaderDao.WORKOUTS_PAGE_SIZE * 2
            repeat(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE * 2) { i ->
                workoutHeaderDao.insert(
                    copy(
                        id = id + idOffset + i,
                        startTime = startTime + i,
                        activityId = activityId + 2 + (i % 2)
                    )
                )
            }

            val excludedTypes = setOf(activityId + 2, activityId + 3)

            val result1 = workoutHeaderDao.findAllPagedExcludingTypes(
                username = username,
                includeDeleted = true,
                excludedTypes = excludedTypes,
                page = 1
            )
            assertThat(result1).hasSize(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE)
            assertThat(result1.none { excludedTypes.contains(it.activityId) }).isTrue()
            assertThat(result1.first().startTime).isEqualTo(startTime + 59)
            assertThat(result1.last().startTime).isEqualTo(startTime + 30)

            val result2 = workoutHeaderDao.findAllPagedExcludingTypes(
                username = username,
                includeDeleted = true,
                excludedTypes = excludedTypes,
                page = 2
            )
            assertThat(result2).hasSize(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE)
            assertThat(result2.none { excludedTypes.contains(it.activityId) }).isTrue()
            assertThat(result2.first().startTime).isEqualTo(startTime + 29)
            assertThat(result2.last().startTime).isEqualTo(startTime)

            val result3 = workoutHeaderDao.findAllPagedExcludingTypes(
                username = username,
                includeDeleted = true,
                excludedTypes = excludedTypes,
                page = 3
            )
            assertThat(result3).isEmpty()

            // Not sending a page (aka: value <=0) should return all the data
            val result4 = workoutHeaderDao.findAllPagedExcludingTypes(
                username = username,
                includeDeleted = true,
                excludedTypes = excludedTypes,
                page = 0
            )
            assertThat(result4.none { excludedTypes.contains(it.activityId) }).isTrue()
            assertThat(result4).hasSize(WorkoutHeaderDao.WORKOUTS_PAGE_SIZE * 2)
        }
    }

    @Test
    fun findByIdsForUser() = runTest {
        fakeWorkoutHeader.run {
            workoutHeaderDao.insert(this)
            workoutHeaderDao.insert(copy(id + 1))
            val result = workoutHeaderDao.findByIdsForUser(username, listOf(id))
            assertThat(result).containsExactly(this)
        }
    }

    @Test
    fun findLatestNotDeletedWorkouts() = runTest {
        fakeWorkoutHeader.run {
            workoutHeaderDao.insert(this)
            workoutHeaderDao.insert(copy(id + 1, startTime = startTime + 10))
            workoutHeaderDao.insert(copy(id + 2, activityId = activityId + 1))
            val result = workoutHeaderDao.findLatestNotDeletedWorkouts(
                username,
                startTime,
                activityId
            )
            assertThat(result).containsExactly(this)
        }
    }

    @Test
    fun findLatestNotDeletedWorkoutsWithLimit() = runTest {
        fakeWorkoutHeader.run {
            workoutHeaderDao.insert(this)
            workoutHeaderDao.insert(copy(id + 1, startTime = startTime + 10))
            workoutHeaderDao.insert(copy(id + 2, activityId = activityId + 1))
            workoutHeaderDao.insert(copy(id + 3, startTime = startTime - 10))
            val result = workoutHeaderDao.findLatestNotDeletedWorkoutsWithLimit(
                username,
                startTime,
                activityId,
                limit = 1
            )
            assertThat(result).containsExactly(this)
        }
    }

    @Test
    fun getLatestNotDeletedWorkoutHeadersWithUserTags() = runTest {
        fakeWorkoutHeader.run {
            workoutHeaderDao.insert(this)
            val laterWorkout = copy(id + 1, startTime = startTime + 10)
            workoutHeaderDao.insert(laterWorkout)
            workoutHeaderDao.insert(copy(id + 2, startTime = startTime + 20, deleted = true))

            val result =
                workoutHeaderDao.getLatestNotDeletedWorkoutHeadersAndMatchingUserTags(
                    username,
                    limit = 2
                )

            assertThat(result.keys).containsExactly(
                laterWorkout,
                this
            )
        }
    }

    @Test
    fun getNotDeletedWorkoutHeaders() = runTest {
        fakeWorkoutHeader.run {
            listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20),
                copy(id + 3, startTime = startTime + 30, activityId = activityId + 1),
                copy(id + 4, startTime = startTime + 40, deleted = true),
            ).forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.getNotDeletedWorkoutHeaders(
                username,
                since = startTime,
                until = startTime + 50,
                activityTypeId = activityId
            )
            assertThat(result).containsExactly(
                copy(id + 2, startTime = startTime + 20),
                copy(id + 1, startTime = startTime + 10),
                this,
            )
        }
    }

    @Test
    fun loadUserWorkoutSummary() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20),
                copy(id + 3, startTime = startTime + 30),
                copy(id + 4, startTime = startTime + 40),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.loadUserWorkoutSummary(username)
            assertThat(result).isEqualTo(
                LocalUserWorkoutSummary(
                    totalWorkouts = testList.size,
                    totalDistance = totalDistance * testList.size,
                    totalDuration = totalTime * testList.size,
                    totalEnergyKCal = energyConsumption * testList.size
                )
            )
        }
    }

    @Test
    fun loadUserWorkoutSummaryWithConditions() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20),
                copy(id + 3, startTime = startTime + 30, activityId = activityId + 1),
                copy(id + 4, startTime = startTime + 40),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.loadUserWorkoutSummary(
                username = username,
                activityTypeId = activityId,
                since = startTime,
                until = startTime + 30
            )
            assertThat(result).isEqualTo(
                LocalUserWorkoutSummary(
                    totalWorkouts = 3,
                    totalDistance = totalDistance * 3,
                    totalDuration = totalTime * 3,
                    totalEnergyKCal = energyConsumption * 3
                )
            )
        }
    }

    @Test
    fun loadUserWorkoutTotals() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20),
                copy(id + 3, startTime = startTime + 30),
                copy(id + 4, startTime = startTime + 40),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.loadUserWorkoutTotals(username)
            assertThat(result).isEqualTo(
                LocalUserWorkoutTotals(
                    totalWorkouts = testList.size,
                    totalTime = totalTime * testList.size,
                    pictureCount = pictureCount * testList.size,
                    commentCount = commentCount * testList.size,
                    reactionCount = reactionCount * testList.size,
                )
            )
        }
    }

    @Test
    fun countPrivateWorkouts() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20, sharingFlags = 0),
                copy(id + 3, startTime = startTime + 30),
                copy(id + 4, startTime = startTime + 40, sharingFlags = 0),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.countPrivateWorkouts(username)
            assertThat(result).isEqualTo(2)
        }
    }

    @Test
    fun countWorkoutsForFollowers() = runTest {
        val followerSharingMask =
            LocalSharingOption.FOLLOWERS.backendId or LocalSharingOption.SHARED_MASK
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20, sharingFlags = followerSharingMask),
                copy(id + 3, startTime = startTime + 30),
                copy(id + 4, startTime = startTime + 40, sharingFlags = followerSharingMask),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.countWorkoutsForFollowers(username)
            assertThat(result).isEqualTo(2)
        }
    }

    @Test
    fun countPublicWorkouts() = runTest {
        val publicSharingMask =
            LocalSharingOption.EVERYONE.backendId or LocalSharingOption.SHARED_MASK
        fakeWorkoutHeader.run {
            val testList = listOf(
                copy(sharingFlags = 0),
                copy(id + 1, startTime = startTime + 10, sharingFlags = 0),
                copy(id + 2, startTime = startTime + 20, sharingFlags = publicSharingMask),
                copy(id + 3, startTime = startTime + 30, sharingFlags = 0),
                copy(id + 4, startTime = startTime + 40, sharingFlags = publicSharingMask),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.countPublicWorkouts(username)
            assertThat(result).isEqualTo(2)
        }
    }

    @Test
    fun countWorkoutsWithDescription() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20, description = null),
                copy(id + 3, startTime = startTime + 30),
                copy(id + 4, startTime = startTime + 40, description = null),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.countWorkoutsWithDescription(username)
            assertThat(result).isEqualTo(3)
        }
    }

    @Test
    fun countWorkoutsBetween() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20),
                copy(id + 3, startTime = startTime + 30),
                copy(id + 4, startTime = startTime + 40),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.countWorkoutsBetween(username, startTime, startTime + 20)
            assertThat(result).isEqualTo(3)
        }
    }

    @Test
    fun findAllWhereOwner() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20, username = username + 1),
                copy(id + 3, startTime = startTime + 30),
                copy(id + 4, startTime = startTime + 40, username = username + 2),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.findAllWhereOwner(listOf(username, username + 1))
            assertThat(result).isEqualTo((testList - testList.last()).reversed())

            val result2 = workoutHeaderDao.findAllWhereOwner(listOf(username, username + 1), 3)
            assertThat(result2).containsExactly(testList[3], testList[2], testList[1])
        }
    }

    @Test
    fun findSyncedButLocallyChanged() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, key = null),
                copy(id + 2, locallyChanged = true),
                copy(id + 3, key = null, locallyChanged = true),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.findSyncedButLocallyChanged(username)
            assertThat(result).containsExactly(testList[2])
        }
    }

    @Test
    fun findAllDeleted() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, deleted = true),
                copy(id + 2, deleted = true),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.findAllDeleted(username)
            assertThat(result).containsExactly(testList[1], testList[2])
        }
    }

    @Test
    fun findNeverSyncedWorkoutsForUser() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, key = null),
                copy(id + 2, key = null, manuallyCreated = true),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.findNeverSyncedWorkoutsForUser(username)
            assertThat(result).containsExactly(testList[1])
        }
    }

    @Test
    fun findAllSynced() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, key = null),
                copy(id + 2, key = null, manuallyCreated = true),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.findAllSynced()
            assertThat(result).containsExactly(testList[0])
        }
    }

    @Test
    fun findManuallyCreatedWorkouts() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, manuallyCreated = true),
                copy(id + 2, key = null, manuallyCreated = true),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.findManuallyCreatedWorkouts(username)
            assertThat(result).containsExactly(testList[2])
        }
    }

    @Test
    fun markAsSynced() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                copy(locallyChanged = true),
                copy(id + 1, key = key + 1, locallyChanged = true),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            workoutHeaderDao.markAsSynced(listOf(key!!, key!! + 1))
            assertThat(workoutHeaderDao.findById(id)!!.locallyChanged).isFalse()
            assertThat(workoutHeaderDao.findById(id + 1)!!.locallyChanged).isFalse()
        }
    }

    @Test
    fun markAsSeen() = runTest {
        fakeWorkoutHeader.run {
            workoutHeaderDao.insert(copy(seen = false))
            workoutHeaderDao.markAsSeen(key!!)
            assertThat(workoutHeaderDao.findById(id)).isEqualTo(copy(seen = true))
        }
    }

    @Test
    fun takeOwnershipOfUnsyncedWorkouts() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, key = null),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            workoutHeaderDao.takeOwnershipOfUnsyncedWorkouts("newuser")
            assertThat(workoutHeaderDao.findById(id + 1)!!.username).isEqualTo("newuser")
        }
    }

    @Test
    fun findWithDistanceBetween() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, totalDistance = totalDistance + 10),
                copy(id + 2, totalDistance = totalDistance + 20),
                copy(id + 3, totalDistance = totalDistance + 30),
                copy(id + 4, totalDistance = totalDistance + 40),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.findWithDistanceBetween(
                setOf(username),
                setOf(activityId),
                totalDistance,
                totalDistance + 20
            )
            assertThat(result).isEqualTo(testList.subList(0, 3))
        }
    }

    @Test
    fun findWithStartTime() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 10, activityId = activityId + 1),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.findWithStartTime(activityId, startTime = startTime + 10)
            assertThat(result).containsExactly(testList[1])
        }
    }

    @Test
    fun findNotDeletedWorkouts() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20, activityId = activityId + 1),
                copy(id + 3, startTime = startTime + 30, deleted = true),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.findNotDeletedWorkouts(username, null)
            assertThat(result).isEqualTo((testList - testList.last()).reversed())

            val result2 = workoutHeaderDao.findNotDeletedWorkouts(username, activityId)
            assertThat(result2).containsExactly(testList[1], testList[0])
        }
    }

    @Test
    fun count() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20, deleted = true),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.count(username)
            assertThat(result).isEqualTo(2)
        }
    }

    @Test
    fun syncedWorkoutsCount() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10, key = null),
                copy(id + 2, startTime = startTime + 20, deleted = true),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.syncedWorkoutsCount(username)
            assertThat(result).isEqualTo(1)
        }
    }

    @Test
    fun any() = runTest {
        fakeWorkoutHeader.run {
            workoutHeaderDao.insert(this)
            val result = workoutHeaderDao.any(username)
            assertThat(result).isEqualTo(this)
        }
    }

    @Test
    fun loadWorkouts() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10, activityId = activityId + 1),
                copy(id + 2, startTime = startTime + 20, deleted = true),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.loadWorkouts(username, activityId)
            assertThat(result).containsExactly(testList[0])
        }
    }

    @Test
    fun loadWorkoutsTSSSummary() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10, activityId = activityId + 1),
                copy(id + 2, startTime = startTime + 20, deleted = true),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.loadWorkoutsTSSSummary(username, startTime + 10)
            assertThat(result).containsExactly(
                LocalWorkoutTSSSummary(
                    id = id + 1,
                    key = key!!,
                    username = username,
                    startTime = startTime + 10,
                    endTime = stopTime,
                    tss = tss!!,
                    activityId = activityId + 1
                ),
            )
        }
    }

    @Test
    fun countUnseenWorkouts() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20, seen = false),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.countUnseenWorkouts(username)
            assertThat(result).isEqualTo(1)
        }
    }

    @Test
    fun countOfActivityId() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20, activityId = activityId + 1),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.countOfActivityId(username, activityId)
            assertThat(result).isEqualTo(2)
        }
    }

    @Test
    fun loadFastestOfActivityTypeInPeriod() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                copy(avgSpeed = avgSpeed + 3),
                copy(id + 1, startTime = startTime + 10, avgSpeed = avgSpeed + 1),
                copy(id + 2, startTime = startTime + 20, avgSpeed = avgSpeed + 2),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.loadFastestOfActivityTypeInPeriod(
                username = username,
                activityTypeId = activityId,
                since = startTime + 10,
                until = startTime + 20 // exlusive
            )
            assertThat(result).isEqualTo(testList[1])
        }
    }

    @Test
    fun loadFarthestOfActivityTypeInPeriod() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                copy(totalDistance = totalDistance + 3),
                copy(id + 1, startTime = startTime + 10, totalDistance = totalDistance + 1),
                copy(id + 2, startTime = startTime + 20, totalDistance = totalDistance + 2),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.loadFarthestOfActivityTypeInPeriod(
                username = username,
                activityTypeId = activityId,
                since = startTime + 10,
                until = startTime + 20 // exclusive
            )
            assertThat(result).isEqualTo(testList[1])
        }
    }

    @Test
    fun countActivitiesInPeriod() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.countActivitiesInPeriod(
                username = username,
                activityTypeId = activityId,
                since = startTime + 10,
                until = startTime + 20
            )
            assertThat(result).isEqualTo(2)
        }
    }

    @Test
    fun loadLatestOfActivityType() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.loadLatestOfActivityType(
                username = username,
                activityTypeId = activityId,
                until = startTime + 20 // exlusive
            )
            assertThat(result).isEqualTo(testList[1])
        }
    }

    @Test
    fun loadLatestWorkoutsOfActivityTypes() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.loadLatestWorkoutsOfActivityTypes(
                username = username,
                activityTypes = listOf(activityId),
                maxCount = 1,
                minDurationSeconds = 1,
                since = startTime,
                until = startTime + 20
            )
            assertThat(result).containsExactly(testList[2])
        }
    }

    @Test
    fun loadLatestWorkoutsOfActivityTypesWithNullList() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                this,
                copy(id + 1, startTime = startTime + 10),
                copy(id + 2, startTime = startTime + 20),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            val result = workoutHeaderDao.loadLatestWorkoutsOfActivityTypes(
                username = username,
                activityTypes = null,
                maxCount = 1,
                minDurationSeconds = 1,
                since = startTime,
                until = startTime + 20
            )
            assertThat(result).containsExactly(testList[2])
        }
    }

    @Test
    fun givenStartAndEndDate_getAllCommuteWorkouts_shouldReturnWorkoutsWithinTheTwoDates() =
        runTest {
            fakeWorkoutHeader.run {
                val workoutsList = listOf(
                    copy(id, startTime = 10, suuntoTags = listOf(LocalSuuntoTag.COMMUTE)),
                    copy(id + 1, startTime = 20, suuntoTags = listOf(LocalSuuntoTag.COMMUTE)),
                    copy(id + 2, startTime = 30, suuntoTags = null),
                    copy(id + 3, startTime = 40, suuntoTags = listOf(LocalSuuntoTag.COMMUTE)),
                )
                workoutsList.forEach { workoutHeaderDao.insert(it) }
                val result = workoutHeaderDao.getAllCommuteWorkouts(username, 15, 35).first()

                assertThat(result).containsExactly(workoutsList[1])
            }
        }

    @Test
    fun getAllCommuteWorkouts_shouldReturnAllWorkoutsIfRangeDateNotGiven() =
        runTest {
            fakeWorkoutHeader.run {
                val workoutsList = listOf(
                    copy(id, startTime = 10, suuntoTags = listOf(LocalSuuntoTag.COMMUTE)),
                    copy(id + 1, startTime = 20, suuntoTags = listOf(LocalSuuntoTag.COMMUTE)),
                    copy(id + 2, startTime = 30, suuntoTags = null),
                    copy(id + 3, startTime = 40, suuntoTags = listOf(LocalSuuntoTag.COMMUTE)),
                )
                workoutsList.forEach { workoutHeaderDao.insert(it) }
                val result = workoutHeaderDao.getAllCommuteWorkouts(username, null, null).first()

                assertThat(result).containsExactly(
                    workoutsList[0],
                    workoutsList[1],
                    workoutsList[3]
                )
            }
        }

    @Test
    fun findWorkoutsWithUnsyncedUserTags_shouldGiveOnlyReadyToSyncWorkoutHeaders() =
        runTest {
            val userTag = TagsTestUtils.createLocalUserTag(id = 1, key = "key")
            val localWorkoutHeader1 = fakeWorkoutHeader.copy(id = 1)
            val localWorkoutHeader2 = fakeWorkoutHeader.copy(id = 2)
            val localWorkoutHeader3 = fakeWorkoutHeader.copy(id = 3)
            val localWorkoutHeader4 = fakeWorkoutHeader.copy(id = 4, key = null)

            //
            workoutHeaderDao.insert(localWorkoutHeader1)
            workoutHeaderDao.insert(localWorkoutHeader2)
            workoutHeaderDao.insert(localWorkoutHeader3)
            workoutHeaderDao.insert(localWorkoutHeader4)
            val userTagLocalId = userTagsDao.insert(userTag)

            userTagsDao.insertUserTagWorkoutHeadersCrossRef(
                TagsTestUtils.createLocalUserTagWorkoutHeaderCrossRef(
                    workoutId = localWorkoutHeader1.id,
                    userTagId = userTagLocalId.toInt(),
                    isSynced = true,
                    isRemoved = false
                )
            )

            userTagsDao.insertUserTagWorkoutHeadersCrossRef(
                TagsTestUtils.createLocalUserTagWorkoutHeaderCrossRef(
                    workoutId = localWorkoutHeader2.id,
                    userTagId = userTagLocalId.toInt(),
                    isSynced = true,
                    isRemoved = true
                )
            )
            userTagsDao.insertUserTagWorkoutHeadersCrossRef(
                TagsTestUtils.createLocalUserTagWorkoutHeaderCrossRef(
                    workoutId = localWorkoutHeader3.id,
                    userTagId = userTagLocalId.toInt(),
                    isSynced = false,
                    isRemoved = false
                )
            )

            userTagsDao.insertUserTagWorkoutHeadersCrossRef(
                TagsTestUtils.createLocalUserTagWorkoutHeaderCrossRef(
                    workoutId = localWorkoutHeader4.id,
                    userTagId = userTagLocalId.toInt(),
                    isSynced = false,
                    isRemoved = false
                )
            )

            //
            val result = workoutHeaderDao.findWorkoutsWithUnsyncedUserTags()
            assertThat(result).containsExactlyEntriesIn(
                mapOf(
                    localWorkoutHeader2 to listOf(userTag),
                    localWorkoutHeader3 to listOf(userTag)
                )
            )
        }

    @Test
    fun getAllWorkoutsForComparisonMustReturnAllActivitiesWhenActivityIdsParamIsNotNull() =
        runTest {
            fakeWorkoutHeader.run {
                val testList = listOf(
                    copy(id, startTime = startTime, activityId = 1),
                    copy(id + 1, startTime = startTime + 10, activityId = 2),
                    copy(id + 2, startTime = startTime + 20, activityId = 3),
                )
                testList.forEach { workoutHeaderDao.insert(it) }
                val result = workoutHeaderDao.getAllWorkoutsForSummary(
                    username = username,
                    activityIds = listOf(1),
                    minStartTimeInclusive = null,
                    maxStartTimeInclusive = null,
                    minTotalDistanceInclusive = null,
                    maxTotalDistanceInclusive = null,
                    suuntoTags = null,
                    userTags = null,

                    ).test {
                    val result = expectMostRecentItem()
                    assertThat(result).hasSize(1)
                    assertThat(result[0].activityId).isEqualTo(1)
                }
            }
        }

    @Test
    fun getAllWorkoutsForComparisonMustReturnAllActivitiesWhenActivityIdsParamIsNull() = runTest {
        fakeWorkoutHeader.run {
            val testList = listOf(
                copy(id, startTime = startTime, activityId = 1),
                copy(id + 1, startTime = startTime + 10, activityId = 2),
                copy(id + 2, startTime = startTime + 20, activityId = 3),
            )
            testList.forEach { workoutHeaderDao.insert(it) }
            workoutHeaderDao.getAllWorkoutsForSummary(
                username = username,
                activityIds = null,
                minStartTimeInclusive = null,
                maxStartTimeInclusive = null,
                minTotalDistanceInclusive = null,
                maxTotalDistanceInclusive = null,
                suuntoTags = null,
                userTags = null,
            ).test {
                val result = expectMostRecentItem()
                assertThat(result).hasSize(3)
            }
        }
    }

    @Test
    fun getAllWorkoutsForComparisonMustReturnAllActivitiesWithinSpecifiedMinAndMaxDistance() =
        runTest {
            fakeWorkoutHeader.run {
                val testList = listOf(
                    copy(id = id, totalDistance = 5000.0, startTime = 1),
                    copy(id = id + 1, totalDistance = 4000.0, startTime = 2),
                    copy(id = id + 2, totalDistance = 1000.0, startTime = 3),
                )
                testList.forEach { workoutHeaderDao.insert(it) }
                workoutHeaderDao.getAllWorkoutsForSummary(
                    username = username,
                    activityIds = null,
                    minStartTimeInclusive = null,
                    maxStartTimeInclusive = null,
                    minTotalDistanceInclusive = 2000.0,
                    maxTotalDistanceInclusive = 5000.0,
                    suuntoTags = null,
                    userTags = null,
                ).test {
                    val result = expectMostRecentItem()
                    assertThat(result).hasSize(2)
                    assertThat(result[0].id).isEqualTo(fakeWorkoutHeader.id + 1)
                    assertThat(result[1].id).isEqualTo(fakeWorkoutHeader.id)
                }
            }
        }

    @Test
    fun getAllWorkoutsForComparison_commuteMustCheckedFromTheIsCommuteColumnAndNotFromImpactCardioAndImpactMuscularColumns() =
        runTest {
            fakeWorkoutHeader.run {
                listOf(
                    copy(id = id, suuntoTags = listOf(LocalSuuntoTag.COMMUTE), startTime = 1),
                    copy(
                        id = id + 1,
                        suuntoTags = null,
                        startTime = 2
                    ),
                    copy(id = id + 2, suuntoTags = listOf(LocalSuuntoTag.COMMUTE), startTime = 3),
                ).run {
                    forEach { workoutHeaderDao.insert(it) }
                }
            }
            workoutHeaderDao.getAllWorkoutsForSummary(
                username = fakeWorkoutHeader.username,
                activityIds = null,
                minStartTimeInclusive = null,
                maxStartTimeInclusive = null,
                minTotalDistanceInclusive = null,
                maxTotalDistanceInclusive = null,
                suuntoTags = listOf(LocalSuuntoTag.COMMUTE),
                userTags = null,
            ).test {
                val result = expectMostRecentItem()
                assertThat(result).hasSize(2)
                assertThat(result[0].id).isEqualTo(fakeWorkoutHeader.id + 2)
                assertThat(result[1].id).isEqualTo(fakeWorkoutHeader.id)
            }
        }

    @Test
    fun getAllWorkoutsForComparison_whenCommuteAndImpactTagAreSelected_mustReturnWorkoutsWithCommuteOrImpactTag() =
        runTest {
            fakeWorkoutHeader.run {
                listOf(
                    copy(id = id, suuntoTags = listOf(LocalSuuntoTag.COMMUTE), startTime = 1),
                    copy(
                        id = id + 1,
                        suuntoTags = listOf(LocalSuuntoTag.IMPACT_AEROBIC),
                        startTime = 2
                    ),
                    copy(
                        id = id + 2,
                        suuntoTags = listOf(LocalSuuntoTag.IMPACT_LONG_AEROBIC_BASE, LocalSuuntoTag.COMMUTE),
                        startTime = 3
                    ),
                    copy(
                        id = id + 3,
                        suuntoTags = null,
                        startTime = 4
                    ),
                ).run {
                    forEach { workoutHeaderDao.insert(it) }
                }
            }
            workoutHeaderDao.getAllWorkoutsForSummary(
                username = fakeWorkoutHeader.username,
                activityIds = null,
                minStartTimeInclusive = null,
                maxStartTimeInclusive = null,
                minTotalDistanceInclusive = null,
                maxTotalDistanceInclusive = null,
                suuntoTags = listOf(LocalSuuntoTag.COMMUTE, LocalSuuntoTag.IMPACT_AEROBIC),
                userTags = null,
            ).test {
                val result = expectMostRecentItem()
                assertThat(result).hasSize(3)
                assertThat(result[0].id).isEqualTo(fakeWorkoutHeader.id + 2)
                assertThat(result[1].id).isEqualTo(fakeWorkoutHeader.id + 1)
                assertThat(result[2].id).isEqualTo(fakeWorkoutHeader.id)
            }
        }

    @Test
    fun getAllWorkoutsForComparison_whenImpactTagIsSelected_mustReturnWorkoutsWithImpactTag() =
        runTest {
            fakeWorkoutHeader.run {
                listOf(
                    copy(
                        id = id + 1,
                        suuntoTags = listOf(LocalSuuntoTag.IMPACT_AEROBIC),
                        startTime = 1
                    ),
                    copy(
                        id = id + 2,
                        suuntoTags = listOf(LocalSuuntoTag.IMPACT_AEROBIC, LocalSuuntoTag.IMPACT_LONG_AEROBIC_BASE),
                        startTime = 2
                    ),
                    copy(
                        id = id + 3,
                        suuntoTags = null,
                        startTime = 3
                    )
                ).run {
                    forEach { workoutHeaderDao.insert(it) }
                }
            }
            workoutHeaderDao.getAllWorkoutsForSummary(
                username = fakeWorkoutHeader.username,
                activityIds = null,
                minStartTimeInclusive = null,
                maxStartTimeInclusive = null,
                minTotalDistanceInclusive = null,
                maxTotalDistanceInclusive = null,
                suuntoTags = listOf(
                    LocalSuuntoTag.IMPACT_LONG_AEROBIC_BASE,
                    LocalSuuntoTag.IMPACT_AEROBIC,
                    LocalSuuntoTag.IMPACT_EASY_RECOVERY
                ),
                userTags = null,
            ).test {
                val result = expectMostRecentItem()
                assertThat(result).hasSize(2)
                assertThat(result[0].id).isEqualTo(fakeWorkoutHeader.id + 2)
                assertThat(result[1].id).isEqualTo(fakeWorkoutHeader.id + 1)
            }
        }

    @Test
    fun getAllWorkoutsForComparison_whenImpactTagIsSelectedAnsUserHasNoWorkouts_mustReturnAnEmptyList() =
        runTest {
            workoutHeaderDao.getAllWorkoutsForSummary(
                username = fakeWorkoutHeader.username,
                activityIds = null,
                minStartTimeInclusive = null,
                maxStartTimeInclusive = null,
                minTotalDistanceInclusive = null,
                maxTotalDistanceInclusive = null,
                suuntoTags = listOf(
                    LocalSuuntoTag.IMPACT_LONG_AEROBIC_BASE,
                    LocalSuuntoTag.IMPACT_AEROBIC,
                    LocalSuuntoTag.IMPACT_EASY_RECOVERY
                ),
                userTags = null,
            ).test {
                val result = expectMostRecentItem()
                assertThat(result).isEmpty()
            }
        }

    @Test
    fun getAllWorkoutsForComparison_whenImpactTagIsSelected_ButNoWorkoutMatches_mustReturnAnEmptyList() =
        runTest {
            fakeWorkoutHeader.run {
                listOf(
                    copy(
                        id = id + 1,
                        suuntoTags = listOf(LocalSuuntoTag.IMPACT_AEROBIC),
                    ),
                    copy(
                        id = id + 2,
                        suuntoTags = listOf(LocalSuuntoTag.IMPACT_AEROBIC, LocalSuuntoTag.IMPACT_LONG_AEROBIC_BASE),
                    ),
                    copy(
                        id = id + 3,
                        suuntoTags = null,
                    )
                ).run {
                    forEach { workoutHeaderDao.insert(it) }
                }
            }
            workoutHeaderDao.getAllWorkoutsForSummary(
                username = fakeWorkoutHeader.username,
                activityIds = null,
                minStartTimeInclusive = null,
                maxStartTimeInclusive = null,
                minTotalDistanceInclusive = null,
                maxTotalDistanceInclusive = null,
                suuntoTags = listOf(
                    LocalSuuntoTag.IMPACT_EASY_RECOVERY
                ),
                userTags = null,
            ).test {
                val result = expectMostRecentItem()
                assertThat(result).isEmpty()
            }
        }

    @Test
    fun getAllWorkoutsForComparison_whenImpactTagIsSelectedAndUserTagIsSelected_mustReturnWorkoutsWithThatContainsAtLeastOneOfThem() =
        runTest {
            fakeWorkoutHeader.run {
                listOf(
                    copy(
                        id = id,
                        suuntoTags = listOf(LocalSuuntoTag.IMPACT_AEROBIC),
                        startTime = 1
                    ),
                    copy(
                        id = id + 1,
                        suuntoTags = listOf(LocalSuuntoTag.IMPACT_AEROBIC, LocalSuuntoTag.IMPACT_LONG_AEROBIC_BASE),
                        startTime = 2
                    ),
                    copy(
                        id = id + 2,
                        suuntoTags = null,
                        startTime = 3
                    )
                ).run {
                    forEach { workoutHeaderDao.insert(it) }
                }
            }

            val userTag1 = TagsTestUtils.createLocalUserTag(id = 1, key = "key1")
            val userTag1LocalId = userTagsDao.insert(userTag1)

            userTagsDao.insertUserTagWorkoutHeadersCrossRef(
                TagsTestUtils.createLocalUserTagWorkoutHeaderCrossRef(
                    workoutId = fakeWorkoutHeader.id,
                    userTagId = userTag1LocalId.toInt(),
                )
            )

            userTagsDao.insertUserTagWorkoutHeadersCrossRef(
                TagsTestUtils.createLocalUserTagWorkoutHeaderCrossRef(
                    workoutId = fakeWorkoutHeader.id + 1,
                    userTagId = userTag1LocalId.toInt(),
                    isSynced = true,
                    isRemoved = true
                )
            )

            workoutHeaderDao.getAllWorkoutsForSummary(
                username = fakeWorkoutHeader.username,
                activityIds = null,
                minStartTimeInclusive = null,
                maxStartTimeInclusive = null,
                minTotalDistanceInclusive = null,
                maxTotalDistanceInclusive = null,
                suuntoTags = listOf(
                    LocalSuuntoTag.IMPACT_LONG_AEROBIC_BASE,
                ),
                userTags = listOf(userTag1),
            ).test {
                val result = expectMostRecentItem()
                assertThat(result).hasSize(2)
                assertThat(result[0].id).isEqualTo(fakeWorkoutHeader.id + 1)
                assertThat(result[1].id).isEqualTo(fakeWorkoutHeader.id)
            }
        }

    @Test
    fun getAllWorkoutsForComparison_MustReturnAllWorkoutsIfNoFilterIsSelected() =
        runTest {
            fakeWorkoutHeader.run {
                listOf(
                    copy(id = id + 1),
                    copy(id = id + 2),
                    copy(id = id + 3)
                ).run {
                    forEach { workoutHeaderDao.insert(it) }
                }
            }
            workoutHeaderDao.getAllWorkoutsForSummary(
                username = fakeWorkoutHeader.username,
                activityIds = null,
                minStartTimeInclusive = null,
                maxStartTimeInclusive = null,
                minTotalDistanceInclusive = null,
                maxTotalDistanceInclusive = null,
                suuntoTags = emptyList(),
                userTags = emptyList(),
            ).test {
                val result = expectMostRecentItem()
                assertThat(result).hasSize(3)
            }
        }

    @Test
    fun getAllWorkoutsForComparison_whenWorkoutContainsMultipleUserTags_onlyOneWorkoutIsReturned() =
        runTest {
            workoutHeaderDao.insert(
                fakeWorkoutHeader.copy(
                    suuntoTags = null,
                )
            )

            val userTag1 = TagsTestUtils.createLocalUserTag(id = 1, key = "key1")
            val userTag2 = TagsTestUtils.createLocalUserTag(id = 2, key = "key2")
            val userTag1LocalId = userTagsDao.insert(userTag1)
            val userTag2LocalId = userTagsDao.insert(userTag2)

            userTagsDao.insertUserTagWorkoutHeadersCrossRef(
                TagsTestUtils.createLocalUserTagWorkoutHeaderCrossRef(
                    workoutId = fakeWorkoutHeader.id,
                    userTagId = userTag1LocalId.toInt(),
                )
            )

            userTagsDao.insertUserTagWorkoutHeadersCrossRef(
                TagsTestUtils.createLocalUserTagWorkoutHeaderCrossRef(
                    workoutId = fakeWorkoutHeader.id,
                    userTagId = userTag2LocalId.toInt(),
                )
            )

            workoutHeaderDao.getAllWorkoutsForSummary(
                username = fakeWorkoutHeader.username,
                activityIds = null,
                minStartTimeInclusive = null,
                maxStartTimeInclusive = null,
                minTotalDistanceInclusive = null,
                maxTotalDistanceInclusive = null,
                suuntoTags = null,
                userTags = listOf(userTag1, userTag2),
            ).test {
                val result = expectMostRecentItem()
                assertThat(result).hasSize(1)
                assertThat(result[0].id).isEqualTo(fakeWorkoutHeader.id)
            }
        }

    @Test
    fun getAllWorkoutsForSummary_whenUSerTagIsUsedInMultipleWorkouts_allOfThemMustBeReturned() =
        runTest {
            val workout1 = fakeWorkoutHeader.copy(startTime = 1)
            val workout2 = fakeWorkoutHeader.copy(id = fakeWorkoutHeader.id + 1, startTime = 2)
            workoutHeaderDao.insert(workout1)
            workoutHeaderDao.insert(workout2)

            val userTag1 = TagsTestUtils.createLocalUserTag(id = 1, key = "key1", name = "name1")
            val userTag2 = TagsTestUtils.createLocalUserTag(id = 2, key = "key2", name = "name2")
            val userTag1LocalId = userTagsDao.insert(userTag1)
            val userTag2LocalId = userTagsDao.insert(userTag2)

            userTagsDao.insertUserTagWorkoutHeadersCrossRef(
                TagsTestUtils.createLocalUserTagWorkoutHeaderCrossRef(
                    workoutId = workout1.id,
                    userTagId = userTag1LocalId.toInt(),
                )
            )

            userTagsDao.insertUserTagWorkoutHeadersCrossRef(
                TagsTestUtils.createLocalUserTagWorkoutHeaderCrossRef(
                    workoutId = workout1.id,
                    userTagId = userTag2LocalId.toInt(),
                )
            )

            userTagsDao.insertUserTagWorkoutHeadersCrossRef(
                TagsTestUtils.createLocalUserTagWorkoutHeaderCrossRef(
                    workoutId = workout2.id,
                    userTagId = userTag2LocalId.toInt(),
                )
            )

            workoutHeaderDao.getAllWorkoutsForSummary(
                username = fakeWorkoutHeader.username,
                activityIds = null,
                minStartTimeInclusive = null,
                maxStartTimeInclusive = null,
                minTotalDistanceInclusive = null,
                maxTotalDistanceInclusive = null,
                suuntoTags = null,
                userTags = listOf(userTag2),
            ).test {
                val result = expectMostRecentItem()
                assertThat(result).hasSize(2)
                assertThat(result[0].id).isEqualTo(workout2.id)
                assertThat(result[1].id).isEqualTo(workout1.id)
            }
        }
}
