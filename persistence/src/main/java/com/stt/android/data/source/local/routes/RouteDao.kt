package com.stt.android.data.source.local.routes

import android.database.Cursor
import androidx.annotation.VisibleForTesting
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.stt.android.coroutines.runSuspendCatching
import kotlinx.coroutines.flow.Flow
import timber.log.Timber

@Dao
abstract class RouteDao {
    @VisibleForTesting
    @Query("SELECT * FROM routes")
    internal abstract fun selectAll(): List<LocalRoute>

    @VisibleForTesting
    @Query("SELECT * FROM routes WHERE `key` = :key")
    internal abstract fun fetchByKey(key: String): LocalRoute?

    @VisibleForTesting
    @Query("UPDATE routes SET deleted = 1, locallyChanged = 1")
    abstract fun markAllDeleted()

    @Insert
    internal abstract fun insert(route: LocalRoute)

    @Update
    internal abstract fun update(route: LocalRoute): Int

    @Query("SELECT * FROM routes WHERE _id = :routeId")
    abstract suspend fun fetchById(routeId: String): LocalRoute?

    @Query("SELECT * FROM routes WHERE isInProgress = 0 ORDER BY created DESC")
    abstract fun fetchAll(): Flow<List<LocalRoute>>

    @Query(
        """
        SELECT
            ${LocalRoute.ID},
            ${LocalRoute.KEY},
            ${LocalRoute.OWNER_USER_NAME},
            ${LocalRoute.NAME},
            ${LocalRoute.VISIBILITY},
            ${LocalRoute.ACTIVITY_IDS},
            ${LocalRoute.AVERAGE_SPEED},
            ${LocalRoute.TOTAL_DISTANCE},
            ${LocalRoute.ASCENT},
            ${LocalRoute.DESCENT},
            ${LocalRoute.START_POINT},
            ${LocalRoute.CENTER_POINT},
            ${LocalRoute.STOP_POINT},
            ${LocalRoute.LOCALLY_CHANGED},
            ${LocalRoute.DELETED},
            ${LocalRoute.CREATED_DATE},
            ${LocalRoute.MODIFIED_DATE},
            ${LocalRoute.SEGMENTS_MODIFIED_DATE},
            ${LocalRoute.WATCH_SYNC_STATE},
            ${LocalRoute.WATCH_SYNC_RESPONSE_CODE},
            ${LocalRoute.WATCH_ROUTE_ID},
            ${LocalRoute.WATCH_ENABLED},
            ${LocalRoute.IS_IN_PROGRESS},
            ${LocalRoute.TURN_WAYPOINTS_ENABLED},
            ${LocalRouteProducer.PRODUCER_ID},
            ${LocalRouteProducer.PRODUCER_NAME},
            ${LocalRouteProducer.PRODUCER_ICON_URL},
            ${LocalRoute.EXTERNAL_URL},
            x'' AS ${LocalRoute.SEGMENTS}
        FROM routes
        WHERE isInProgress = 0
        ORDER BY created DESC
        """
    )
    abstract fun fetchAllWithoutSegments(): Flow<List<LocalRoute>>

    @Query("SELECT * FROM routes WHERE isInProgress = 0 AND deleted = 0")
    abstract fun fetchAllCursor(): Cursor

    @Query("""
        SELECT ${LocalRoute.WATCH_ROUTE_ID}
        FROM routes
        WHERE
            ${LocalRoute.IS_IN_PROGRESS} = 0 AND
            ${LocalRoute.DELETED} = 0 AND
            ${LocalRoute.WATCH_ENABLED} != 0
    """)
    abstract fun fetchWatchEnabledRouteIdsCursor(): Cursor

    @Query("SELECT MAX(${LocalRoute.WATCH_ROUTE_ID}) FROM routes")
    abstract fun fetchLatestWatchRouteIdCursor(): Cursor

    @Query("""
        SELECT *
        FROM routes
        WHERE
            ${LocalRoute.IS_IN_PROGRESS} = 0 AND
            ${LocalRoute.DELETED} = 0 AND
            ${LocalRoute.WATCH_SYNC_STATE} IN (:states)
    """)
    abstract fun fetchRoutesWithStatesCursor(states: Array<String>): Cursor

    @Query("SELECT * FROM routes WHERE locallyChanged = 1 AND isInProgress = 0")
    abstract suspend fun fetchUnsynced(): List<LocalRoute>

    @Query(
        """
        SELECT COUNT(_id)
        FROM routes
        WHERE deleted = 0 AND isInProgress = 0
    """
    )
    abstract suspend fun getRouteCount(): Int

    /**
     * Get the number of routes in the database including deleted ones.
     * This method is used for migrations.
     */
    @Query(
        """
        SELECT COUNT(_id)
        FROM routes
    """
    )
    abstract fun getRoutesCountIncludingDeleted(): Int

    @Query(
        """
        SELECT * FROM routes
        WHERE deleted = 0 AND watchEnabled = 1 AND watchSyncState != 'IGNORED'
        """
    )
    abstract suspend fun fetchAllWatchEnabled(): List<LocalRoute>

    @Query(
        """
        SELECT COUNT(_id)
        FROM routes
        WHERE deleted = 0 AND watchEnabled = 1 AND isInProgress = 0
    """
    )
    abstract suspend fun getWatchEnabledRouteCount(): Int

    @Query(
        """
        UPDATE routes
        SET deleted = 1, locallyChanged = 1, modifiedDate = :modifiedDate, watchSyncState = :watchSyncState
        WHERE _id = :routeId
    """
    )
    abstract suspend fun markDeleted(routeId: String, modifiedDate: Long, watchSyncState: String)

    open suspend fun markDeleted(route: LocalRoute) {
        markDeleted(route.id, System.currentTimeMillis(), route.watchSyncState)
    }

    @Query(
        """
        UPDATE routes
        SET watchEnabled = 0, locallyChanged = 1, modifiedDate = :modifiedDate, watchSyncState = 'IGNORED'
        WHERE watchRouteId = :watchRouteId
    """
    )
    abstract fun disableWatchSync(watchRouteId: Int, modifiedDate: Long)

    open fun disableWatchSync(routeIds: List<Int>) {
        routeIds.forEach { disableWatchSync(it, System.currentTimeMillis()) }
    }

    @Query("DELETE FROM routes WHERE _id = :routeId")
    abstract fun delete(routeId: String)

    @Query(
        """
        DELETE
        FROM routes
    """
    )
    abstract fun deleteAll()

    @Query("DELETE FROM routes WHERE isInProgress = 1")
    abstract suspend fun deleteRoutesInProgress()

    /**
     * Update existing [route] or insert if it doesn't exist.
     * The operation is executed within a transaction.
     */
    @Transaction
    open suspend fun upsert(route: LocalRoute) {
        doUpsert(route)
    }

    /**
     * Update existing [routes] or insert if don't exist.
     * The operation is executed within a transaction.
     */
    @Transaction
    open suspend fun upsert(routes: List<LocalRoute>) {
        routes.forEach { route ->
            doUpsert(route)
        }
    }

    /**
     * Returns true if route is modified so that we should update the [LocalRoute.modifiedDate] timestamp
     */
    private fun hasModifications(newRoute: LocalRoute, existingRoute: LocalRoute): Boolean =
        newRoute.watchRouteId != existingRoute.watchRouteId ||
            newRoute.ownerUserName != existingRoute.ownerUserName ||
            newRoute.name != existingRoute.name ||
            newRoute.visibility != existingRoute.visibility ||
            newRoute.activityIds != existingRoute.activityIds ||
            newRoute.averageSpeed != existingRoute.averageSpeed ||
            newRoute.totalDistance != existingRoute.totalDistance ||
            newRoute.startPoint != existingRoute.startPoint ||
            newRoute.centerPoint != existingRoute.centerPoint ||
            newRoute.stopPoint != existingRoute.stopPoint ||
            newRoute.deleted != existingRoute.deleted ||
            newRoute.createdDate != existingRoute.createdDate ||
            newRoute.segments != existingRoute.segments ||
            newRoute.turnWaypointsEnabled != existingRoute.turnWaypointsEnabled ||
            newRoute.producer != existingRoute.producer ||
            newRoute.externalUrl != existingRoute.externalUrl

    private suspend fun doUpsert(route: LocalRoute) {
        // All routes created locally have unique ID, but routes that come from server
        // do not have local IDs
        val existingRoute = if (route.fromServer) {
            fetchByKey(route.key)
        } else {
            runSuspendCatching {
                fetchById(route.id)
            }.getOrElse { e ->
                Timber.w(e, "Route with ID = %s not found, proceed with insert", route.id)
                null
            }
        }

        if (existingRoute != null) {
            Timber.d("Updating existing route: id=${existingRoute.id} watchRouteId=${existingRoute.watchRouteId} key=${existingRoute.key} segments.size=${existingRoute.segments.size}")
            // Refuse to clear the remote key, if the existing route already has it
            val key = route.key.takeUnless { it.isEmpty() } ?: existingRoute.key
            val hasModifications = hasModifications(route, existingRoute)
            // segmentsModifiedDate is stored only locally, LocalRoutes not originating from local DB
            // will most of the time have it same as modifiedDate. So we'll never want to use
            // the one from the updated route, keep existing or update to current timestamp
            val segmentsModifiedDate = if (route.segments != existingRoute.segments) {
                System.currentTimeMillis()
            } else {
                existingRoute.segmentsModifiedDate
            }

            val modifiedDate = if (hasModifications) {
                System.currentTimeMillis()
            } else if (route.fromServer) {
                existingRoute.modifiedDate
            } else {
                route.modifiedDate
            }

            update(
                route.copy(
                    id = existingRoute.id,
                    key = key,
                    modifiedDate = modifiedDate,
                    segmentsModifiedDate = segmentsModifiedDate
                )
            )
        } else {
            val newId = route.id.ifEmpty { LocalRoute.generateId() }
            // Re-sync routes not previously in app DB with watch
            val newWatchSyncState = if (route.watchEnabled) "PENDING" else route.watchSyncState
            val newWatchSyncResponseCode =
                if (newWatchSyncState == "PENDING") 0 else route.watchSyncResponseCode

            val newRoute = route.copy(
                id = newId,
                watchSyncState = newWatchSyncState,
                watchSyncResponseCode = newWatchSyncResponseCode,
                modifiedDate = route.modifiedDate.takeIf { it > 0L } ?: route.createdDate,
            )
            Timber.d("Inserting a new route: id=${newRoute.id} watchRouteId=${newRoute.watchRouteId} key=${newRoute.key} segments.size=${newRoute.segments.size}")
            insert(newRoute)
        }
    }
}
