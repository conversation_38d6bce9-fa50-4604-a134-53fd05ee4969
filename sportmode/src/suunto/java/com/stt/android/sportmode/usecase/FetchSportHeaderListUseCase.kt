package com.stt.android.sportmode.usecase

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workout.ActivityType
import com.stt.android.sportmode.datasource.RunSportModesApi
import com.stt.android.sportmode.home.SportHeader
import com.stt.android.sportmode.home.mapSportHeader
import com.stt.android.watch.MissingCurrentWatchException
import timber.log.Timber
import javax.inject.Inject

class FetchSportHeaderListUseCase @Inject constructor(
    private val runSportModesApi: RunSportModesApi
) {
    private fun logWarning(throwable: Throwable, message: String) {
        if (throwable is MissingCurrentWatchException) {
            Timber.i("$message, Missing current watch")
        } else {
            Timber.w(throwable, message)
        }
    }

    suspend fun fetchAll(): List<SportHeader> {
        val entities = runSuspendCatching {
            runSportModesApi.fetchAllSportItems()
        }.onFailure {
            logWarning(it, "fetch all sport items error")
        }.getOrDefault(emptyList())
        val allActivities = ActivityType.values()
        return entities.map { entity ->
            val activity = allActivities.first { it.id == entity.sportId }
            activity.mapSportHeader(
                modeCount = entity.trainingModeNum
            )
        }.filtered()
    }

    suspend fun fetchRecent(all: List<SportHeader>): List<SportHeader> {
        val ids = runSuspendCatching {
            runSportModesApi.fetchRecentSportIds()
        }.onFailure {
            logWarning(it, "fetch recent sport items error")
        }.getOrDefault(emptyList())
        return ids.mapNotNull { id ->
            all.firstOrNull { it.id == id }
        }.filtered()
    }

    private fun List<SportHeader>.filtered(): List<SportHeader> =
        filterNot { it.activityType == ActivityType.TRIATHLON } // Triathlon is not supported for now.
            .filterNot { it.activityType == ActivityType.MULTISPORT } // Multi sport is not supported
}
