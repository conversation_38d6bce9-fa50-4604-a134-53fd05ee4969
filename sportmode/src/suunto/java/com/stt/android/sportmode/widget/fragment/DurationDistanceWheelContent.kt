package com.stt.android.sportmode.widget.fragment

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bottomSheetShape
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberViewInteropNestedScrollConnection
import com.stt.android.compose.widgets.DraggableBottomSheetHandle
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.home.settings.wheel.WheelFragmentColumn
import com.stt.android.sportmode.modesetting.list.RadioButtonItem
import com.stt.android.sportmode.modesetting.list.RadioButtonItemView
import com.stt.android.sportmode.modesetting.sportStr
import com.stt.android.home.settings.wheel.WheelPickerColumn
import com.stt.android.home.settings.wheel.WheelPickerData
import com.stt.android.home.settings.wheel.WheelPickerDataView
import java.util.Locale

@Composable
fun DurationDistanceWheelContent(
    title: String,
    data: DurationDistanceWheelFragmentData,
    onDoneClick: (distanceSelected: Boolean, indices: List<Int>) -> Unit,
    modifier: Modifier = Modifier,
    onDragging: (dragging: Boolean) -> Unit = {},
) {
    val distanceSelected = remember {
        mutableStateOf(data.distanceChecked)
    }

    val durationIndices = remember {
        data.durationColumns.map { mutableIntStateOf(it.defaultIndex) }
    }
    val distanceIndices = remember {
        data.distanceColumns.map { mutableIntStateOf(it.defaultIndex) }
    }
    val durationWheelData = remember {
        WheelPickerData(
            columns = data.durationColumns.mapIndexed { index, it ->
                WheelPickerColumn(
                    textList = it.textList,
                    defaultIndex = it.defaultIndex,
                    unit = it.unit,
                    onSelect = { i ->
                        durationIndices[index].intValue = i
                    }
                )
            },
            disableDoneStrategy = data.durationDisableDoneStrategy
        )
    }
    val distanceWheelData = remember {
        WheelPickerData(
            columns = data.distanceColumns.mapIndexed { index, it ->
                WheelPickerColumn(
                    textList = it.textList,
                    defaultIndex = it.defaultIndex,
                    unit = it.unit,
                    onSelect = { i ->
                        distanceIndices[index].intValue = i
                    }
                )
            },
            disableDoneStrategy = data.distanceDisableDoneStrategy
        )
    }

    Column(
        modifier = modifier
            .nestedScroll(rememberViewInteropNestedScrollConnection())
            .clip(MaterialTheme.shapes.bottomSheetShape)
            .background(MaterialTheme.colors.surface)
    ) {
        DraggableBottomSheetHandle()

        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .fillMaxSize()
        ) {
            Text(
                text = title.uppercase(),
                style = MaterialTheme.typography.bodyLargeBold,
                modifier = Modifier
                    .padding(vertical = MaterialTheme.spacing.medium)
                    .align(Alignment.CenterHorizontally)
            )

            Divider()

            RadioButtonItemView(item = RadioButtonItem(
                index = 0,
                title = stringResource(sportStr.autolap_switch_duration),
                checked = !distanceSelected.value,
                onCheck = { _, _, _ ->
                    if (distanceSelected.value) {
                        distanceSelected.value = false
                    }
                }
            ))
            if (!distanceSelected.value) {
                WheelPickerDataView(
                    data = durationWheelData,
                    modifier = Modifier.height(216.dp),
                    onDragging = onDragging,
                )
            }
            RadioButtonItemView(item = RadioButtonItem(
                index = 1,
                title = stringResource(sportStr.autolap_switch_distance),
                checked = distanceSelected.value,
                onCheck = { _, _, _ ->
                    if (!distanceSelected.value) {
                        distanceSelected.value = true
                    }
                }
            ))
            if (distanceSelected.value) {
                WheelPickerDataView(
                    data = distanceWheelData,
                    modifier = Modifier.height(216.dp),
                    onDragging = onDragging,
                )
            }

            PrimaryButton(
                text = stringResource(id = com.stt.android.R.string.done).uppercase(Locale.getDefault()),
                onClick = {
                    if (distanceSelected.value) {
                        onDoneClick(true, distanceIndices.map { it.intValue })
                    } else {
                        onDoneClick(false, durationIndices.map { it.intValue })
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        vertical = MaterialTheme.spacing.medium,
                        horizontal = MaterialTheme.spacing.large
                    )
            )
        }
    }
}

private val previewData = DurationDistanceWheelFragmentData(
    durationColumns = listOf(
        WheelFragmentColumn(
            textList = (0..59).map { "$it" },
            defaultIndex = 10,
            unit = ":"
        ),
        WheelFragmentColumn(
            textList = (0..59).map { "$it" },
            defaultIndex = 22,
            unit = "h"
        )
    ),
    distanceColumns = listOf(
        WheelFragmentColumn(
            textList = (0..100).map { "$it" },
            defaultIndex = 10,
            unit = "km"
        ),
        WheelFragmentColumn(
            textList = (0..100).map { ".$it" },
            defaultIndex = 10,
            unit = "km"
        )
    ),
)

@Preview
@Composable
private fun DurationDistanceWheelContentPreview1() {
    DurationDistanceWheelContent(
        title = "Reminds in",
        data = previewData,
        onDoneClick = { _, _ -> })
}

@Preview
@Composable
private fun DurationDistanceWheelContentPreview2() {
    DurationDistanceWheelContent(
        title = "Reminds in",
        data = previewData.copy(distanceChecked = true),
        onDoneClick = { _, _ -> })
}
