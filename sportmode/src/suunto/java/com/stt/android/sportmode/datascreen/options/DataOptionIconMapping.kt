package com.stt.android.sportmode.datascreen.options

import androidx.annotation.DrawableRes
import com.stt.android.infomodel.DataOptionMapping
import com.stt.android.infomodel.DataOptionMapping.*
import com.stt.android.sportmode.modesetting.sportDrawable

object DataOptionIconMapping {

    private const val HEART_RATE_GROUP_ID = "SPA"

    private const val PHRASE_DURATION = "DURATION"
    private const val PHRASE_ASCENT_TIME = "ASCENT_TIME"
    private const val PHRASE_DESCENT_TIME = "DESCENT_TIME"
    private const val PHRASE_GROUND_CONTACT = "GROUND_CONTACT"
    private const val PHRASE_DISTANCE = "DISTANCE"
    private const val PHRASE_SWIM_DISTANCE = "SWIM_DISTANCE"
    private const val PHRASE_DISTANCE_PER_STROKE = "DISTANCE_PER_STROKE"
    private const val PHRASE_AVG_STRIDE = "AVG_STRIDE"
    private const val PHRASE_ASCENT = "ASCENT"
    private const val PHRASE_DESCENT = "DESCENT"
    private const val PHRASE_ALTITUDE = "ALTITUDE"
    private const val PHRASE_VERTICAL_OSCILLATION = "VERTICAL_OSCILLATION"
    private const val PHRASE_PACE = "PACE"
    private const val PHRASE_GRADED_PACE = "GRADED_PACE"
    private const val PHRASE_AVG_SPEED = "AVG_SPEED"
    private const val PHRASE_MAX_SPEED = "MAX_SPEED"
    private const val PHRASE_ASCENT_SPEED = "ASCENT_SPEED"
    private const val PHRASE_DOWNHILL_SPEED = "DOWNHILL_SPEED"
    private const val PHRASE_DESCENT_SPEED = "DESCENT_SPEED"
    private const val PHRASE_CADENCE = "CADENCE"
    private const val PHRASE_ENERGY_EXPENDITURE = "ENERGY_EXPENDITURE"
    private const val PHRASE_STEPS = "STEPS"
    private const val PHRASE_JUMPS = "JUMPS"
    private const val PHRASE_SWIM_STROKE = "SWIM_STROKE"
    private const val PHRASE_ROWING_STROKE_RATE = "ROWING_STROKE_RATE"
    private const val PHRASE_ROWING_STROKES = "ROWING_STROKES"

    private const val POWER_GROUP_NAME = "TXT_POWER"

    private val mapping: Map<Int, List<DataOptionMapping>> = mapOf(
        // Heart rate/心肺类
        sportDrawable.data_screen_heart_rate to DataOptionMapping.entries.filter { it.groupId == HEART_RATE_GROUP_ID },

        // Time or duration/时间类
        sportDrawable.data_screen_duration to DataOptionMapping.entries.filter {
            it.valuePhrase.contains(PHRASE_DURATION)
        },
        sportDrawable.data_screen_ascent_duration to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_ASCENT_TIME)
        },
        sportDrawable.data_screen_descent_duration to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_DESCENT_TIME)
        },
        sportDrawable.data_screen_ground_contact to DataOptionMapping.entries.filter {
            it.valuePhrase.contains(PHRASE_GROUND_CONTACT)
        },
        sportDrawable.data_screen_time_of_day to listOf(TIMEOFDAY),

        // Distance/距离类
        sportDrawable.data_screen_distance to DataOptionMapping.entries.filter {
            it.valuePhrase.contains(PHRASE_DISTANCE)
                && !it.valuePhrase.contains(PHRASE_SWIM_DISTANCE)
                && !it.valuePhrase.contains(PHRASE_DISTANCE_PER_STROKE)
        },
        sportDrawable.data_screen_stride_length to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_AVG_STRIDE)
        },
        sportDrawable.data_screen_swim_distance to DataOptionMapping.entries.filter {
            it.valuePhrase.contains(PHRASE_SWIM_DISTANCE) || it.valuePhrase.contains(PHRASE_DISTANCE_PER_STROKE)
        },

        // Height/高度类
        sportDrawable.data_screen_ascent to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_ASCENT)
        },
        sportDrawable.data_screen_descent to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_DESCENT)
        },
        sportDrawable.data_screen_altitude to DataOptionMapping.entries.filter {
            it.valuePhrase.contains(PHRASE_ALTITUDE)
        },
        sportDrawable.data_screen_vertical_oscillation to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_VERTICAL_OSCILLATION)
        },

        // Speed&Pace/速度和配速
        sportDrawable.data_screen_pace to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_PACE) && !it.valuePhrase.endsWith(PHRASE_GRADED_PACE)
        },
        sportDrawable.data_screen_ngp to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_GRADED_PACE)
        },
        sportDrawable.data_screen_speed to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_AVG_SPEED) || it.valuePhrase.endsWith(PHRASE_MAX_SPEED)
        } + SPEED,
        sportDrawable.data_screen_vertical_speed to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_ASCENT_SPEED)
        },
        sportDrawable.data_screen_downhill_speed to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_DOWNHILL_SPEED)
        },
        sportDrawable.data_screen_descent_speed to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_DESCENT_SPEED)
        },

        // Cadence/节奏类
        sportDrawable.data_screen_cadence to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_CADENCE)
        },
        sportDrawable.data_screen_stroke_rate to DataOptionMapping.entries.filter {
            it.valuePhrase.contains(PHRASE_SWIM_STROKE)
                || it.valuePhrase.contains(PHRASE_ROWING_STROKES)
        },
        sportDrawable.data_screen_rowing_stroke_rate to DataOptionMapping.entries.filter {
            it.valuePhrase.contains(PHRASE_ROWING_STROKE_RATE)
        },

        // Physiology/生理类
        sportDrawable.data_screen_caloric to DataOptionMapping.entries.filter {
            it.valuePhrase.endsWith(PHRASE_ENERGY_EXPENDITURE)
        },
        sportDrawable.data_screen_fat_burn_percentage to listOf(FAT_OXIDATION_PERCENTAGE),
        sportDrawable.data_screen_tss to listOf(TRAINING_STRESS_SCORE),
        sportDrawable.data_screen_fat_amount to listOf(FAT_EXPENDITURE),
        sportDrawable.data_screen_fat_rate to listOf(FAT_EXPENDITURE_RATE),
        sportDrawable.data_screen_carb_amount to listOf(CARBOHYDRATE_EXPENDITURE),
        sportDrawable.data_screen_carb_rate to listOf(CARBOHYDRATE_EXPENDITURE_RATE),

        // Count/计数类
        sportDrawable.data_screen_steps to DataOptionMapping.entries.filter {
            it.valuePhrase.contains(PHRASE_STEPS)
        },
        sportDrawable.data_screen_jumps to DataOptionMapping.entries.filter {
            it.valuePhrase.contains(PHRASE_JUMPS)
        },

        // Environment/环境类
        sportDrawable.data_screen_temperature to listOf(TEMPERATURE),
        sportDrawable.data_screen_sunrise to listOf(SUNRISE),
        sportDrawable.data_screen_sunset to listOf(SUNSET),
        sportDrawable.data_screen_atmospheric to listOf(SEA_LEVEL_PRESSURE),

        // Power/功率类
        sportDrawable.data_screen_power to DataOptionMapping.entries.filter { it.groupName == POWER_GROUP_NAME },

        // Other/其他类
        sportDrawable.data_screen_battery to listOf(BATTERY),
        sportDrawable.data_screen_swolf to listOf(SWOLF, CURRENT_SEGMENT_SWOLF),
    )

    @DrawableRes
    fun getIconResByDataOption(dataOption: DataOptionMapping): Int {
        return mapping.entries.find { it.value.contains(dataOption) }?.key
            ?: sportDrawable.mode_setting_tips_icon
    }
}
