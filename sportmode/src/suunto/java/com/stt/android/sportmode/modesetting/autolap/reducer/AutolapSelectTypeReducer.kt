package com.stt.android.sportmode.modesetting.autolap.reducer

import com.stt.android.sportmode.modesetting.autolap.Autolap
import com.stt.android.sportmode.modesetting.autolap.AutolapReducer
import com.stt.android.sportmode.modesetting.autolap.AutolapType

class AutolapSelectTypeReducer(private val index: Int) : AutolapReducer {
    override suspend fun invoke(autolap: Autolap): Autolap {
        return autolap.copy(
            autolapTypes = autolap.autolapTypes.mapIndexed { index, autolapType ->
                if (index == <EMAIL>) {
                    when (autolapType) {
                        is AutolapType.Duration -> autolapType.copy(checked = true)
                        is AutolapType.Distance -> autolapType.copy(checked = true)
                        is AutolapType.Location -> autolapType.copy(checked = true)
                    }
                } else if (autolapType.checked) {
                    when (autolapType) {
                        is AutolapType.Duration -> autolapType.copy(checked = false)
                        is AutolapType.Distance -> autolapType.copy(checked = false)
                        is AutolapType.Location -> autolapType.copy(checked = false)
                    }
                } else {
                    autolapType
                }
            }
        )
    }
}
