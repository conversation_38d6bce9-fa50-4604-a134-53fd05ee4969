package com.stt.android.sportmode.trainingmode

import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.sportmode.entity.TrainingModeBase
import com.stt.android.sportmode.modesetting.intensitytarget.ZoneType
import com.stt.android.sportmode.modesetting.secondsPerHour
import com.stt.android.sportmode.modesetting.secondsPerMinute
import com.stt.android.sportmode.modesetting.secondsToMillis
import com.suunto.connectivity.runsportmodes.TrainingModeResponse
import com.suunto.connectivity.runsportmodes.entities.TrainingModeHeaderEntity
import kotlin.math.roundToInt

val TrainingModeResponse.isDefault: Boolean
    get() = TrainingModeBase.entries.find { it.defaultModeId == modeId } != null

val TrainingModeHeaderEntity.isDefault: Boolean
    get() = TrainingModeBase.entries.find { it.defaultModeId == modeId } != null

internal fun Float.formatInIntensityPace(measurementUnit: MeasurementUnit) = run {
    val speedMetersPerSecond = 1000.0 / this
    (measurementUnit.toPaceUnit(speedMetersPerSecond) * secondsPerMinute).toFloat().secondsToMillis
}

internal fun Float.formatInPace(measurementUnit: MeasurementUnit) = run {
    val speedMetersPerSecond = 1000.0 / this
    val paceInSeconds = measurementUnit.toPaceUnit(speedMetersPerSecond) * secondsPerMinute
    paceInSeconds.toFloat()
}

internal fun Float.formatInDistance(measurementUnit: MeasurementUnit) = run {
    (measurementUnit.toDistanceUnit(toDouble()) * 1000).toFloat()
}

internal fun Float.formatInAscent(measurementUnit: MeasurementUnit) = run {
    measurementUnit.toAltitudeUnit(this.toDouble()).toFloat()
}

internal fun Int.formatInSpeed(measurementUnit: MeasurementUnit) = run {
    val metersPerSecond = this * 1.0 / secondsPerHour
    val mph = measurementUnit.toSpeedUnit(metersPerSecond)
    (mph * 10).roundToInt()
}

internal fun Float.formatOutDistance(measurementUnit: MeasurementUnit) = run {
    val meters = measurementUnit.fromDistanceUnit(this / 1000.0).toFloat()
    meters / 1000
}

internal fun Float.formatOutAscent(measurementUnit: MeasurementUnit) = run {
    measurementUnit.fromAltitudeUnit(toDouble()).toFloat()
}

internal fun Int.formatOutPace(measurementUnit: MeasurementUnit) = run {
    (this * measurementUnit.toDistanceUnit(1000.0)).roundToInt()
}

internal fun Int.formatOutZoneTypeValue(zoneType: ZoneType?, measurementUnit: MeasurementUnit) = run {
    when (zoneType) {
        is ZoneType.Pace -> {
            this.formatOutPace(measurementUnit)
        }
        is ZoneType.Speed -> { // 1 meters / h
            val ms = measurementUnit.fromSpeedUnit(this / 10.0)
            (ms * secondsPerHour).roundToInt()
        }
        else -> this
    }
}

internal fun Int.getWheelPickerIndex(range: List<Int>): Int {
    if (range.isEmpty()) return 0

    var closestIndex = 0
    var minDiff = Int.MAX_VALUE

    for ((index, value) in range.withIndex()) {
        val diff = kotlin.math.abs(this - value)
        if (diff < minDiff) {
            minDiff = diff
            closestIndex = index
        }
    }
    return closestIndex
}
